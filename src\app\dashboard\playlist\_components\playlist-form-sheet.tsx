"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Sheet,
  Sheet<PERSON>ontent,
  <PERSON>etDescription,
  She<PERSON><PERSON><PERSON>er,
  <PERSON>etHeader,
  SheetTitle,
} from "@/components/ui/sheet"
import { useCreateMusicPlaylistUser, useUpdateMusicPlaylistUser, GetMusicPlaylistsUser_ResponseTypeSuccess } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { toast } from "sonner"
import { Loader2, Music2, FileText, Sparkles, CheckCircle2, AlertCircle, Image as ImageIcon, Check } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import Image from "next/image"
import { MUSIC_IMAGE_PRESETS } from "@/config/image-presets"

interface PlaylistFormSheetProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  playlist?: GetMusicPlaylistsUser_ResponseTypeSuccess[0]
}

interface FormData {
  name: string
  description: string
  imageUrl: string
}

interface TouchedState {
  name?: boolean
  description?: boolean
  imageUrl?: boolean
}

export function PlaylistFormSheet({ isOpen, onOpenChange, playlist }: PlaylistFormSheetProps) {
  const createMusicPlaylist = useCreateMusicPlaylistUser()
  const updateMusicPlaylist = useUpdateMusicPlaylistUser()
  
  const [formData, setFormData] = useState<FormData>({
    name: "",
    description: "",
    imageUrl: ""
  })

  const [errors, setErrors] = useState<Partial<FormData>>({})
  const [touched, setTouched] = useState<TouchedState>({})

  const isEditing = !!playlist
  const isLoading = createMusicPlaylist.isPending || updateMusicPlaylist.isPending

  // Initialize form data when playlist changes
  useEffect(() => {
    if (playlist) {
      setFormData({
        name: playlist.name || "",
        description: playlist.description || "",
        imageUrl: (playlist as any).imageUrl || ""
      })
    } else {
      setFormData({
        name: "",
        description: "",
        imageUrl: ""
      })
    }
    setErrors({})
    setTouched({})
  }, [playlist, isOpen])

  const validateField = (field: keyof FormData, value: string): string | undefined => {
    switch (field) {
      case "name":
        if (!value.trim()) {
          return "Playlist name is required"
        } else if (value.trim().length < 2) {
          return "Name must be at least 2 characters"
        } else if (value.trim().length > 100) {
          return "Name must not exceed 100 characters"
        }
        break
      case "description":
        if (value && value.length > 500) {
          return "Description must not exceed 500 characters"
        }
        break
      case "imageUrl":
        if (value && value.trim()) {
          try {
            new URL(value)
          } catch {
            return "Please enter a valid URL"
          }
        }
        break
    }
    return undefined
  }

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {}

    Object.keys(formData).forEach((key) => {
      const field = key as keyof FormData
      const error = validateField(field, formData[field])
      if (error) {
        newErrors[field] = error
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      if (isEditing && playlist) {
        await updateMusicPlaylist.mutateAsync({
          form: {
            name: formData.name.trim(),
            ...(formData.description.trim() && { description: formData.description.trim() }),
            ...(formData.imageUrl.trim() && { imageUrl: formData.imageUrl.trim() })
          },
          param: { id: playlist.id }
        })
        toast.success("Playlist updated successfully")
      } else {
        await createMusicPlaylist.mutateAsync({
          form: {
            name: formData.name.trim(),
            ...(formData.description.trim() && { description: formData.description.trim() }),
            ...(formData.imageUrl.trim() && { imageUrl: formData.imageUrl.trim() })
          }
        })
        toast.success("Playlist created successfully")
      }
      onOpenChange(false)
    } catch (error) {
      console.error("Error saving playlist:", error)
      toast.error(
        isEditing ? "Failed to update playlist" : "Failed to create playlist"
      )
    }
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Real-time validation
    if (touched[field]) {
      const error = validateField(field, value)
      setErrors(prev => ({ ...prev, [field]: error }))
    }
  }

  const handleBlur = (field: keyof FormData) => {
    setTouched(prev => ({ ...prev, [field]: true }))
    const error = validateField(field, formData[field])
    setErrors(prev => ({ ...prev, [field]: error }))
  }

  const getFieldStatus = (field: keyof FormData) => {
    if (errors[field]) return "error"
    if (touched[field as keyof TouchedState] && formData[field]) return "success"
    return "default"
  }

  const isFormValid = !Object.values(errors).some(Boolean) && formData.name.trim()

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="flex flex-col h-full sm:max-w-md"
        onCloseAutoFocus={(event) => {
            event.preventDefault();
            document.body.style.pointerEvents = '';
          }}
      >
        {/* Fixed Header */}
        <SheetHeader className="pb-4 px-4 border-b flex-shrink-0">
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-lg bg-gradient-to-br from-orange-500/10 to-rose-500/10 border border-orange-200/20 dark:border-orange-700/20">
              <Music2 className="h-4 w-4 text-orange-600 dark:text-orange-400" />
            </div>
            <div className="space-y-0.5">
              <SheetTitle className="text-lg font-semibold">
                {isEditing ? "Edit Playlist" : "Create Playlist"}
              </SheetTitle>
              <SheetDescription className="text-xs">
                {isEditing
                  ? "Update playlist details"
                  : "Create a personalized music collection"}
              </SheetDescription>
            </div>
          </div>
          {isEditing && (
            <Badge variant="secondary" className="w-fit mt-1 text-xs">
              Editing: {playlist?.name}
            </Badge>
          )}
        </SheetHeader>

        <form onSubmit={handleSubmit} className="flex flex-col flex-1 min-h-0">
          {/* Scrollable Form Content */}
          <div
            className="flex-1 px-4 h-[calc(100vh-200px)] min-h-[400px] max-h-[calc(100vh-180px)] playlist-form-scroll overflow-y-auto"
            style={{
              WebkitOverflowScrolling: 'touch',
              touchAction: 'pan-y',
              overscrollBehavior: 'contain'
            }}
          >
            <div className="py-4 space-y-4">
            {/* Playlist Name Field */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Music2 className="h-3.5 w-3.5 text-muted-foreground" />
                <Label htmlFor="name" className="text-sm font-medium">
                  Playlist Name
                </Label>
                <span className="text-destructive text-xs">*</span>
              </div>
              
              <div className="relative">
                <Input
                  id="name"
                  placeholder="Enter playlist name..."
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  onBlur={() => handleBlur("name")}
                  className={cn(
                    "h-9 text-sm transition-all duration-200",
                    getFieldStatus("name") === "error" && "border-destructive ring-destructive/20",
                    getFieldStatus("name") === "success" && "border-green-500 ring-green-500/20"
                  )}
                  maxLength={100}
                />
                
                <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
                  <AnimatePresence mode="wait">
                    {getFieldStatus("name") === "error" && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                      >
                        <AlertCircle className="h-3.5 w-3.5 text-destructive" />
                      </motion.div>
                    )}
                    {getFieldStatus("name") === "success" && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                      >
                        <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
                      </motion.div>
                    )}
                  </AnimatePresence>
                  
                  <span className="text-xs text-muted-foreground ml-1">
                    {formData.name.length}/100
                  </span>
                </div>
              </div>
              
              <AnimatePresence>
                {errors.name && (
                  <motion.p
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -5 }}
                    className="text-xs text-destructive flex items-center gap-1"
                  >
                    <AlertCircle className="h-3 w-3" />
                    {errors.name}
                  </motion.p>
                )}
              </AnimatePresence>
            </div>

            {/* Description Field */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <FileText className="h-3.5 w-3.5 text-muted-foreground" />
                <Label htmlFor="description" className="text-sm font-medium">
                  Description
                </Label>
                <span className="text-xs text-muted-foreground">(optional)</span>
              </div>
              
              <div className="relative">
                <Textarea
                  id="description"
                  placeholder="Describe your playlist..."
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  onBlur={() => handleBlur("description")}
                  className={cn(
                    "min-h-[80px] max-h-[120px] text-sm resize-none transition-all duration-200",
                    getFieldStatus("description") === "error" && "border-destructive ring-destructive/20",
                    getFieldStatus("description") === "success" && "border-green-500 ring-green-500/20"
                  )}
                  maxLength={500}
                />
                
                <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
                  {formData.description.length}/500
                </div>
              </div>
              
              <AnimatePresence>
                {errors.description && (
                  <motion.p
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -5 }}
                    className="text-xs text-destructive flex items-center gap-1"
                  >
                    <AlertCircle className="h-3 w-3" />
                    {errors.description}
                  </motion.p>
                )}
              </AnimatePresence>
            </div>

            {/* Cover Image URL Field */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <ImageIcon className="h-3.5 w-3.5 text-muted-foreground" />
                <Label htmlFor="imageUrl" className="text-sm font-medium">
                  Cover Image URL
                </Label>
                <span className="text-xs text-muted-foreground">(optional)</span>
              </div>

              <div className="relative">
                <Input
                  id="imageUrl"
                  placeholder="https://example.com/image.jpg"
                  value={formData.imageUrl}
                  onChange={(e) => handleInputChange("imageUrl", e.target.value)}
                  onBlur={() => handleBlur("imageUrl")}
                  className={cn(
                    "h-9 text-sm pl-9 transition-all duration-200",
                    getFieldStatus("imageUrl") === "error" && "border-destructive ring-destructive/20",
                    getFieldStatus("imageUrl") === "success" && "border-green-500 ring-green-500/20"
                  )}
                />
                <ImageIcon className="absolute left-2.5 top-1/2 -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground" />

                <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
                  <AnimatePresence mode="wait">
                    {getFieldStatus("imageUrl") === "error" && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                      >
                        <AlertCircle className="h-3.5 w-3.5 text-destructive" />
                      </motion.div>
                    )}
                    {getFieldStatus("imageUrl") === "success" && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                      >
                        <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>

              <AnimatePresence>
                {errors.imageUrl && (
                  <motion.p
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -5 }}
                    className="text-xs text-destructive flex items-center gap-1"
                  >
                    <AlertCircle className="h-3 w-3" />
                    {errors.imageUrl}
                  </motion.p>
                )}
              </AnimatePresence>

              {/* Image Presets */}
              <div className="space-y-2">
                <Label className="text-xs font-medium text-muted-foreground">
                  Or choose from presets:
                </Label>
                <div className="grid grid-cols-3 gap-1.5">
                  {MUSIC_IMAGE_PRESETS.map((preset) => (
                    <motion.button
                      key={preset.id}
                      type="button"
                      onClick={() => {
                        handleInputChange("imageUrl", preset.url)
                        setTouched(prev => ({ ...prev, imageUrl: true }))
                      }}
                      className={cn(
                        "relative aspect-video rounded-md border-2 overflow-hidden transition-all duration-200",
                        formData.imageUrl === preset.url
                          ? "border-orange-500 ring-2 ring-orange-500/20"
                          : "border-border hover:border-orange-300"
                      )}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Image
                        src={preset.url}
                        alt={preset.alt}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 33vw, 120px"
                      />
                      {formData.imageUrl === preset.url && (
                        <div className="absolute inset-0 bg-orange-500/20 flex items-center justify-center">
                          <div className="bg-orange-500 text-white rounded-full p-0.5">
                            <Check className="h-2.5 w-2.5" />
                          </div>
                        </div>
                      )}
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Image Preview */}
              {/* {formData.imageUrl && !errors.imageUrl && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className="overflow-hidden"
                >
                  <div className="relative aspect-video rounded-md border overflow-hidden bg-muted/30">
                    <Image
                      src={formData.imageUrl}
                      alt="Preview"
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, 400px"
                      onError={() => {
                        setErrors(prev => ({ ...prev, imageUrl: "Invalid image URL" }))
                      }}
                    />
                    <div className="absolute bottom-1 right-1 bg-background/80 backdrop-blur-sm text-xs px-1.5 py-0.5 rounded">
                      Preview
                    </div>
                  </div>
                </motion.div>
              )} */}
            </div>

            {/* Tips Section */}
            <div className="p-3 rounded-lg bg-muted/50 border border-border/50">
              <div className="flex items-start gap-2">
                <Sparkles className="h-3.5 w-3.5 text-orange-500 mt-0.5 shrink-0" />
                <div className="space-y-1">
                  <p className="text-xs font-medium text-foreground">
                    Pro Tips
                  </p>
                  <ul className="text-xs text-muted-foreground space-y-0.5">
                    <li>• Use descriptive names for easy searching</li>
                    <li>• Add descriptions to remember the mood or purpose</li>
                    <li>• You can always edit these details later</li>
                  </ul>
                </div>
              </div>
            </div>
            </div>
          </div>

          {/* Fixed Footer */}
          <SheetFooter className="px-4 py-3 border-t bg-muted/20 flex-shrink-0">
            <div className="flex gap-2 w-full">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="flex-1 h-9 text-sm"
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={!isFormValid || isLoading}
                className="flex-1 h-9 text-sm bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 hover:from-orange-600 hover:via-red-600 hover:to-rose-700 text-white transition-all duration-200"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-3.5 w-3.5 animate-spin" />
                    {isEditing ? "Updating..." : "Creating..."}
                  </>
                ) : (
                  <>
                    {isEditing ? "Update Playlist" : "Create Playlist"}
                  </>
                )}
              </Button>
            </div>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  )
} 