import { z } from "zod";
import { PomodoroSessionSchema, PomodoroSessionPartialSchema } from "../../zod-auto-types/modelSchema/PomodoroSessionSchema";
import { PomodoroTypeSchema } from "../../zod-auto-types/inputTypeSchemas/PomodoroTypeSchema";

// Minimum duration constants (in seconds)
export const MINIMUM_FOCUS_DURATION_SECONDS = 60; // 5 minutes for focus sessions
export const MINIMUM_BREAK_DURATION_SECONDS = 60; // 1 minute for break sessions

// Define the type for interrupted session
export const InterruptedSessionSchema = z.object({
  startTime: z.union([z.date(), z.string().datetime()]),
  endTime: z.union([z.date(), z.string().datetime()]),
});

export type InterruptedSession = z.infer<typeof InterruptedSessionSchema>;

export const createPomodoroSessionSchema = PomodoroSessionSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
}).extend({
  startTime: z.union([
    z.date(),
    z.string().datetime(),
  ]).optional().default(() => new Date()),
  endTime: z.union([
    z.date(),
    z.string().datetime(),
  ]).optional().nullable(),
  totalDuration: z.union([
    z.number().int().min(0),
    z.string().transform(val => parseInt(val, 10))
  ]),
  focusDuration: z.union([
    z.number().int().min(0),
    z.string().transform(val => parseInt(val, 10))
  ]).optional().nullable(),
  breakDuration: z.union([
    z.number().int().min(0),
    z.string().transform(val => parseInt(val, 10))
  ]).optional().nullable(),
  completed: z.union([
    z.boolean(),
    z.string().transform(val => val === "true"),
  ]).optional().default(false),
  interrupted: z.union([
    z.boolean(),
    z.string().transform(val => val === "true"),
  ]).optional().default(false),
  interruptedSessions: z.union([
    z.array(InterruptedSessionSchema),
    z.string().transform((val) => {
      if (val === "undefined" || val === "null" || val === "") {
        return null;
      }
      try {
        const parsed = JSON.parse(val);
        if (Array.isArray(parsed)) {
          return parsed.filter(session => session && session.startTime && session.endTime);
        }
        return null;
      } catch {
        return null;
      }
    }),
  ]).optional().nullable(),
  note: z.string().optional().nullable(),
  intervalType: PomodoroTypeSchema.optional().default("FOCUS"),
  taskId: z.string().optional().transform((val) => {
    // Transform string values of "undefined", "null", empty string to actual null
    if (val === undefined || val === null || val === "undefined" || val === "null" || val === "") {
      return null;
    }
    return val;
  }).nullable(),
}).refine((data) => {
  // Get the actual duration to validate based on session type
  const getDurationToValidate = () => {
    if (data.intervalType === "FOCUS") {
      return data.focusDuration ?? data.totalDuration;
    } else if (data.intervalType === "SHORT_BREAK" || data.intervalType === "LONG_BREAK") {
      return data.breakDuration ?? data.totalDuration;
    }
    return data.totalDuration;
  };

  const duration = getDurationToValidate();
  
  // Convert string duration to number if needed
  const durationSeconds = typeof duration === 'string' ? parseInt(duration, 10) : duration;
  
  // Focus sessions must be at least the minimum duration
  if (data.intervalType === "FOCUS") {
    return durationSeconds >= MINIMUM_FOCUS_DURATION_SECONDS;
  }
  
  // Break sessions must be at least the minimum duration
  if (data.intervalType === "SHORT_BREAK" || data.intervalType === "LONG_BREAK") {
    return durationSeconds >= MINIMUM_BREAK_DURATION_SECONDS;
  }
  
  return true;
}, {
  message: `Focus sessions must be at least ${MINIMUM_FOCUS_DURATION_SECONDS / 60} minutes, break sessions must be at least ${MINIMUM_BREAK_DURATION_SECONDS / 60} minute${MINIMUM_BREAK_DURATION_SECONDS > 60 ? 's' : ''}`,
  path: ["totalDuration"]
});

export const updatePomodoroSessionSchema = PomodoroSessionPartialSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
}).extend({
  endTime: z.union([
    z.date(),
    z.string().datetime(),
  ]).optional().nullable(),
  focusDuration: z.union([
    z.number().int().min(0),
    z.string().transform(val => parseInt(val, 10))
  ]).optional().nullable(),
  breakDuration: z.union([
    z.number().int().min(0),
    z.string().transform(val => parseInt(val, 10))
  ]).optional().nullable(),
  completed: z.union([
    z.boolean(),
    z.string().transform(val => val === "true"),
  ]).optional(),
  interrupted: z.union([
    z.boolean(),
    z.string().transform(val => val === "true"),
  ]).optional(),
  interruptedSessions: z.union([
    z.array(InterruptedSessionSchema),
    z.string().transform((val) => {
      if (val === "undefined" || val === "null" || val === "") {
        return null;
      }
      try {
        const parsed = JSON.parse(val);
        if (Array.isArray(parsed)) {
          return parsed.filter(session => session && session.startTime && session.endTime);
        }
        return null;
      } catch {
        return null;
      }
    }),
  ]).optional().nullable(),
  note: z.string().optional().nullable(),
  taskId: z.string().optional().transform((val) => {
    // Transform string values of "undefined", "null", empty string to actual null
    if (val === undefined || val === null || val === "undefined" || val === "null" || val === "") {
      return null;
    }
    return val;
  }).nullable(),
}).refine((data) => {
  // Only validate if both intervalType and duration are provided in the update
  if (!data.intervalType) {
    return true; // Skip validation if intervalType is not being updated
  }

  // Get the actual duration to validate based on session type
  const getDurationToValidate = () => {
    if (data.intervalType === "FOCUS") {
      return data.focusDuration ?? data.totalDuration;
    } else if (data.intervalType === "SHORT_BREAK" || data.intervalType === "LONG_BREAK") {
      return data.breakDuration ?? data.totalDuration;
    }
    return data.totalDuration;
  };

  const duration = getDurationToValidate();
  
  // Skip validation if no duration is being updated
  if (duration === undefined) {
    return true;
  }
  
  // Convert string duration to number if needed
  const durationSeconds = typeof duration === 'string' ? parseInt(duration, 10) : duration;
  
  // Focus sessions must be at least the minimum duration
  if (data.intervalType === "FOCUS") {
    return durationSeconds >= MINIMUM_FOCUS_DURATION_SECONDS;
  }
  
  // Break sessions must be at least the minimum duration
  if (data.intervalType === "SHORT_BREAK" || data.intervalType === "LONG_BREAK") {
    return durationSeconds >= MINIMUM_BREAK_DURATION_SECONDS;
  }
  
  return true;
}, {
  message: `Focus sessions must be at least ${MINIMUM_FOCUS_DURATION_SECONDS / 60} minutes, break sessions must be at least ${MINIMUM_BREAK_DURATION_SECONDS / 60} minute${MINIMUM_BREAK_DURATION_SECONDS > 60 ? 's' : ''}`,
  path: ["totalDuration"]
});

export type CreatePomodoroSessionInput = z.infer<typeof createPomodoroSessionSchema>;
export type UpdatePomodoroSessionInput = z.infer<typeof updatePomodoroSessionSchema>;

// PomodoroSession type with all fields
export type PomodoroSession = {
  id: string;
  startTime: Date;
  endTime: Date | null;
  totalDuration: number;
  focusDuration: number | null;
  breakDuration: number | null;
  completed: boolean;
  interrupted: boolean;
  interruptedSessions: InterruptedSession[] | null;
  note: string | null;
  intervalType: "FOCUS" | "SHORT_BREAK" | "LONG_BREAK";
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  taskId: string | null;
}; 