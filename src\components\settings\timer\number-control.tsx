'use client';

import { useState, useEffect, useRef, ChangeEvent, KeyboardEvent } from 'react';
import { Minus, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

type NumberControlColor = "blue" | "emerald" | "purple" | "indigo";

interface NumberControlProps {
  value: number;
  onValueChange: (value: number) => void;
  min: number;
  max: number;
  color?: NumberControlColor;
  ariaLabel?: string;
}

const colorClasses = {
  blue: {
    base: "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800 focus-within:border-blue-400 dark:focus-within:border-blue-600",
    button: "text-blue-500 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 hover:bg-blue-100/70 dark:hover:bg-blue-800/40 focus:bg-blue-100 dark:focus:bg-blue-800/60"
  },
  emerald: {
    base: "bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800 focus-within:border-emerald-400 dark:focus-within:border-emerald-600",
    button: "text-emerald-500 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300 hover:bg-emerald-100/70 dark:hover:bg-emerald-800/40 focus:bg-emerald-100 dark:focus:bg-emerald-800/60"
  },
  purple: {
    base: "bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-800 focus-within:border-purple-400 dark:focus-within:border-purple-600",
    button: "text-purple-500 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 hover:bg-purple-100/70 dark:hover:bg-purple-800/40 focus:bg-purple-100 dark:focus:bg-purple-800/60"
  },
  indigo: {
    base: "bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300 border-indigo-200 dark:border-indigo-800 focus-within:border-indigo-400 dark:focus-within:border-indigo-600",
    button: "text-indigo-500 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 hover:bg-indigo-100/70 dark:hover:bg-indigo-800/40 focus:bg-indigo-100 dark:focus:bg-indigo-800/60"
  },
};

export function NumberControl({
  value,
  onValueChange,
  min,
  max,
  color = "blue",
  ariaLabel
}: NumberControlProps) {
  // State to track the visual value before sending updates
  const [localValue, setLocalValue] = useState(value);
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Update local value when external value changes
  useEffect(() => {
    setLocalValue(value);
  }, [value]);
  
  // Reference to the update timeout
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Commit the change with debounce
  const commitChange = (newValue: number) => {
    // Ensure value is within bounds
    const boundedValue = Math.max(min, Math.min(max, newValue));
    
    // Update local state immediately
    setLocalValue(boundedValue);
    
    // Clear any pending timeouts
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }
    
    // Set a timeout to commit the change
    updateTimeoutRef.current = setTimeout(() => {
      onValueChange(boundedValue);
    }, 300); // 300ms debounce
  };

  const increment = () => {
    if (localValue < max) commitChange(localValue + 1);
  };
  
  const decrement = () => {
    if (localValue > min) commitChange(localValue - 1);
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    
    // Only allow numeric input
    if (value === '' || /^[0-9]+$/.test(value)) {
      const parsed = value === '' ? min : parseInt(value, 10);
      setLocalValue(parsed);
    }
  };

  const handleInputBlur = () => {
    // Ensure value is within bounds on blur
    commitChange(localValue);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      inputRef.current?.blur();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      setLocalValue(value); // Reset to prop value
      inputRef.current?.blur();
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      increment();
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      decrement();
    }
  };
  
  return (
    <motion.div 
      className={cn(
        "flex items-center h-8 rounded-md border overflow-hidden transition-colors shadow-sm",
        colorClasses[color].base
      )}
      initial={{ opacity: 0, y: 2 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.15 }}
    >
      <motion.button 
        type="button"
        onClick={decrement}
        disabled={localValue <= min}
        className={cn(
          "flex items-center justify-center w-7 h-full transition-colors",
          colorClasses[color].button,
          localValue <= min && "opacity-40 cursor-not-allowed"
        )}
        whileTap={{ scale: 0.9 }}
        aria-label={`Decrease ${ariaLabel || 'value'}`}
      >
        <Minus className="h-3 w-3" />
      </motion.button>
      
      <div className="relative h-full min-w-[2.5rem] flex items-center justify-center">
        <input
          ref={inputRef}
          type="text"
          inputMode="numeric"
          pattern="[0-9]*"
          value={localValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          onKeyDown={handleKeyDown}
          className="absolute inset-0 w-full h-full bg-transparent text-center px-1 font-medium text-sm focus:outline-none"
          aria-label={`Edit ${ariaLabel || 'value'}`}
        />
      </div>
      
      <motion.button 
        type="button"
        onClick={increment}
        disabled={localValue >= max}
        className={cn(
          "flex items-center justify-center w-7 h-full transition-colors",
          colorClasses[color].button,
          localValue >= max && "opacity-40 cursor-not-allowed"
        )}
        whileTap={{ scale: 0.9 }}
        aria-label={`Increase ${ariaLabel || 'value'}`}
      >
        <Plus className="h-3 w-3" />
      </motion.button>
    </motion.div>
  );
} 