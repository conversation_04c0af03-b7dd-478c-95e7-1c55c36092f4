import { <PERSON>o } from "hono";
import { zValidator } from "@hono/zod-validator";
import { createNaturePlaylistSchema, updateNaturePlaylistSchema } from "./nature-playlist-type";
import prisma from "@/lib/prisma";
import { UserVariable } from "../..";
import { UserRole } from "@prisma/client";
import { z } from "zod";
import { privateRoutesMiddleware } from "@/server/private/middleware";

const app = new Hono<{ Variables: UserVariable }>()
  // Get all nature playlists
  .get("/", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const isPublic = c.req.query("isPublic") === "true" ? true : c.req.query("isPublic") === "false" ? false : undefined;
    const isDefault = c.req.query("isDefault") === "true" ? true : c.req.query("isDefault") === "false" ? false : undefined;

    console.log({
      isPublic,
      isDefault
    });

    const filters: Record<string, unknown> = {};

    // Add filters based on query parameters
    if (isPublic !== undefined) {
      filters.isPublic = isPublic;
    }

    if (isDefault !== undefined) {
      filters.isDefault = isDefault;
    }

    // Create conditions for access control
    // User can access: their own playlists OR public playlists OR admin playlists (if user is not admin, exclude admin-created private playlists)
    const accessConditions = user.role === UserRole.ADMIN 
      ? [
          { userId: user.id },
          { isPublic: true },
          { creatorType: UserRole.ADMIN }
        ]
      : [
          { userId: user.id }, // User's own playlists
          { isPublic: true, creatorType: UserRole.ADMIN }, // Public admin playlists only
        ];

    const naturePlaylists = await prisma.naturePlaylist.findMany({
      where: {
        ...filters,
        OR: accessConditions
      },
      include: {
        videos: {
          select: {
            id: true,
            title: true,
            thumbnail: true
          }
        },
        natureSounds: {
          select: {
            id: true,
            title: true,
          }
        }
      },
    });

    return c.json({ data: naturePlaylists });
  })

  // Get single nature playlist
  .get("/:id", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");

    const naturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id },
      include: {
        videos: true,
        natureSounds: true
      },
    });

    if (!naturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    // Check if user can access this playlist
    const canAccess = user.role === UserRole.ADMIN 
      ? true // Admins can access everything
      : (
          naturePlaylist.userId === user.id || // User owns the playlist
          (naturePlaylist.isPublic && naturePlaylist.creatorType === UserRole.ADMIN) // Public admin playlist
        );

    if (!canAccess) {
      return c.json({ error: "Unauthorized" }, 403);
    }

    return c.json({ data: naturePlaylist });
  })

  // Create nature playlist - allow users to create their own playlists
  .post("/", privateRoutesMiddleware, zValidator("form", createNaturePlaylistSchema), async (c) => {
    const user = c.get("user");
    const { name, description, isPublic, imageUrl, videoIds, natureSoundIds } = c.req.valid("form");

    // Create relationships with videos and nature sounds if provided
    const connectVideos = videoIds && videoIds.length > 0 
      ? { connect: videoIds.map(id => ({ id })) } 
      : undefined;
      
    const connectNatureSounds = natureSoundIds && natureSoundIds.length > 0 
      ? { connect: natureSoundIds.map(id => ({ id })) } 
      : undefined;

    // Create nature playlist in database
    const naturePlaylist = await prisma.naturePlaylist.create({
      data: {
        name,
        description,
        isPublic: isPublic ?? false,
        imageUrl: imageUrl || "",
        userId: user.id,
        creatorType: user.role as UserRole, // Set creatorType based on user's role
        videos: connectVideos,
        natureSounds: connectNatureSounds
      },
      include: {
        videos: true,
        natureSounds: true
      }
    });

    return c.json({ data: naturePlaylist });
  })

  // Update nature playlist - users can update their own, admins can update any, but users cannot update admin-created content
  .patch("/:id", privateRoutesMiddleware, zValidator("form", updateNaturePlaylistSchema), async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");
    const updates = c.req.valid("form");

    // First check if nature playlist exists
    const existingNaturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id },
    });

    if (!existingNaturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    // Security check: 
    // - Admins can update any playlist
    // - Users can only update their own playlists (not admin-created ones)
    const canUpdate = user.role === UserRole.ADMIN 
      ? true
      : (existingNaturePlaylist.userId === user.id && existingNaturePlaylist.creatorType !== UserRole.ADMIN);

    if (!canUpdate) {
      return c.json({ error: "Unauthorized to update this nature playlist" }, 403);
    }

    // Prepare update data with proper typing
    const updateData = {
      ...(updates.name !== undefined && { name: updates.name }),
      ...(updates.description !== undefined && { description: updates.description }),
      ...(updates.isPublic !== undefined && { isPublic: updates.isPublic }),
      ...(updates.imageUrl !== undefined && { imageUrl: updates.imageUrl }),
    };

    // Handle video and nature sound connections/disconnections
    let relationshipUpdates = {};
    
    // Videos
    if (updates.videoIds !== undefined) {
      relationshipUpdates = {
        ...relationshipUpdates,
        videos: {
          ...(updates.videoIds.length > 0 
            ? { set: updates.videoIds.map(id => ({ id })) }
            : { set: [] }) // Empty array to disconnect all
        }
      };
    }
    
    // Nature sounds
    if (updates.natureSoundIds !== undefined) {
      relationshipUpdates = {
        ...relationshipUpdates,
        natureSounds: {
          ...(updates.natureSoundIds.length > 0 
            ? { set: updates.natureSoundIds.map(id => ({ id })) }
            : { set: [] })
        }
      };
    }

    // Update the nature playlist
    const updatedNaturePlaylist = await prisma.naturePlaylist.update({
      where: { id },
      data: {
        ...updateData,
        ...relationshipUpdates
      },
      include: {
        videos: true,
        natureSounds: true
      }
    });

    return c.json({ data: updatedNaturePlaylist });
  })

  // Delete nature playlist - users can delete their own, admins can delete any, but users cannot delete admin-created content
  .delete("/:id", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");

    // Find the nature playlist
    const naturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id },
    });

    if (!naturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    // Security check:
    // - Admins can delete any playlist (except default ones)
    // - Users can only delete their own playlists (not admin-created ones)
    const canDelete = user.role === UserRole.ADMIN 
      ? true
      : (naturePlaylist.userId === user.id && naturePlaylist.creatorType !== UserRole.ADMIN);

    if (!canDelete) {
      return c.json({ error: "Unauthorized to delete this nature playlist" }, 403);
    }

    // Check if this is a default playlist which shouldn't be deleted
    if (naturePlaylist.isDefault) {
      return c.json({ error: "Cannot delete a default nature playlist" }, 403);
    }

    // Delete nature playlist
    await prisma.naturePlaylist.delete({
      where: { id },
    });

    return c.json({ success: true });
  })

  // Nature Sounds Management - users can manage their own playlists, admins can manage any
  .post("/:id/nature-sounds", privateRoutesMiddleware, zValidator("json", z.object({
    natureSoundIds: z.array(z.string())
  })), async (c) => {
    const user = c.get("user");
    const naturePlaylistId = c.req.param("id");
    const { natureSoundIds } = c.req.valid("json");

    // Check nature playlist exists
    const naturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id: naturePlaylistId },
    });

    if (!naturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    // Security check
    const canManage = user.role === UserRole.ADMIN 
      ? true
      : (naturePlaylist.userId === user.id && naturePlaylist.creatorType !== UserRole.ADMIN);

    if (!canManage) {
      return c.json({ error: "Unauthorized to manage nature sounds in this playlist" }, 403);
    }

    // Get current natureSoundOrder
    const currentNatureSoundOrder = naturePlaylist.natureSoundOrder || [];

    // Add new nature sound IDs to the end of the order
    const newNatureSoundOrder = [...currentNatureSoundOrder, ...natureSoundIds];

    // Add nature sound items to playlist and update natureSoundOrder
    const updatedNaturePlaylist = await prisma.naturePlaylist.update({
      where: { id: naturePlaylistId },
      data: {
        natureSounds: {
          connect: natureSoundIds.map(id => ({ id }))
        },
        natureSoundOrder: newNatureSoundOrder
      },
      include: {
        natureSounds: true
      }
    });

    return c.json({ data: updatedNaturePlaylist });
  })

  // Reorder nature sounds in playlist - users can reorder their own playlists
  .patch("/:id/nature-sounds/reorder", privateRoutesMiddleware, zValidator("json", z.object({
    natureSoundOrder: z.array(z.string())
  })), async (c) => {
    const user = c.get("user");
    const naturePlaylistId = c.req.param("id");
    const { natureSoundOrder } = c.req.valid("json");

    // Check nature playlist exists
    const naturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id: naturePlaylistId },
      include: {
        natureSounds: {
          select: { id: true }
        }
      }
    });

    if (!naturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    // Security check
    const canReorder = user.role === UserRole.ADMIN 
      ? true
      : (naturePlaylist.userId === user.id && naturePlaylist.creatorType !== UserRole.ADMIN);

    if (!canReorder) {
      return c.json({ error: "Unauthorized to reorder nature sounds in this playlist" }, 403);
    }

    // Validate that all nature sound IDs in the new order exist in the playlist
    const naturePlaylistSoundIds = new Set(naturePlaylist.natureSounds.map(ns => ns.id));
    const hasInvalidIds = natureSoundOrder.some(id => !naturePlaylistSoundIds.has(id));
    
    if (hasInvalidIds) {
      return c.json({ error: "Invalid nature sound IDs in order array" }, 400);
    }

    // Update the nature playlist with new nature sound order
    const updatedNaturePlaylist = await prisma.naturePlaylist.update({
      where: { id: naturePlaylistId },
      data: {
        natureSoundOrder
      },
      include: {
        natureSounds: true
      }
    });

    return c.json({ data: updatedNaturePlaylist });
  })

  // Remove a specific nature sound from playlist - users can remove from their own playlists
  .delete("/:id/nature-sounds/:natureSoundId", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const naturePlaylistId = c.req.param("id");
    const natureSoundId = c.req.param("natureSoundId");

    // Check nature playlist exists
    const naturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id: naturePlaylistId },
      include: {
        natureSounds: {
          where: { id: natureSoundId },
          select: { id: true }
        }
      }
    });

    if (!naturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    // Security check
    const canRemove = user.role === UserRole.ADMIN 
      ? true
      : (naturePlaylist.userId === user.id && naturePlaylist.creatorType !== UserRole.ADMIN);

    if (!canRemove) {
      return c.json({ error: "Unauthorized to remove nature sounds from this playlist" }, 403);
    }

    // Check if nature sound exists in playlist
    if (naturePlaylist.natureSounds.length === 0) {
      return c.json({ error: "Nature sound not found in nature playlist" }, 404);
    }

    // Get current natureSoundOrder and remove the natureSoundId
    const currentNatureSoundOrder = naturePlaylist.natureSoundOrder || [];
    const newNatureSoundOrder = currentNatureSoundOrder.filter(id => id !== natureSoundId);

    // Remove nature sound from playlist and update natureSoundOrder
    const updatedNaturePlaylist = await prisma.naturePlaylist.update({
      where: { id: naturePlaylistId },
      data: {
        natureSounds: {
          disconnect: { id: natureSoundId }
        },
        natureSoundOrder: newNatureSoundOrder
      }
    });

    return c.json({ data: updatedNaturePlaylist });
  })

  // Videos Management - users can manage their own playlists
  .post("/:id/videos", privateRoutesMiddleware, zValidator("json", z.object({
    videoIds: z.array(z.string())
  })), async (c) => {
    const user = c.get("user");
    const naturePlaylistId = c.req.param("id");
    const { videoIds } = c.req.valid("json");

    // Check nature playlist exists
    const naturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id: naturePlaylistId },
    });

    if (!naturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    // Security check
    const canManage = user.role === UserRole.ADMIN 
      ? true
      : (naturePlaylist.userId === user.id && naturePlaylist.creatorType !== UserRole.ADMIN);

    if (!canManage) {
      return c.json({ error: "Unauthorized to manage videos in this playlist" }, 403);
    }

    // Add videos to nature playlist
    const updatedNaturePlaylist = await prisma.naturePlaylist.update({
      where: { id: naturePlaylistId },
      data: {
        videos: {
          connect: videoIds.map(id => ({ id }))
        }
      },
      include: {
        videos: true
      }
    });

    return c.json({ data: updatedNaturePlaylist });
  })

  // Remove a specific video from nature playlist - users can remove from their own playlists
  .delete("/:id/videos/:videoId", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const naturePlaylistId = c.req.param("id");
    const videoId = c.req.param("videoId");

    // Check nature playlist exists
    const naturePlaylist = await prisma.naturePlaylist.findUnique({
      where: { id: naturePlaylistId },
      include: {
        videos: {
          where: { id: videoId },
          select: { id: true }
        }
      }
    });

    if (!naturePlaylist) {
      return c.json({ error: "Nature playlist not found" }, 404);
    }

    // Security check
    const canRemove = user.role === UserRole.ADMIN 
      ? true
      : (naturePlaylist.userId === user.id && naturePlaylist.creatorType !== UserRole.ADMIN);

    if (!canRemove) {
      return c.json({ error: "Unauthorized to remove videos from this playlist" }, 403);
    }

    // Check if video exists in playlist
    if (naturePlaylist.videos.length === 0) {
      return c.json({ error: "Video not found in nature playlist" }, 404);
    }

    // Remove video from nature playlist
    const updatedNaturePlaylist = await prisma.naturePlaylist.update({
      where: { id: naturePlaylistId },
      data: {
        videos: {
          disconnect: { id: videoId }
        }
      }
    });

    return c.json({ data: updatedNaturePlaylist });
  });

export default app; 