"use client"

import { useState, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"
import {
  Search,
  Plus,
  Music2,
  Star,
  Filter,
  X,
  Play,
  Pause,
  Clock,
  Heart,
  Headphones
} from "lucide-react"
import { useGetMusics } from "@schemas/Music/music-query"
import { useAddMusicToMusicPlaylistUser } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { useAudioStore } from "@/lib/audio-store"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { AddToPlaylistDialog } from "../../_components/add-to-playlist-dialog"

interface Music {
  id: string
  title: string
  src: string | null
  genres: string[]
  isPublic: boolean
  rating?: number | null
  duration?: number | null
  note?: string | null
}

export function MusicsRoute() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedGenres, setSelectedGenres] = useState<string[]>([])
  const [selectedMusicId, setSelectedMusicId] = useState<string | null>(null)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)

  const { data: musics, isLoading } = useGetMusics({ isPublic: true })
  const addMusicToPlaylist = useAddMusicToMusicPlaylistUser()

  // Global audio player state
  const {
    globalPlayer,
    setGlobalPlayerTrack,
    setGlobalPlayerPlaying
  } = useAudioStore()

  // Get unique genres for filtering
  const availableGenres = useMemo(() => {
    if (!musics) return []
    const genreSet = new Set<string>()
    musics.forEach(music => {
      music.genres?.forEach(genre => genreSet.add(genre))
    })
    return Array.from(genreSet).sort()
  }, [musics])

  // Filter musics based on search and genre filters
  const filteredMusics = useMemo(() => {
    if (!musics) return []

    return musics.filter(music => {
      const matchesSearch = music.title.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesGenre = selectedGenres.length === 0 ||
        music.genres?.some(genre => selectedGenres.includes(genre))

      return matchesSearch && matchesGenre
    })
  }, [musics, searchQuery, selectedGenres])

  // Enhanced stats with more insights
  const stats = useMemo(() => {
    if (!musics) return { total: 0, genres: 0, totalDuration: 0, avgRating: 0 }

    const total = musics.length
    const uniqueGenres = new Set<string>()
    let totalRating = 0
    let ratedCount = 0
    let totalDurationSeconds = 0

    musics.forEach(music => {
      music.genres?.forEach(genre => uniqueGenres.add(genre))
      if (music.rating) {
        totalRating += music.rating
        ratedCount++
      }
      if (music.duration) {
        totalDurationSeconds += music.duration
      }
    })

    return {
      total,
      genres: uniqueGenres.size,
      totalDuration: Math.floor(totalDurationSeconds / 60), // Convert to minutes
      avgRating: ratedCount > 0 ? totalRating / ratedCount : 0
    }
  }, [musics])

  const handleGenreToggle = (genre: string) => {
    setSelectedGenres(prev => 
      prev.includes(genre) 
        ? prev.filter(g => g !== genre)
        : [...prev, genre]
    )
  }

  const clearFilters = () => {
    setSearchQuery("")
    setSelectedGenres([])
  }

  const handleAddToPlaylist = (musicId: string) => {
    setSelectedMusicId(musicId)
    setIsAddDialogOpen(true)
  }

  const handleAddMusicToPlaylist = async (playlistId: string) => {
    if (!selectedMusicId) return

    await addMusicToPlaylist.mutateAsync({
      musicPlaylistUserId: playlistId,
      musicIds: [selectedMusicId]
    })
  }

  const handlePlayMusic = (music: Music) => {
    const track = {
      id: music.id,
      title: music.title,
      src: music.src || `https://www.soundjay.com/misc/sounds/bell-ringing-05.wav`,
      type: 'music' as const,
      genres: music.genres
    }

    if (globalPlayer.currentTrack?.id === music.id) {
      // If already playing this track, toggle play/pause
      setGlobalPlayerPlaying(!globalPlayer.isPlaying)
    } else {
      // Play new track - set track first, then ensure it starts playing
      setGlobalPlayerTrack(track)
      setGlobalPlayerPlaying(true)
    }
  }

  const selectedMusic = selectedMusicId ? musics?.find(m => m.id === selectedMusicId) : null

  // Compact Loading State
  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Compact Header skeleton */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Skeleton className="h-10 w-10 rounded-xl" />
              <div className="space-y-2">
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-64" />
              </div>
            </div>
          </div>

          {/* Compact stats skeleton */}
          <div className="flex gap-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 rounded" />
                <div className="space-y-1">
                  <Skeleton className="h-5 w-8" />
                  <Skeleton className="h-3 w-12" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Search and filters skeleton */}
        <Card>
          <CardContent className="p-4 space-y-4">
            <Skeleton className="h-10 w-full" />
            <div className="flex flex-wrap gap-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-6 w-16" />
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Grid skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 9 }).map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <CardContent className="p-3">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-10 w-10 rounded-lg shrink-0" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <div className="flex gap-1">
                      <Skeleton className="h-3 w-12" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 pb-32">
        {/* Compact Header with Inline Stats */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-xl bg-gradient-to-br from-orange-500/10 to-rose-500/10 border border-orange-200/20 dark:border-orange-700/20">
                <Music2 className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 bg-clip-text text-transparent">
                  Music Library
                </h1>
                <p className="text-sm text-muted-foreground">
                  Discover and add music tracks to enhance your focus sessions
                </p>
              </div>
            </div>
          </div>

        </div>

        {/* Search and Filters Card */}
        <Card>
          <CardContent className="p-4 space-y-4">
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search music tracks..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {(searchQuery || selectedGenres.length > 0) && (
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  className="gap-2 shrink-0 hover:bg-orange-50 hover:text-orange-700 hover:border-orange-200 dark:hover:bg-orange-950/30 dark:hover:text-orange-300 dark:hover:border-orange-800"
                >
                  <X className="h-4 w-4" />
                  Clear Filters
                </Button>
              )}
            </div>

            {/* Genre Filters */}
            {availableGenres.length > 0 && (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Genres</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {availableGenres.map(genre => (
                    <Badge
                      key={genre}
                      variant={selectedGenres.includes(genre) ? "default" : "outline"}
                      className={cn(
                        "cursor-pointer transition-all duration-200 hover:scale-105",
                        selectedGenres.includes(genre)
                          ? "bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 text-white border-transparent hover:from-orange-600 hover:via-red-600 hover:to-rose-700"
                          : "hover:bg-orange-50 hover:text-orange-700 hover:border-orange-200 dark:hover:bg-orange-950/30 dark:hover:text-orange-300 dark:hover:border-orange-800"
                      )}
                      onClick={() => handleGenreToggle(genre)}
                    >
                      {genre}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Enhanced Music Display */}
        <AnimatePresence mode="wait">
        {filteredMusics.length === 0 ? (
          <motion.div
            key="empty"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            className="flex flex-col items-center justify-center py-20 text-center"
          >
            {/* Enhanced Icon with Animation */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.1, type: "spring", stiffness: 200, damping: 15 }}
              className="relative mb-8"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-orange-400/20 to-rose-400/20 rounded-full blur-xl scale-150" />
              <div className="relative p-6 rounded-2xl bg-gradient-to-br from-orange-50 via-red-50 to-rose-50 dark:from-orange-950/50 dark:via-red-950/30 dark:to-rose-950/50 border border-orange-200/50 dark:border-orange-800/50 shadow-lg">
                <motion.div
                  animate={{
                    rotate: [0, -10, 10, -5, 5, 0],
                    scale: [1, 1.05, 1]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatDelay: 3,
                    ease: "easeInOut"
                  }}
                >
                  <Music2 className="h-16 w-16 text-orange-500 dark:text-orange-400" />
                </motion.div>
              </div>
            </motion.div>

            {/* Enhanced Content */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="space-y-6 max-w-lg"
            >
              <div className="space-y-3">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-orange-600 via-red-600 to-rose-600 bg-clip-text text-transparent">
                  {searchQuery || selectedGenres.length > 0
                    ? "No tracks match your search"
                    : "Discover amazing music"
                  }
                </h3>
                <p className="text-base text-muted-foreground leading-relaxed px-4">
                  {searchQuery || selectedGenres.length > 0
                    ? "Try adjusting your search terms or filters to find the perfect tracks for your focus sessions."
                    : "Explore our curated collection of focus music to enhance your productivity and concentration."
                  }
                </p>
              </div>

              {/* Enhanced Features List */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5, duration: 0.5 }}
                className="grid grid-cols-1 sm:grid-cols-3 gap-4 py-4"
              >
                <div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-muted/30">
                  <div className="p-2 rounded-full bg-orange-500/10">
                    <Music2 className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground">Focus Music</span>
                </div>
                <div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-muted/30">
                  <div className="p-2 rounded-full bg-rose-500/10">
                    <Headphones className="h-4 w-4 text-rose-600 dark:text-rose-400" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground">High Quality</span>
                </div>
                <div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-muted/30">
                  <div className="p-2 rounded-full bg-red-500/10">
                    <Heart className="h-4 w-4 text-red-600 dark:text-red-400" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground">Curated Selection</span>
                </div>
              </motion.div>

              {/* Clear Filters Button */}
              {(searchQuery || selectedGenres.length > 0) && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.7, duration: 0.3 }}
                  className="flex justify-center pt-2"
                >
                  <Button
                    onClick={clearFilters}
                    size="lg"
                    className="bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 hover:from-orange-600 hover:via-red-600 hover:to-rose-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 group px-8"
                  >
                    <X className="mr-2 h-5 w-5 group-hover:rotate-90 transition-transform duration-200" />
                    Clear All Filters
                  </Button>
                </motion.div>
              )}
            </motion.div>
          </motion.div>
        ) : (
          <motion.div
            key="musics"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
          >
            {filteredMusics.map((music, index) => {
              const isCurrentlyPlaying = globalPlayer.currentTrack?.id === music.id && globalPlayer.isPlaying
              const isCurrentTrack = globalPlayer.currentTrack?.id === music.id

              return (
                <motion.div
                  key={music.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    delay: index * 0.03,
                    duration: 0.2,
                    ease: "easeOut"
                  }}
                  layout
                >
                  <Card
                    className={cn(
                      "group relative overflow-hidden transition-all duration-200 cursor-pointer h-full",
                      "hover:shadow-lg hover:bg-accent/30 hover:border-accent-foreground/20",
                      isCurrentTrack && "bg-orange-50/50 border-orange-200 ring-1 ring-orange-200/50 dark:bg-orange-950/20 dark:border-orange-800 dark:ring-orange-800/50"
                    )}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center gap-3 relative">
                        {/* Music Icon / Play Button */}
                        <div className="h-10 w-10 rounded-lg bg-muted/20 group-hover:bg-orange-50 dark:group-hover:bg-orange-950/30 flex items-center justify-center shrink-0 relative transition-colors duration-200">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation()
                              handlePlayMusic(music)
                            }}
                            className="w-10 h-10 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/40 absolute inset-0 transition-colors duration-150"
                          >
                            {isCurrentlyPlaying ? (
                              <Pause className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                            ) : (
                              <Play className="h-4 w-4 text-orange-600 dark:text-orange-400 ml-0.5" />
                            )}
                          </Button>
                        </div>

                        {/* Music Info */}
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium line-clamp-1 mb-1.5 text-sm leading-tight">{music.title}</h3>
                          <div className="flex items-center gap-1.5 flex-wrap">
                            {music.genres?.slice(0, 2).map(genre => (
                              <Badge
                                key={genre}
                                variant="secondary"
                                className="text-xs px-1.5 py-0.5"
                              >
                                {genre}
                              </Badge>
                            ))}
                            {music.rating && (
                              <div className="flex items-center gap-1">
                                <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                <span className="text-xs text-muted-foreground">
                                  {music.rating.toFixed(1)}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Add Button - Hover Only */}
                        <div className="absolute right-3 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-200 transform translate-x-2 group-hover:translate-x-0">
                          <Button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleAddToPlaylist(music.id)
                            }}
                            size="sm"
                            className={cn(
                              "h-7 px-3 text-xs transition-all duration-200 gap-1.5 shadow-lg",
                              "bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 hover:from-orange-600 hover:via-red-600 hover:to-rose-700 text-white",
                              "hover:shadow-xl hover:scale-105"
                            )}
                          >
                            <Plus className="h-3 w-3" />
                            Add
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </motion.div>
        )}
        </AnimatePresence>

        {/* Add to Playlist Dialog */}
        <AddToPlaylistDialog
          isOpen={isAddDialogOpen}
          onOpenChange={setIsAddDialogOpen}
          itemType="music"
          itemTitle={selectedMusic?.title || ""}
          onAddToPlaylist={handleAddMusicToPlaylist}
        />
      </div>
  )
}
