"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"
import { v4 as uuidv4 } from "uuid"
import type { Task } from "@/types/task"

interface TaskState {
  tasks: Task[]
  addTask: (task: Partial<Task>) => void
  updateTask: (id: string, updates: Partial<Task>) => void
  deleteTask: (id: string) => void
  duplicateTask: (id: string) => void
  clearCompletedTasks: () => void
  updateTaskOrder: (tasks: Task[]) => void
  getTaskById: (id: string) => Task | undefined
  incrementPomodoroCount: (id: string) => void
  updateTaskProgress: (id: string, progress: number) => void
}

export const useTaskStore = create<TaskState>()(
  persist(
    (set, get) => ({
      tasks: [],

      addTask: (taskData) => {
        const newTask: Task = {
          id: uuidv4(),
          title: taskData.title || "Untitled Task",
          description: taskData.description || "",
          status: taskData.status || "pending",
          priority: taskData.priority || "medium",
          dueDate: taskData.dueDate || null,
          recurrence: taskData.recurrence || null,
          pomodoroEstimate: taskData.pomodoroEstimate || 1,
          pomodoroCount: 0,
          progress: 0,
          timeSpent: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          order: get().tasks.length,
        }

        set((state) => ({
          tasks: [...state.tasks, newTask],
        }))
      },

      updateTask: (id, updates) => {
        set((state) => ({
          tasks: state.tasks.map((task) =>
            task.id === id
              ? {
                  ...task,
                  ...updates,
                  updatedAt: new Date().toISOString(),
                }
              : task,
          ),
        }))
      },

      deleteTask: (id) => {
        set((state) => ({
          tasks: state.tasks.filter((task) => task.id !== id),
        }))
      },

      duplicateTask: (id) => {
        const taskToDuplicate = get().tasks.find((task) => task.id === id)

        if (taskToDuplicate) {
          const duplicatedTask: Task = {
            ...taskToDuplicate,
            id: uuidv4(),
            title: `${taskToDuplicate.title} (Copy)`,
            status: "pending",
            pomodoroCount: 0,
            progress: 0,
            timeSpent: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            order: get().tasks.length,
          }

          set((state) => ({
            tasks: [...state.tasks, duplicatedTask],
          }))
        }
      },

      clearCompletedTasks: () => {
        set((state) => ({
          tasks: state.tasks.filter((task) => task.status !== "completed"),
        }))
      },

      updateTaskOrder: (updatedTasks) => {
        set((state) => {
          // Create a map of updated tasks by ID
          const updatedTaskMap = new Map(updatedTasks.map((task) => [task.id, task]))

          // Update only the tasks that were reordered
          const newTasks = state.tasks.map((task) => {
            const updatedTask = updatedTaskMap.get(task.id)
            if (updatedTask) {
              return { ...task, ...updatedTask }
            }
            return task
          })

          return { tasks: newTasks }
        })
      },

      getTaskById: (id) => {
        return get().tasks.find((task) => task.id === id)
      },

      incrementPomodoroCount: (id) => {
        set((state) => ({
          tasks: state.tasks.map((task) =>
            task.id === id
              ? {
                  ...task,
                  pomodoroCount: task.pomodoroCount + 1,
                  updatedAt: new Date().toISOString(),
                }
              : task,
          ),
        }))
      },

      updateTaskProgress: (id, progress) => {
        set((state) => ({
          tasks: state.tasks.map((task) =>
            task.id === id
              ? {
                  ...task,
                  progress,
                  status: progress >= 100 ? "completed" : task.status,
                  updatedAt: new Date().toISOString(),
                }
              : task,
          ),
        }))
      },
    }),
    {
      name: "pomodoro-tasks",
    },
  ),
)
