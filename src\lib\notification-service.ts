'use client';

import { soundMap, SoundType, useNotificationStore } from './notification-store';

interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  sound?: string;
}

interface StoredNotificationSettings {
  soundType: SoundType;
  volume: number;
  repeat: number;
  enabled: boolean;
}

class NotificationService {
  private notificationPermission: NotificationPermission = 'default';
  private audio: HTMLAudioElement | null = null;

  constructor() {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      try {
        this.notificationPermission = Notification.permission;
        // Don't automatically request permission - let the user decide
      } catch (error) {
        console.warn('Notification API not accessible:', error);
        this.notificationPermission = 'denied';
      }
    } else {
      this.notificationPermission = 'denied';
    }
  }

  // Request notification permission
  async requestPermission(): Promise<boolean> {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      return false;
    }

    if (this.notificationPermission === 'granted') {
      return true;
    }

    try {
      const permission = await Notification.requestPermission();
      this.notificationPermission = permission;
      return permission === 'granted';
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      this.notificationPermission = 'denied';
      return false;
    }
  }

  // Check if notifications are supported and permission is granted
  canNotify(): boolean {
    return (
      typeof window !== 'undefined' &&
      'Notification' in window &&
      this.notificationPermission === 'granted'
    );
  }

  // Get current notification permission status
  getPermissionStatus(): NotificationPermission {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      try {
        this.notificationPermission = Notification.permission;
        return this.notificationPermission;
      } catch (error) {
        console.warn('Error accessing Notification.permission:', error);
        this.notificationPermission = 'denied';
        return 'denied';
      }
    }
    return 'denied';
  }

  // Check if notifications are supported by the browser
  isSupported(): boolean {
    return typeof window !== 'undefined' && 'Notification' in window;
  }

  // Show a notification
  async notify({ title, body, icon, sound }: NotificationOptions): Promise<void> {
    if (!this.canNotify()) {
      const hasPermission = await this.requestPermission();
      if (!hasPermission) {
        console.log('No notification permission granted');
        return;
      }
    }

    try {
      // Get notification settings from store
      let notificationSettings: StoredNotificationSettings | null = null;
      if (typeof window !== 'undefined') {
        const settingsString = localStorage.getItem('notificationSettings');
        if (settingsString) {
          try {
            notificationSettings = JSON.parse(settingsString) as StoredNotificationSettings;
          } catch (e) {
            console.error('Failed to parse notification settings:', e);
          }
        }
      }

      // Check if notifications are enabled
      if (notificationSettings && !notificationSettings.enabled) {
        return;
      }

      // Use settings from store if available, otherwise use default sound
      if (notificationSettings) {
        // Try to get the preloaded audio from the store
        const preloadedAudio = useNotificationStore.getState().preloadedAudio;

        // Function to play sound once
        const playOnce = (index: number) => {
          setTimeout(() => {
            // Use preloaded audio if available for first play for instant start
            if (index === 0 && preloadedAudio) {
              // Clone the audio to allow multiple plays
              preloadedAudio.currentTime = 0;
              preloadedAudio.play().catch(error => {
                console.error('Error playing preloaded notification sound:', error);
              });
              // Preload next sound immediately for future notifications
              useNotificationStore.getState().preloadSound();
            } else {
              // For repeats or if no preloaded audio, create new audio element
              const audio = new Audio(soundMap[notificationSettings!.soundType]);
              audio.volume = notificationSettings!.volume / 100;
              audio.play().catch(error => {
                console.error('Error playing notification sound:', error);
              });
            }
          }, index * 1000);
        };

        // Play the sound the specified number of times
        for (let i = 0; i < notificationSettings.repeat; i++) {
          playOnce(i);
        }
      } else if (sound && typeof window !== 'undefined') {
        // Fallback to provided sound if no settings exist
        this.audio = new Audio(sound);
        this.audio.play().catch(error => {
          console.error('Error playing notification sound:', error);
        });
      }

      // Create and show the notification (only if Notification API is available)
      if (typeof window !== 'undefined' && 'Notification' in window && this.notificationPermission === 'granted') {
        try {
          const notification = new Notification(title, {
            body,
            icon: icon || '/favicon.ico',
          });

          // Close notification after 5 seconds
          setTimeout(() => notification.close(), 5000);
        } catch (notificationError) {
          console.warn('Error creating notification:', notificationError);
          // Notification failed, but sound should still play
        }
      }
    } catch (error) {
      console.error('Error showing notification:', error);
    }
  }

  // Notify for Pomodoro completion
  notifyPomodoroComplete(): void {
    this.notify({
      title: 'Pomodoro Complete!',
      body: 'Time for a break. Good job!',
      sound: '/notifications/bell.mp3',
    });
  }

  // Notify for break completion
  notifyBreakComplete(isLongBreak = false): void {
    this.notify({
      title: `${isLongBreak ? 'Long' : 'Short'} Break Complete!`,
      body: 'Time to focus again.',
      sound: '/notifications/bell.mp3',
    });
  }
}

// Create a singleton instance
export const notificationService = typeof window !== 'undefined'
  ? new NotificationService()
  : null;