"use client";

import { useState } from "react";
import { useGetVideos } from "../../../../../prisma/schema/Video/video-query";
import { VideoTable } from "./video-table";
import { VideoFormSheet } from "./video-form-sheet";
import { Button } from "@/components/ui/button";
import { PlusIcon } from "lucide-react";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { GetVideos_ResponseTypeSuccess } from "@schemas/Video/video-query";

export function VideoManagement() {
  const [isCreateSheetOpen, setIsCreateSheetOpen] = useState(false);
  const [editVideoId, setEditVideoId] = useState<string | null>(null);
  const [filter, setFilter] = useState<"all" | "public" | "private" | "premium">("all");
  
  // Query to fetch videos based on filter
  const videosQuery = useGetVideos(
    filter === "public" ? { isPublic: true } :
    filter === "private" ? { isPublic: false } :
    filter === "premium" ? { isPremium: true } :
    undefined
  );
  
  // Handle edit action
  const handleEdit = (id: string) => {
    setEditVideoId(id);
  };
  
  // Close all sheets
  const handleCloseSheets = () => {
    setIsCreateSheetOpen(false);
    setEditVideoId(null);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Tabs
          defaultValue="all"
          className="w-[400px]"
          onValueChange={(value) => setFilter(value as "all" | "public" | "private" | "premium")}
        >
          <TabsList>
            <TabsTrigger value="all">All Videos</TabsTrigger>
            <TabsTrigger value="public">Public</TabsTrigger>
            <TabsTrigger value="private">Private</TabsTrigger>
            <TabsTrigger value="premium">Premium</TabsTrigger>
          </TabsList>
        </Tabs>
        
        <Button 
          onClick={() => setIsCreateSheetOpen(true)}
          className="flex items-center gap-1"
        >
          <PlusIcon className="h-4 w-4" />
          New Video
        </Button>
      </div>
      
      {videosQuery.isLoading ? (
        <div className="space-y-3">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
        </div>
      ) : videosQuery.isError ? (
        <div className="p-8 text-center">
          <p className="text-red-500">Error loading videos: {videosQuery.error.message}</p>
        </div>
      ) : (
        <VideoTable 
          videos={videosQuery.data as GetVideos_ResponseTypeSuccess || []} 
          onEdit={handleEdit} 
        />
      )}
      
      {/* Create Video Sheet */}
      <VideoFormSheet 
        open={isCreateSheetOpen} 
        onOpenChange={setIsCreateSheetOpen}
        onSuccess={() => {
          handleCloseSheets();
          videosQuery.refetch();
        }}
      />
      
      {/* Edit Video Sheet */}
      {editVideoId && (
        <VideoFormSheet 
          videoId={editVideoId}
          open={!!editVideoId} 
          onOpenChange={(open: boolean) => {
            if (!open) setEditVideoId(null);
          }}
          onSuccess={() => {
            handleCloseSheets();
            videosQuery.refetch();
          }}
        />
      )}
    </div>
  );
}