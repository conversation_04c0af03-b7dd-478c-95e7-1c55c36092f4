'use client';

import { motion } from 'framer-motion';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Mail, MessageCircle } from 'lucide-react';

const faqs = [
  {
    question: "Can I start with the free plan and upgrade later?",
    answer: "Absolutely! You can start with our free plan and upgrade to any premium plan at any time. Your data and settings will be preserved when you upgrade, and you'll immediately gain access to all premium features."
  },
  {
    question: "What's the difference between Monthly and Annual plans?",
    answer: "Both plans include the exact same premium features. The Annual plan offers significant savings - you pay $19/year instead of $36/year (monthly billing), saving you 47%. It's perfect if you're committed to improving your productivity long-term."
  },
  {
    question: "Is the Lifetime plan really lifetime?",
    answer: "Yes! The Lifetime plan gives you permanent access to all current and future premium features with a one-time payment of $59. You'll never be charged again and will receive all updates and new features as they're released."
  },
  {
    question: "Can I cancel my subscription anytime?",
    answer: "Yes, you can cancel your subscription at any time from your account settings. If you cancel, you'll continue to have access to premium features until the end of your current billing period, then your account will revert to the free plan."
  },
  {
    question: "Do you offer refunds?",
    answer: "We offer a 14-day money-back guarantee for all paid plans. If you're not satisfied within the first 14 days, contact our support team for a full refund. Refunds for the Lifetime plan are available within 30 days of purchase."
  },
  {
    question: "How does cloud sync work?",
    answer: "With any premium plan, your timer settings, session history, playlists, and preferences are automatically synced across all your devices. Sign in to your account on any device to access your personalized Pomodoro setup."
  },
  {
    question: "What payment methods do you accept?",
    answer: "We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and various local payment methods depending on your region. All payments are processed securely through our payment partner."
  },
  {
    question: "Is there a student or educational discount?",
    answer: "Yes! We offer a 30% discount for students and educational institutions. Contact our support team with your educational email address or student ID to apply for the discount."
  },
  {
    question: "How many devices can I use with premium plans?",
    answer: "Premium plans allow unlimited device access. You can use Pomodoro 365 on your computer, tablet, phone, and any other devices. Your data stays synced across all of them."
  },
  {
    question: "What happens to my data if I downgrade?",
    answer: "If you downgrade to the free plan, your data is preserved but some features become limited. You'll keep access to current day analytics but lose historical data access. Your playlists and settings are saved and will be restored if you upgrade again."
  }
];

export const PricingFAQ = () => {
  return (
    <section className="w-full py-16 md:py-20 bg-background">
      <div className="container mx-auto px-4 max-w-4xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Frequently Asked{' '}
            <span className="bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 bg-clip-text text-transparent">
              Questions
            </span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Everything you need to know about our pricing and plans. Can't find what you're looking for? 
            Contact our support team.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          viewport={{ once: true }}
        >
          <Card>
            <CardContent className="p-6">
              <Accordion type="single" collapsible className="w-full">
                {faqs.map((faq, index) => (
                  <AccordionItem key={index} value={`item-${index}`}>
                    <AccordionTrigger className="text-left hover:text-orange-600 transition-colors">
                      {faq.question}
                    </AccordionTrigger>
                    <AccordionContent className="text-muted-foreground leading-relaxed">
                      {faq.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </CardContent>
          </Card>
        </motion.div>

        {/* Contact Support Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          viewport={{ once: true }}
          className="mt-12"
        >
          <Card className="bg-gradient-to-r from-orange-50/50 to-red-50/50 border-orange-200/50">
            <CardHeader className="text-center">
              <CardTitle className="text-xl font-semibold">
                Still have questions?
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground mb-6">
                Our support team is here to help you choose the right plan and get the most out of Pomodoro 365.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button variant="outline" className="gap-2">
                  <Mail className="h-4 w-4" />
                  Email Support
                </Button>
                <Button variant="outline" className="gap-2">
                  <MessageCircle className="h-4 w-4" />
                  Live Chat
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};
