---
description: 
globs: 
alwaysApply: false
---
# Development Workflow

## Project Setup
This project uses Bun instead of npm/pnpm. The [package.json](mdc:package.json) contains all dependencies and scripts.

## Running the Project
- `bun install` - Install dependencies
- `bun run dev` - Start development server on port 2604
- `bun run build` - Build for production
- `bun run start` - Start production server on port 2604

## Code Standards
- TypeScript for type safety (avoid using "any" type)
- Tailwind CSS for styling
- Next.js App Router structure
- React Server Components where possible

## Dependencies
- React 19 with modern hooks
- Next.js 15 with App Router
- Shadcn UI components with Radix UI primitives
- Lucide React for icons
- Zustand for state management

## Key Configuration Files
- [next.config.ts](mdc:next.config.ts) - Next.js configuration
- [tsconfig.json](mdc:tsconfig.json) - TypeScript configuration
- [components.json](mdc:components.json) - Shadcn UI configuration
