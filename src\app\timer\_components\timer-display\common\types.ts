import { TimerColorPreset, TimerPositions } from '@/lib/pomodoro-store';
import { TimerSizePreset } from '../use-timer-resize';

export interface CustomPosition {
  top: number;
  left: number;
}

export interface TimerSize {
  width?: number;
  timeScale?: number;
}

export interface FontSize {
  time?: string;
}

export interface TimerDisplayProps {
  className?: string;
}

export interface PlayPauseButtonProps {
  isRunning: boolean;
  onClick: () => void;
  onReset?: () => void;
  timerColor: TimerColorPreset;
}

export interface ActionButtonProps {
  icon: React.ReactNode;
  onClick: () => void;
  label?: string;
  title?: string;
  variant?: "ghost" | "outline" | "secondary";
}

export interface SettingsButtonProps {
  currentPhase: string;
  customPosition: CustomPosition | null;
  timerSize: TimerSize | null;
  resetPosition: () => void;
  handleResetSize: () => void;
  skipBreak: () => void;
  handleApplyPreset: (preset: TimerSizePreset) => void;
  currentSizePreset: TimerSizePreset | null;
  setTimerPosition: (position: TimerPositions) => void;
  timerPosition: TimerPositions;
}

export interface PhaseSwitcherProps {
  onSwitchPhase: (direction: 'prev' | 'next') => void;
  showControls?: boolean;
} 