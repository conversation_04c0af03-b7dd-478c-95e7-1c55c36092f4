'use client';

import { useState } from 'react';
import { <PERSON><PERSON><PERSON>cle2, Circle, Trash2, MoreH<PERSON>zontal, Edit, Play, Timer, Target, Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { usePomodoroStore } from '@/lib/pomodoro-store';
import type { UnifiedTask } from '@/hooks/useTaskManagement';

type TaskType = UnifiedTask;

interface TaskItemProps {
  task: TaskType;
  onToggle: () => void;
  onDelete: () => void;
  onEdit: () => void;
  isLoading?: boolean;
  onTaskFocusStart?: () => void;
}

// Helper function to format duration in a user-friendly way
function formatDuration(seconds: number): string {
  if (seconds === 0) return '0m';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (hours > 0) {
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  }
  
  return `${minutes}m`;
}

// Helper function to calculate pomodoro statistics
function getPomodoroStats(task: TaskType) {
  // For local tasks, pomodoroSessions might not exist
  const sessions = task.pomodoroSessions || [];
  
  // Calculate total focus duration from all sessions (completed + interrupted)
  // Ensure we handle null/undefined values and convert to numbers properly
  const totalFocusDuration = sessions.reduce((sum: number, session: any) => {
    const focusDuration = session.focusDuration || 0;
    // Ensure it's a number and handle any potential string values
    const duration = typeof focusDuration === 'number' ? focusDuration : parseInt(String(focusDuration)) || 0;
    return sum + duration;
  }, 0);
  
  return {
    sessionCount: sessions.length,
    totalFocusDuration,
    formattedDuration: formatDuration(totalFocusDuration)
  };
}

// Helper function to get color styling based on session count
function getSessionColorClass(sessionCount: number) {
  if (sessionCount === 0) {
    return {
      textColor: 'text-muted-foreground/70',
      iconColor: 'text-muted-foreground/50',
      bgColor: 'bg-muted/30',
      borderColor: 'border-muted/40'
    };
  } else if (sessionCount <= 2) {
    return {
      textColor: 'text-emerald-600 dark:text-emerald-400',
      iconColor: 'text-emerald-500',
      bgColor: 'bg-emerald-50 dark:bg-emerald-900/20',
      borderColor: 'border-emerald-200 dark:border-emerald-800/40'
    };
  } else if (sessionCount <= 5) {
    return {
      textColor: 'text-green-600 dark:text-green-400',
      iconColor: 'text-green-500',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      borderColor: 'border-green-200 dark:border-green-800/40'
    };
  } else {
    return {
      textColor: 'text-blue-600 dark:text-blue-400',
      iconColor: 'text-blue-500',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      borderColor: 'border-blue-200 dark:border-blue-800/40'
    };
  }
}

// Helper function to get focus duration color styling
function getFocusDurationColorClass(totalMinutes: number) {
  if (totalMinutes === 0) {
    return {
      textColor: 'text-muted-foreground/70',
      iconColor: 'text-muted-foreground/50'
    };
  } else if (totalMinutes < 30) {
    return {
      textColor: 'text-amber-600 dark:text-amber-400',
      iconColor: 'text-amber-500'
    };
  } else if (totalMinutes < 120) {
    return {
      textColor: 'text-emerald-600 dark:text-emerald-400',
      iconColor: 'text-emerald-500'
    };
  } else {
    return {
      textColor: 'text-violet-600 dark:text-violet-400',
      iconColor: 'text-violet-500'
    };
  }
}

export function TaskItem({ task, onToggle, onDelete, onEdit, isLoading = false, onTaskFocusStart }: TaskItemProps) {
  const [isHovered, setIsHovered] = useState(false);
  const { startFocusSession, isRunning, currentTask } = usePomodoroStore();

  const handleStartFocus = () => {
    startFocusSession({ id: task.id, title: task.title });
    // Call the callback to close the task sheet
    onTaskFocusStart?.();
  };

  const isCurrentTask = currentTask?.id === task.id;
  const pomodoroStats = getPomodoroStats(task);
  const sessionColors = getSessionColorClass(pomodoroStats.sessionCount);
  const focusColors = getFocusDurationColorClass(Math.floor(pomodoroStats.totalFocusDuration / 60));
  
  // Check if this is an optimistic task
  const isOptimistic = task.isOptimistic;

  return (
    <div
      className={cn(
        "group relative p-2.5 rounded-md border transition-all duration-300 overflow-hidden",
        "bg-white hover:bg-gray-50/50 hover:shadow-sm",
        task.completed ? "border-gray-200/60 opacity-75" : "border-border/40",
        isLoading && "opacity-50 pointer-events-none",
        isCurrentTask && "ring-1 ring-primary/50 border-primary/50 shadow-sm",
        isOptimistic && "border-primary/40 bg-gradient-to-r from-primary/5 to-transparent animate-in slide-in-from-top-1 duration-300"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/90 rounded-lg z-10">
          <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent" />
        </div>
      )}

      {/* Optimistic task indicator with enhanced animations */}
      {isOptimistic && (
        <motion.div 
          className="absolute top-2 right-2 flex items-center gap-1"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1, duration: 0.3 }}
        >
          <motion.div 
            className="w-1.5 h-1.5 bg-primary rounded-full"
            animate={{ 
              scale: [1, 1.5, 1],
              opacity: [0.7, 1, 0.7] 
            }}
            transition={{ 
              duration: 1.5, 
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.span 
            className="text-xs text-primary/70 font-medium"
            animate={{ opacity: [0.7, 1, 0.7] }}
            transition={{ 
              duration: 2, 
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            Saving...
          </motion.span>
        </motion.div>
      )}

      <div className="flex items-start gap-2.5">
        {/* Checkbox */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="h-5 w-5 p-0 rounded-full hover:bg-muted/50 shrink-0 mt-0.5"
              onClick={onToggle}
              disabled={isLoading || isOptimistic}
            >
              {task.completed ? (
                <CheckCircle2 className="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
              ) : (
                <Circle className={cn(
                  "h-3.5 w-3.5 transition-colors",
                  isOptimistic ? "text-primary/60" : "text-muted-foreground hover:text-primary"
                )} />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent side="right" className="z-[80] bg-white text-foreground border border-border shadow-md">
            <span>
              {isOptimistic 
                ? 'Saving task...' 
                : task.completed 
                  ? 'Mark as pending' 
                  : 'Mark as completed'
              }
            </span>
          </TooltipContent>
        </Tooltip>

        {/* Task Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-1.5">
                <p
                  className={cn(
                    "text-xs font-medium leading-4 transition-all duration-200",
                    task.completed
                      ? "text-green-600 dark:text-green-400"
                      : isOptimistic
                        ? "text-foreground/85"
                        : "text-foreground"
                  )}
                  title={task.title}
                >
                  {task.title}
                </p>
                
                {/* Current task indicator */}
                {isCurrentTask && !task.completed && (
                  <Badge variant="default" className="text-[10px] px-1 py-0 h-4 bg-primary/10 text-primary border-primary/20">
                    {isRunning ? 'Active' : 'Selected'}
                  </Badge>
                )}

                {/* Optimistic task badge */}
                {isOptimistic && (
                  <Badge variant="outline" className="text-[10px] px-1 py-0 h-4 border-primary/30 text-primary/70 bg-primary/5">
                    New
                  </Badge>
                )}
              </div>
              
              {/* Pomodoro Stats */}
              <div className="flex items-center gap-1 mt-1.5 text-[10px]">
                {/* Session count */}
                <div className={cn("flex items-center gap-0.5", sessionColors.textColor)}>
                  <Target className={cn("h-2.5 w-2.5", sessionColors.iconColor)} />
                  <span>
                    {pomodoroStats.sessionCount === 0 
                      ? 'No sessions'
                      : `${pomodoroStats.sessionCount} session${pomodoroStats.sessionCount === 1 ? '' : 's'}`
                    }
                  </span>
                </div>
                
                {/* Focus duration - only show if there are any sessions */}
                {pomodoroStats.sessionCount > 0 && (
                  <div className={cn("flex items-center gap-0.5", focusColors.textColor)}>
                    <Timer className={cn("h-2.5 w-2.5", focusColors.iconColor)} />
                    <span>
                      {pomodoroStats.formattedDuration}
                    </span>
                  </div>
                )}
                
                {task.completed && (
                  <Badge variant="secondary" className="text-[10px] px-1 py-0 h-3.5 bg-green-100 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/40">
                    Done
                  </Badge>
                )}
              </div>
            </div>

            {/* Actions - Hide for optimistic tasks */}
            {!isOptimistic && (
              <div
                className={cn(
                  "flex items-center gap-0.5 transition-opacity duration-200 relative z-10",
                  isHovered ? "opacity-100" : "opacity-0 group-hover:opacity-100"
                )}
              >
                {/* Start Focus Button - Only show for non-completed tasks */}
                {!task.completed && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className={cn(
                          "h-6 w-6 rounded-full hover:bg-primary/10 relative z-20 transition-colors",
                          isCurrentTask && "bg-primary/10 text-primary"
                        )}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleStartFocus();
                        }}
                        disabled={isLoading}
                      >
                        <Play className={cn(
                          "h-3 w-3 transition-colors",
                          isCurrentTask ? "text-primary" : "text-muted-foreground hover:text-primary"
                        )} />
                        <span className="sr-only">Start focus session</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="z-[80] bg-white text-foreground border border-border shadow-md">
                      <div className="text-center">
                        <div className="font-medium text-sm">
                          {isCurrentTask ? 'Continue Focus Session' : 'Start Focus Session'}
                        </div>
                        {pomodoroStats.sessionCount > 0 && (
                          <div className="text-xs text-muted-foreground mt-0.5">
                            {pomodoroStats.sessionCount} session{pomodoroStats.sessionCount === 1 ? '' : 's'} • {pomodoroStats.formattedDuration} focused
                          </div>
                        )}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                )}

                <DropdownMenu modal={false}>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-5 w-5 rounded-full hover:bg-muted/50 relative z-20"
                      disabled={isLoading}
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    >
                      <MoreHorizontal className="h-2.5 w-2.5" />
                      <span className="sr-only">Task options</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-28 z-[80]" side="bottom">
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        onEdit();
                      }}
                      className="text-[11px] py-1.5"
                      disabled={isLoading}
                    >
                      <Edit className="h-2.5 w-2.5 mr-1.5" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        onToggle();
                      }}
                      className="text-[11px] py-1.5"
                      disabled={isLoading}
                    >
                      {task.completed ? (
                        <>
                          <Circle className="h-2.5 w-2.5 mr-1.5" />
                          Mark Pending
                        </>
                      ) : (
                        <>
                          <CheckCircle2 className="h-2.5 w-2.5 mr-1.5" />
                          Mark Done
                        </>
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        onDelete();
                      }}
                      className="text-[11px] py-1.5 text-destructive focus:text-destructive"
                      disabled={isLoading}
                    >
                      <Trash2 className="h-2.5 w-2.5 mr-1.5" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Completion indicator */}
      {task.completed && (
        <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-green-500/30 rounded-b-lg" />
      )}
    </div>
  );
} 