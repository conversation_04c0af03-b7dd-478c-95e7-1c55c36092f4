'use client';

import { useNavigationAudioCleanup } from '@/hooks/use-navigation-audio-cleanup';

/**
 * Global component that handles audio cleanup during navigation.
 * This component should be placed in the root layout to catch all route changes.
 */
export function NavigationAudioCleanup() {
  // Initialize the navigation audio cleanup hook
  useNavigationAudioCleanup();

  // This component doesn't render anything - it's just for navigation cleanup
  return null;
}
