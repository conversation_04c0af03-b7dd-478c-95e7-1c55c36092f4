import { getUserServer } from "@/server/private/get-user-server"
import { redirect } from "next/navigation"
import { DashboardLayoutClient } from "./layout-client"

// Force dynamic rendering for authentication
export const dynamic = 'force-dynamic';

interface DashboardLayoutProps {
  children: React.ReactNode
}

// Helper function to get page title based on pathname
function getPageInfo(pathname: string) {
  if (pathname.includes('/analytics')) {
    return {
      title: "Focus Analytics",
      showFilters: true,
      showDownload: true
    }
  }
  
  if (pathname.includes('/profile')) {
    return {
      title: "User Profile",
      showFilters: false,
      showDownload: false
    }
  }
  
  if (pathname.includes('/playlist')) {
    return {
      title: "Music & Playlists",
      // subtitle: "Enhance your focus with audio",
      showFilters: false,
      showDownload: false
    }
  }
  
  if (pathname.includes('/tasks')) {
    return {
      title: "Task Management",
      subtitle: "Organize and track your work",
      showFilters: false,
      showDownload: false
    }
  }

  return {
    title: "Focus Analytics",
    showFilters: false,
    showDownload: false
  }
}

export default async function DashboardLayout({ children }: DashboardLayoutProps) {
  // Use server-side authentication check
  const { user, isAuthenticated, error } = await getUserServer()

  // If user is not authenticated or there's an error, redirect to sign-in
  if (!user || !isAuthenticated || error) {
    redirect("/auth/sign-in")
  }

  // User is authenticated, render the dashboard content with client layout
  return <DashboardLayoutClient>{children}</DashboardLayoutClient>
}
