"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { InnovativeMetrics } from "@/components/stats/InnovativeMetrics"
import { FocusPatternInsights } from "@/components/stats/FocusPatternInsights"
import { Brain, Sparkles } from "lucide-react"

interface AIInsightsTabProps {
  transformedData: {
    // InnovativeMetrics properties
    productivityScore: number
    focusEfficiency: number
    deepWorkRatio: number
    goalAchievement: number
    flowStateRating: string
    flowStateTime: string
    flowStateDuration: string
    flowStateData: Array<{ time: string; flowScore: number }>
    flowTriggers: string[]
    flowBlockers: string[]
    optimalTimeData: Array<{ hour: string; score: number }>
    optimalDay: string
    optimalDayCompletion: number
    optimalTime: string
    optimalTimeEfficiency: number
    optimalDuration: string
    optimalDurationSuccess: number
    // FocusPatternInsights properties
    productivityRhythm: string
    focusDurationInsight: string
    consistencyPattern: string
    recommendations: string[]
  }
}

export function AIInsightsTab({ transformedData }: AIInsightsTabProps) {
  return (
    <div className="space-y-6">
      {/* AI Insights Header */}
      <Card className="border-border bg-gradient-to-r from-violet-500/10 to-blue-500/10 shadow-lg">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-lg">
            <Sparkles className="mr-2 h-5 w-5 text-violet-500" />
            AI-Powered Analytics
          </CardTitle>
          <CardDescription className="text-sm">
            Advanced insights and personalized recommendations powered by artificial intelligence
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Smart Insights Content */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Innovative Metrics */}
        <div className="space-y-4">
          <InnovativeMetrics data={transformedData} />
        </div>

        {/* Focus Pattern Insights */}
        <div className="space-y-4">
          <FocusPatternInsights data={transformedData} />
        </div>
      </div>

      {/* Additional AI Insights Section */}
      <div className="mt-6 grid gap-4 md:grid-cols-3">
        <Card className="border-border bg-card/50 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center">
              <Brain className="mr-2 h-4 w-4 text-violet-500" />
              Productivity Insights
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-1 font-medium text-sm">Peak Performance</h4>
                <p className="text-xs text-muted-foreground">
                  Your productivity peaks during {transformedData.optimalTime} with {transformedData.optimalTimeEfficiency}% efficiency
                </p>
              </div>
              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-1 font-medium text-sm">Flow State</h4>
                <p className="text-xs text-muted-foreground">
                  You achieve {transformedData.flowStateRating.toLowerCase()} flow state most consistently during {transformedData.flowStateTime} sessions
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-border bg-card/50 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center">
              <Sparkles className="mr-2 h-4 w-4 text-amber-500" />
              Optimization Tips
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-1 font-medium text-sm">Best Day</h4>
                <p className="text-xs text-muted-foreground">
                  {transformedData.optimalDay} shows your highest completion rate at {transformedData.optimalDayCompletion}%
                </p>
              </div>
              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-1 font-medium text-sm">Optimal Duration</h4>
                <p className="text-xs text-muted-foreground">
                  {transformedData.optimalDuration} sessions have {transformedData.optimalDurationSuccess}% success rate
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-border bg-card/50 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center">
              <Brain className="mr-2 h-4 w-4 text-emerald-500" />
              Performance Metrics
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Productivity Score</span>
                <span className="font-bold text-primary">{transformedData.productivityScore}%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Focus Efficiency</span>
                <span className="font-bold text-emerald-500">{transformedData.focusEfficiency}%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Deep Work Ratio</span>
                <span className="font-bold text-blue-500">{transformedData.deepWorkRatio}%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Goal Achievement</span>
                <span className="font-bold text-violet-500">{transformedData.goalAchievement}%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI Recommendations Section */}
      <Card className="border-border bg-card/50 shadow-lg">
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center">
            <Sparkles className="mr-2 h-4 w-4 text-violet-500" />
            AI Recommendations
          </CardTitle>
          <CardDescription className="text-sm">Personalized suggestions for improvement</CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid gap-3 md:grid-cols-2">
            {transformedData.recommendations.map((recommendation, index) => (
              <div key={index} className="rounded-lg bg-violet-500/5 border border-violet-500/20 p-3">
                <div className="flex items-start">
                  <Sparkles className="mr-2 mt-0.5 h-3 w-3 text-violet-500 shrink-0" />
                  <p className="text-xs text-muted-foreground">{recommendation}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
