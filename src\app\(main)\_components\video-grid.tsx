'use client';

import { usePomodoroStore } from '@/lib/pomodoro-store';
import { useAudioStore } from '@/lib/audio-store';
import Image from 'next/image';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';
import { VideoPlaceholder } from '@/app/timer/_components/video-placeholder';
import { FallbackThumbnail } from '@/app/timer/_components/fallback-thumbnail';
import { useEffect, useState, useCallback, memo, useMemo } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Video } from '@/lib/pomodoro-store';
import Link from 'next/link';

interface VideoGridProps {
  videos: Video[];
  isLoading: boolean;
  error?: string;
}

export function VideoGrid({ videos, isLoading, error }: VideoGridProps) {
  const { selectedVideo, selectVideo } = usePomodoroStore();
  const {
    setSelectedAudiosByMusicTrackId,
    setSelectedAudiosByIds,
    rememberAudioSelections,
    rememberedAudioIds
  } = useAudioStore();

  // Auto-select the first video on mount if no video is selected
  useEffect(() => {
    if (videos.length > 0 && !selectedVideo) {
      selectVideo(videos[0]);

      // Choose between remembered audios or video's music track
      if (rememberAudioSelections && rememberedAudioIds.length > 0) {
        setSelectedAudiosByIds(rememberedAudioIds);
      } else if (videos[0].musicTrackId) {
        setSelectedAudiosByMusicTrackId(videos[0].musicTrackId);
      }
    }
  }, [videos, selectedVideo, selectVideo, setSelectedAudiosByMusicTrackId, setSelectedAudiosByIds, rememberAudioSelections, rememberedAudioIds]);

  // Memoized video selection handler to prevent recreation on each render
  const handleVideoSelect = useCallback((video: Video) => {
    selectVideo(video);

    // If remembering audio selections is enabled, use those instead
    if (rememberAudioSelections && rememberedAudioIds.length > 0) {
      setSelectedAudiosByIds(rememberedAudioIds);
    }
    // Otherwise use video's music track if available
    else if (video.musicTrackId) {
      setSelectedAudiosByMusicTrackId(video.musicTrackId);
    }
  }, [selectVideo, rememberAudioSelections, rememberedAudioIds, setSelectedAudiosByIds, setSelectedAudiosByMusicTrackId]);

  // Memoize loading skeleton grid to prevent recreating on each render
  const LoadingSkeleton = useMemo(() => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1.5 sm:gap-3 md:gap-4 w-full">
      {[1, 2, 3, 4, 5, 6].map((index) => (
        <div key={index} className="rounded-lg border overflow-hidden bg-card/80">
          <AspectRatio ratio={16 / 9}>
            <Skeleton className="w-full h-full" />
          </AspectRatio>
          <div className="p-2 sm:p-3">
            <Skeleton className="w-3/4 h-4 mb-2" />
            <Skeleton className="w-1/2 h-3" />
          </div>
        </div>
      ))}
    </div>
  ), []);

  // Show loading state
  if (isLoading && videos.length === 0) {
    return LoadingSkeleton;
  }

  // Show error state
  if (error && videos.length === 0) {
    return (
      <div className="w-full p-8 text-center">
        <p className="text-red-500 mb-4">Failed to load videos</p>
        <p className="text-sm text-slate-500">Please try refreshing the page</p>
      </div>
    );
  }

  // If there are no videos available, show the placeholder
  if (!videos.length) {
    return <VideoPlaceholder />;
  }

  // Get selected video ID once to avoid repeated object property access in map
  const selectedVideoId = selectedVideo?.id;

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1.5 sm:gap-3 md:gap-4 w-full">
      {videos.map((video) => (
        <MemoizedVideoCard
          key={video.id}
          video={video}
          isSelected={selectedVideoId === video.id}
          onSelect={handleVideoSelect}
        />
      ))}
    </div>
  );
}

interface VideoCardProps {
  video: Video;
  isSelected: boolean;
  onSelect: (video: Video) => void;
}

// Component extraction for better performance and reusability
const StartTimerButton = memo(({ isActive, handleSelect }: {
  isActive: boolean;
  handleSelect: () => void;
}) => (
  <div
    className={cn(
      "absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-30",
      "transition-all duration-300 ease-out",
      "pointer-events-none",
      isActive ? "opacity-100 scale-100" : "opacity-0 scale-90"
    )}
  >
    <Link
      href="/timer"
      className="pointer-events-auto group"
      onClick={(e) => {
        e.stopPropagation();
        handleSelect();
      }}
    >
      <div className={cn(
        // Base styles - simplified and elegant
        "relative flex items-center gap-2",
        "bg-white/95 backdrop-blur-sm",
        "px-3 py-2 rounded-full",
        "text-gray-900 font-medium text-sm",
        "shadow-lg border border-white/20",
        // Simplified hover effects
        "transition-all duration-200 ease-out",
        "hover:bg-white hover:shadow-xl hover:scale-105",
        // Simplified active state
        "active:scale-95",
        // Focus for accessibility
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-white/60"
      )}>
        {/* Play icon */}
        <div className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center">
          <svg
            className="w-2.5 h-2.5 text-white transition-transform duration-200 group-hover:scale-110"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M8 5v14l11-7z" />
          </svg>
        </div>
        
        {/* Button text */}
        <span className="text-gray-900 font-medium">
          Start Timer
        </span>
      </div>
    </Link>
  </div>
));

StartTimerButton.displayName = 'StartTimerButton';

const SelectionIndicator = memo(() => (
  <div className={cn(
    "absolute top-2 right-2 bg-gradient-to-r from-red-500 to-rose-600 text-white",
    "p-1.5 rounded-full shadow-sm z-10 ring-2 ring-white/30"
  )}>
    <Check className="h-3 w-3" />
  </div>
));

SelectionIndicator.displayName = 'SelectionIndicator';

function VideoCard({ video, isSelected, onSelect }: VideoCardProps) {
  // Combine related state to reduce re-renders
  const [cardState, setCardState] = useState({
    imageError: false,
    isLoading: true,
    isHovered: false,
    isFocused: false
  });

  // Reset loading state when video changes
  useEffect(() => {
    setCardState(prev => ({
      ...prev,
      isLoading: true,
      imageError: false
    }));
  }, [video.id]);

  const handleImageError = useCallback(() => {
    setCardState(prev => ({
      ...prev,
      imageError: true,
      isLoading: false
    }));
  }, []);

  const handleImageLoad = useCallback(() => {
    // Using requestAnimationFrame for better performance
    requestAnimationFrame(() => {
      setCardState(prev => ({
        ...prev,
        isLoading: false
      }));
    });
  }, []);

  const handleSelect = useCallback(() => {
    onSelect(video);
  }, [video, onSelect]);

  // Memoize isActive to prevent recalculations
  const isActive = cardState.isHovered || cardState.isFocused;

  // Event handlers optimized to use callbacks
  const handleMouseEnter = useCallback(() => {
    setCardState(prev => ({ ...prev, isHovered: true }));
  }, []);

  const handleMouseLeave = useCallback(() => {
    setCardState(prev => ({ ...prev, isHovered: false }));
  }, []);

  const handleFocus = useCallback(() => {
    setCardState(prev => ({ ...prev, isFocused: true }));
  }, []);

  const handleBlur = useCallback(() => {
    setCardState(prev => ({ ...prev, isFocused: false }));
  }, []);

  // Memoize class names for better performance - optimized for scroll performance
  const containerClassName = useMemo(() => cn(
    // Base styles with CSS containment for better performance
    "overflow-hidden cursor-pointer rounded-lg border border-border/50 bg-card/80",
    "transition-all duration-150 ease-out select-none",
    "hover:shadow-md hover:scale-[1.02] focus-visible:scale-[1.02]",
    "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-red-500/50",
    // CSS containment for better scroll performance
    "contain-layout contain-style",
    isSelected ? "ring-2 ring-red-500 ring-offset-1 shadow-md shadow-red-500/10" : ""
  ), [isSelected]);

  const skeletonClassName = useMemo(() => cn(
    "absolute inset-0 bg-muted/50",
    "transition-opacity duration-200",
    cardState.isLoading ? "opacity-100" : "opacity-0 pointer-events-none"
  ), [cardState.isLoading]);

  const imageClassName = useMemo(() => cn(
    "object-cover transition-transform duration-200",
    isActive ? "scale-[1.05]" : "",
    cardState.isLoading ? "opacity-0" : "opacity-100"
  ), [isActive, cardState.isLoading]);

  const infoSectionClassName = useMemo(() => cn(
    "p-2 sm:p-2.5 bg-card/90 border-t border-border/30",
    "flex flex-col items-start gap-1",
    "transition-colors duration-150",
    isActive ? "bg-red-50/50 dark:bg-red-950/20" : ""
  ), [isActive]);

  return (
    <div
      className={containerClassName}
      onClick={handleSelect}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onFocus={handleFocus}
      onBlur={handleBlur}
      tabIndex={0}
      role="button"
      aria-pressed={isSelected}
      aria-label={`Select video: ${video.title}`}
    >
      {/* Thumbnail section */}
      <div className="relative w-full overflow-hidden">
        {/* Loading skeleton */}
        <div className={skeletonClassName}>
          <AspectRatio ratio={16 / 9}>
            <Skeleton className="w-full h-full rounded-t-lg" />
          </AspectRatio>
        </div>

        {/* Image or Fallback */}
        {!cardState.imageError ? (
          <AspectRatio ratio={16 / 9}>
            <Image
              src={video.thumbnail}
              alt={video.title}
              fill
              priority={false}
              loading="lazy"
              sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
              className={imageClassName}
              onError={handleImageError}
              onLoad={handleImageLoad}
            />

            {/* Gradient overlay for better text contrast - simplified */}
            <div className="absolute inset-x-0 bottom-0 h-12 bg-gradient-to-t from-black/50 to-transparent pointer-events-none" />

            {/* Start Timer button */}
            <StartTimerButton isActive={isActive} handleSelect={handleSelect} />

            {/* Selection indicator */}
            {isSelected && <SelectionIndicator />}
          </AspectRatio>
        ) : (
          <div className="relative">
            <FallbackThumbnail title={video.title} />

            {/* Start Timer button for fallback */}
            <StartTimerButton isActive={isActive} handleSelect={handleSelect} />

            {/* Selection indicator for fallback */}
            {isSelected && <SelectionIndicator />}
          </div>
        )}
      </div>

      {/* Info section */}
      <div className={infoSectionClassName}>
        <h3 className="font-medium text-xs sm:text-sm text-foreground line-clamp-2" style={{ fontFamily: 'var(--font-geist-sans)' }}>
          {video.title}
        </h3>
      </div>
    </div>
  );
}

// Memoize the VideoCard component to prevent unnecessary re-renders
const MemoizedVideoCard = memo(VideoCard);