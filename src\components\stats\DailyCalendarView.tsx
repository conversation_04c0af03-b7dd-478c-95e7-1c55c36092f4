"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChevronLeft, ChevronRight, Clock, Info } from "lucide-react"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface Session {
  id: string
  title: string
  type: "focus" | "shortBreak" | "longBreak"
  startTime: string
  endTime: string
  completed: boolean
}

interface DailyCalendarViewProps {
  sessions: Session[]
  date: string
}

export function DailyCalendarView({ sessions, date }: DailyCalendarViewProps) {
  const [selectedDate, setSelectedDate] = useState(date)
  const [selectedSession, setSelectedSession] = useState<Session | null>(null)

  // Format date for display
  const formatDisplayDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric",
    })
  }

  // Navigate to previous day
  const goToPreviousDay = () => {
    const currentDate = new Date(selectedDate)
    currentDate.setDate(currentDate.getDate() - 1)
    setSelectedDate(currentDate.toISOString().split("T")[0])
  }

  // Navigate to next day
  const goToNextDay = () => {
    const currentDate = new Date(selectedDate)
    currentDate.setDate(currentDate.getDate() + 1)
    setSelectedDate(currentDate.toISOString().split("T")[0])
  }

  // Go to today
  const goToToday = () => {
    setSelectedDate(new Date().toISOString().split("T")[0])
  }

  // Generate time slots for the day (30-minute intervals)
  const generateTimeSlots = () => {
    const slots = []
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const formattedHour = hour.toString().padStart(2, "0")
        const formattedMinute = minute.toString().padStart(2, "0")
        slots.push(`${formattedHour}:${formattedMinute}`)
      }
    }
    return slots
  }

  const timeSlots = generateTimeSlots()

  // Get session for a specific time slot
  const getSessionForTimeSlot = (timeSlot: string) => {
    const [hour, minute] = timeSlot.split(":")
    const slotTime = new Date(selectedDate)
    slotTime.setHours(Number.parseInt(hour, 10), Number.parseInt(minute, 10), 0, 0)
    
    const filteredSessions = sessions.filter(session => {
      // Filter sessions for the selected date only
      const sessionDate = new Date(session.startTime).toISOString().split('T')[0]
      return sessionDate === selectedDate
    })

    return filteredSessions.find((session) => {
      const sessionStart = new Date(session.startTime)
      const sessionEnd = new Date(session.endTime)
      
      return slotTime >= sessionStart && slotTime < sessionEnd
    })
  }

  // Calculate session height based on duration
  const getSessionHeight = (session: Session) => {
    const sessionStart = new Date(session.startTime)
    const sessionEnd = new Date(session.endTime)
    
    const start = sessionStart.getHours() * 60 + sessionStart.getMinutes()
    const end = sessionEnd.getHours() * 60 + sessionEnd.getMinutes()
    
    // Each 30 minutes is 60px height
    return ((end - start) / 30) * 60
  }

  // Calculate session top position
  const getSessionTopPosition = (session: Session) => {
    const sessionStart = new Date(session.startTime)
    const minutesSinceMidnight = sessionStart.getHours() * 60 + sessionStart.getMinutes()
    
    // Each 30 minutes is 60px height
    return (minutesSinceMidnight / 30) * 60
  }

  // Get session background color
  const getSessionColor = (session: Session) => {
    if (session.type === "focus") {
      return session.completed ? "bg-rose-500/80" : "bg-rose-500/40"
    } else if (session.type === "shortBreak") {
      return session.completed ? "bg-emerald-500/80" : "bg-emerald-500/40"
    } else {
      return session.completed ? "bg-blue-500/80" : "bg-blue-500/40"
    }
  }

  // Get session border color
  const getSessionBorderColor = (session: Session) => {
    if (session.type === "focus") {
      return session.completed ? "border-rose-600" : "border-rose-400"
    } else if (session.type === "shortBreak") {
      return session.completed ? "border-emerald-600" : "border-emerald-400"
    } else {
      return session.completed ? "border-blue-600" : "border-blue-400"
    }
  }

  // Format time for display (12-hour format)
  const formatTime = (timeStr: string) => {
    // If it's a time string like "HH:MM" format
    if (timeStr.includes(":") && !timeStr.includes("T")) {
      const [hour, minute] = timeStr.split(":")
      const hourNum = Number.parseInt(hour, 10)
      const ampm = hourNum >= 12 ? "PM" : "AM"
      const hour12 = hourNum % 12 || 12
      return `${hour12}:${minute} ${ampm}`
    } 
    // If it's an ISO date string
    else {
      const date = new Date(timeStr)
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }
  }

  // Get unique sessions (no duplicates across time slots)
  const getUniqueSessions = () => {
    // Filter sessions for the selected date only
    const filteredSessions = sessions.filter(session => {
      const sessionDate = new Date(session.startTime).toISOString().split('T')[0]
      return sessionDate === selectedDate
    })
    
    const uniqueSessions: Session[] = []
    const sessionIds = new Set()

    filteredSessions.forEach((session) => {
      if (!sessionIds.has(session.id)) {
        uniqueSessions.push(session)
        sessionIds.add(session.id)
      }
    })

    return uniqueSessions
  }

  const uniqueSessions = getUniqueSessions()

  return (
    <Card className="border-border bg-card/50 shadow-lg">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle>Daily Calendar View</CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={goToPreviousDay} className="h-8 w-8 p-0">
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={goToToday} className="h-8 text-xs">
              Today
            </Button>
            <Button variant="outline" size="sm" onClick={goToNextDay} className="h-8 w-8 p-0">
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <CardDescription>{formatDisplayDate(selectedDate)}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex">
          {/* Time column */}
          <div className="w-16 flex-shrink-0 pr-2">
            {timeSlots
              .filter((_, index) => index % 2 === 0)
              .map((timeSlot, index) => (
                <div key={timeSlot} className="relative h-[60px]">
                  <div className="absolute -top-3 right-2 text-xs text-muted-foreground">{formatTime(timeSlot)}</div>
                </div>
              ))}
          </div>

          {/* Calendar grid */}
          <div className="relative flex-1 overflow-hidden rounded border border-border">
            {/* Horizontal time lines */}
            {timeSlots.map((timeSlot, index) => (
              <div
                key={timeSlot}
                className={`absolute left-0 right-0 border-t border-border ${
                  index % 2 === 0 ? "border-opacity-100" : "border-opacity-50"
                }`}
                style={{ top: `${index * 30}px` }}
              />
            ))}

            {/* Session blocks */}
            {uniqueSessions.map((session) => (
              <div
                key={session.id}
                className={`absolute left-1 right-1 rounded border p-2 shadow-md transition-opacity hover:opacity-90 ${getSessionColor(
                  session,
                )} ${getSessionBorderColor(session)}`}
                style={{
                  top: `${getSessionTopPosition(session)}px`,
                  height: `${getSessionHeight(session)}px`,
                }}
                onClick={() => setSelectedSession(session)}
              >
                <div className="flex h-full flex-col justify-between overflow-hidden">
                  <div className="flex items-center justify-between">
                    <span className="truncate text-xs font-medium text-white">
                      {session.type === "focus"
                        ? "Focus Session"
                        : session.type === "shortBreak"
                          ? "Short Break"
                          : "Long Break"}
                    </span>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button className="rounded-full bg-white/20 p-0.5">
                            <Info className="h-3 w-3 text-white" />
                          </button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{session.completed ? "Completed" : "Interrupted"}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  {getSessionHeight(session) >= 50 && (
                    <div className="text-xs text-white/80">
                      {formatTime(session.startTime)} - {formatTime(session.endTime)}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Session details */}
        {selectedSession && (
          <div className="mt-4 rounded-lg border border-slate-700 bg-slate-800/50 p-4">
            <div className="mb-2 flex items-center justify-between">
              <h3 className="font-medium">
                {selectedSession.type === "focus"
                  ? "Focus Session"
                  : selectedSession.type === "shortBreak"
                    ? "Short Break"
                    : "Long Break"}
              </h3>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 text-xs text-slate-400"
                onClick={() => setSelectedSession(null)}
              >
                Close
              </Button>
            </div>
            <div className="grid gap-2 text-sm">
              <div className="flex items-center">
                <Clock className="mr-2 h-4 w-4 text-slate-400" />
                <span>
                  {formatTime(selectedSession.startTime)} - {formatTime(selectedSession.endTime)}
                </span>
              </div>
              <div className="flex items-center">
                <div
                  className={`mr-2 h-3 w-3 rounded-full ${
                    selectedSession.type === "focus"
                      ? "bg-rose-500"
                      : selectedSession.type === "shortBreak"
                        ? "bg-emerald-500"
                        : "bg-blue-500"
                  }`}
                />
                <span>
                  {selectedSession.type === "focus"
                    ? "Focus Session"
                    : selectedSession.type === "shortBreak"
                      ? "Short Break"
                      : "Long Break"}
                </span>
              </div>
              <div className="flex items-center">
                <div
                  className={`mr-2 h-3 w-3 rounded-full ${
                    selectedSession.completed ? "bg-emerald-500" : "bg-amber-500"
                  }`}
                />
                <span>{selectedSession.completed ? "Completed" : "Interrupted"}</span>
              </div>
            </div>
          </div>
        )}

        {/* Legend */}
        <div className="mt-4 flex flex-wrap items-center justify-end gap-4">
          <div className="flex items-center">
            <div className="mr-2 h-3 w-3 rounded-full bg-primary"></div>
            <span className="text-xs text-muted-foreground">Focus</span>
          </div>
          <div className="flex items-center">
            <div className="mr-2 h-3 w-3 rounded-full bg-emerald-500"></div>
            <span className="text-xs text-muted-foreground">Short Break</span>
          </div>
          <div className="flex items-center">
            <div className="mr-2 h-3 w-3 rounded-full bg-blue-500"></div>
            <span className="text-xs text-muted-foreground">Long Break</span>
          </div>
          <div className="flex items-center">
            <div className="mr-2 h-3 w-3 rounded-full border border-border bg-background/50"></div>
            <span className="text-xs text-muted-foreground">Interrupted</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
