"use client"

import { useTaskStore } from "@/store/taskStore"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Bar, BarChart, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"
import { subDays, format, startOfWeek, addDays, isWithinInterval } from "date-fns"

interface CustomTooltipProps {
  active?: boolean
  payload?: Array<{ name: string; value: number; color: string }>
  label?: string
}

export function TaskCompletionChart() {
  const { tasks } = useTaskStore()

  // Get the start of the current week
  const startOfCurrentWeek = startOfWeek(new Date(), { weekStartsOn: 1 })

  // Get the start of the previous week
  const startOfPreviousWeek = subDays(startOfCurrentWeek, 7)

  // Generate data for the last 7 days
  const generateWeekData = (startDate: Date) => {
    const weekData = []

    for (let i = 0; i < 7; i++) {
      const currentDate = addDays(startDate, i)
      const dayStr = format(currentDate, "EEE")

      // Count completed tasks for this day
      const completedCount = tasks.filter((task) => {
        if (task.status !== "completed") return false
        const taskDate = new Date(task.updatedAt)
        return isWithinInterval(taskDate, {
          start: new Date(currentDate.setHours(0, 0, 0, 0)),
          end: new Date(currentDate.setHours(23, 59, 59, 999)),
        })
      }).length

      weekData.push({
        name: dayStr,
        completed: completedCount,
      })
    }

    return weekData
  }

  // Generate data for current and previous week
  const currentWeekData = generateWeekData(startOfCurrentWeek)
  const previousWeekData = generateWeekData(startOfPreviousWeek)

  // Combine data for comparison
  const comparisonData = currentWeekData.map((day, index) => ({
    name: day.name,
    "This Week": day.completed,
    "Last Week": previousWeekData[index].completed,
  }))

  // Calculate totals
  const currentWeekTotal = currentWeekData.reduce((sum, day) => sum + day.completed, 0)
  const previousWeekTotal = previousWeekData.reduce((sum, day) => sum + day.completed, 0)
  const weekOverWeekChange =
    previousWeekTotal === 0 ? 100 : Math.round(((currentWeekTotal - previousWeekTotal) / previousWeekTotal) * 100)

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-md border border-border bg-card p-3 shadow-md">
          <p className="mb-1 font-medium">{label}</p>
          {payload.map((entry, index) => (
            <p key={`tooltip-${index}`} style={{ color: entry.color }}>
              <span className="font-medium">{entry.name}: </span>
              <span className="font-bold">{entry.value}</span> tasks
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <Card className="border-border bg-card/50 shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Task Completion</CardTitle>
            <CardDescription>Weekly comparison</CardDescription>
          </div>
          <div className="text-right">
            <p className="text-sm font-medium text-muted-foreground">Week over Week</p>
            <p className={`text-lg font-bold ${weekOverWeekChange >= 0 ? "text-emerald-500" : "text-primary"}`}>
              {weekOverWeekChange >= 0 ? "+" : ""}
              {weekOverWeekChange}%
            </p>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={comparisonData} margin={{ top: 10, right: 10, left: 0, bottom: 20 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" vertical={false} />
              <XAxis 
                dataKey="name" 
                stroke="currentColor" 
                className="text-muted-foreground" 
                tickLine={false} 
                axisLine={false} 
                tick={{ fontSize: 12 }} 
              />
              <YAxis 
                stroke="currentColor" 
                className="text-muted-foreground" 
                tickLine={false} 
                axisLine={false} 
                tick={{ fontSize: 12 }} 
                width={30} 
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend
                wrapperStyle={{ paddingTop: 10 }}
                formatter={(value) => <span className="text-xs text-muted-foreground">{value}</span>}
              />
              <Bar
                dataKey="This Week"
                name="This Week"
                fill="#5576F5"
                radius={[4, 4, 0, 0]}
              />
              <Bar dataKey="Last Week" name="Last Week" fill="#36A3F2" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}
