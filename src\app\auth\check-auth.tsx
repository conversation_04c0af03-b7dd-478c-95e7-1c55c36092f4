"use client";

import { useSession } from "@/server/auth/auth-client";
import { useUserStore, userStoreHelpers } from "@/store/userStore";
import { useEffect, useCallback, useRef } from "react";

export function CheckAuth() {
    const { data: session, isPending, error: sessionError } = useSession();
    const {
        user,
        session: storedSession,
        isAuthenticated,
        isSessionValid,
        setLoading,
        setError,
        clearUser
    } = useUserStore();

    // Track previous session state to detect changes
    const prevSessionRef = useRef<typeof session>(null);
    const prevIsPendingRef = useRef<boolean>(isPending);

    // Enhanced user store clearing function
    const clearUserStore = useCallback(() => {
        console.log("🔄 Clearing user store - user logged out or session invalid");
        clearUser();
        setError(null);
        setLoading(false);
    }, [clearUser, setError, setLoading]);

    // Validate and sync authentication state
    const validateAndSyncAuth = useCallback(() => {
        // Handle session loading state
        if (isPending) {
            setLoading(true);
            return;
        }

        setLoading(false);

        // Handle session errors
        if (sessionError) {
            console.error("❌ Session error detected:", sessionError);
            setError("Authentication error occurred");
            clearUserStore();
            return;
        }

        // Case 1: Valid session exists
        if (session?.user && session?.session) {
            try {
                // Check if this is a new session or session has changed
                const sessionChanged =
                    !prevSessionRef.current ||
                    prevSessionRef.current?.session?.id !== session.session.id ||
                    prevSessionRef.current?.user?.id !== session.user.id;

                if (sessionChanged) {
                    // console.log("✅ Valid session detected, initializing user store");
                    userStoreHelpers.initializeFromSessionData(session);
                    setError(null);
                } else {
                    // Session exists but hasn't changed, just validate stored session
                    if (storedSession && !isSessionValid()) {
                        console.log("⚠️ Stored session expired, clearing user store");
                        clearUserStore();
                    }
                }
            } catch (error) {
                console.error("❌ Error initializing user session:", error);
                setError("Failed to initialize user session");
                clearUserStore();
            }
        }
        // Case 2: No session or invalid session
        else {
            // Only clear if we previously had a session or if user store has data
            if (isAuthenticated || user || storedSession) {
                console.log("🚪 No valid session found, clearing user store");
                clearUserStore();
            }
        }

        // Update refs for next comparison
        prevSessionRef.current = session;
        prevIsPendingRef.current = isPending;
    }, [
        session,
        isPending,
        sessionError,
        user,
        storedSession,
        isAuthenticated,
        isSessionValid,
        setLoading,
        setError,
        clearUserStore
    ]);

    // Main effect for authentication state management
    useEffect(() => {
        validateAndSyncAuth();
    }, [validateAndSyncAuth]);

    // Additional effect to periodically check session validity
    useEffect(() => {
        if (!isAuthenticated || !storedSession) return;

        const checkSessionValidity = () => {
            if (!isSessionValid()) {
                console.log("⏰ Session expired during periodic check, clearing user store");
                clearUserStore();
            }
        };

        // Check session validity every 5 minutes
        const intervalId = setInterval(checkSessionValidity, 5 * 60 * 1000);

        return () => clearInterval(intervalId);
    }, [isAuthenticated, storedSession, isSessionValid, clearUserStore]);

    // Effect to handle browser visibility change (user comes back to tab)
    useEffect(() => {
        const handleVisibilityChange = () => {
            if (document.visibilityState === 'visible' && isAuthenticated) {
                // Re-validate session when user returns to the tab
                if (!isSessionValid()) {
                    console.log("👁️ Tab became visible, session invalid, clearing user store");
                    clearUserStore();
                }
            }
        };

        document.addEventListener('visibilitychange', handleVisibilityChange);
        return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
    }, [isAuthenticated, isSessionValid, clearUserStore]);

    // This component doesn't render anything - it's just for auth state management
    return null;
}

