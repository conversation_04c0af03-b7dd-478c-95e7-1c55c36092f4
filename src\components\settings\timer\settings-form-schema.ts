import * as z from 'zod';

// Form schema for timer settings validation
export const timerFormSchema = z.object({
  pomodoroMinutes: z.coerce
    .number()
    .min(1, { message: 'Must be at least 1 minute' })
    .max(999, { message: 'Must be at most 999 minutes' }),
  shortBreakMinutes: z.coerce
    .number()
    .min(1, { message: 'Must be at least 1 minute' })
    .max(999, { message: 'Must be at most 999 minutes' }),
  longBreakMinutes: z.coerce
    .number()
    .min(1, { message: 'Must be at least 1 minute' })
    .max(999, { message: 'Must be at most 999 minutes' }),
  sessionsBeforeLongBreak: z.coerce
    .number()
    .min(1, { message: 'Must be at least 1 session' })
    .max(10, { message: 'Must be at most 10 sessions' }),
  timerMode: z.enum(['countDown', 'countUp'], {
    required_error: 'Timer mode is required',
  }),
});

export type TimerFormValues = z.infer<typeof timerFormSchema>; 