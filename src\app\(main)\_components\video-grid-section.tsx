'use client';

import { VideoGrid } from '@/app/(main)/_components/video-grid';
import { Video } from '@/lib/pomodoro-store';
import { useState, useEffect, useRef, useMemo, memo } from 'react';
import { Video as VideoIcon } from 'lucide-react';
import { ErrorBoundary } from '@/components/error-boundary';

interface VideoGridSectionProps {
  videos: Video[];
}

// Background elements extracted as a separate memoized component - now static
const BackgroundElements = memo(() => null);

BackgroundElements.displayName = 'BackgroundElements';

// SectionHeader extracted as a separate memoized component - now static
const SectionHeader = memo(() => (
  <div className="text-center mb-4 md:mb-8 px-1 md:px-0">
    <div className="inline-flex items-center gap-2 px-3 py-1.5 mb-2 md:mb-4 rounded-full bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-950/30 dark:to-red-950/30 text-orange-600 dark:text-orange-400 text-sm font-medium border border-orange-200/60 dark:border-orange-800/40">
      <VideoIcon className="h-4 w-4" />
      <span>Focus Environments</span>
    </div>

    <h2
      className="text-lg md:text-3xl lg:text-4xl font-bold tracking-tight text-foreground mb-1 md:mb-3"
      style={{
        fontFamily: 'var(--font-geist-sans)',
        letterSpacing: '-0.025em',
      }}
    >
      <span className="bg-clip-text text-transparent bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 dark:from-slate-100 dark:via-slate-200 dark:to-slate-300">
        Choose Your{' '}
      </span>
      <span className="bg-clip-text text-transparent bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 font-extrabold">
        Focus Environment
      </span>
    </h2>

    <p
      className="text-xs md:text-base text-muted-foreground max-w-2xl mx-auto leading-relaxed px-2 md:px-0"
      style={{ fontFamily: 'var(--font-geist-sans)' }}
    >
      Select a calming background to enhance your productivity and minimize distractions
    </p>
  </div>
));

SectionHeader.displayName = 'SectionHeader';

// EmptyStateMessage as a separate memoized component - now static
const EmptyStateMessage = memo(() => (
  <div className="text-center py-10">
    <p className="text-muted-foreground text-base" style={{ fontFamily: 'var(--font-geist-sans)' }}>
      No videos available at the moment. Please check back soon!
    </p>
  </div>
));

EmptyStateMessage.displayName = 'EmptyStateMessage';

const VideoGridSectionComponent = ({ videos }: VideoGridSectionProps) => {
  const [isMounted, setIsMounted] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  // Use effect to set mounted state
  useEffect(() => {
    try {
      setIsMounted(true);
    } catch (error) {
      console.error('Error setting mounted state in VideoGridSection:', error);
    }
  }, []);

  // Memoize the empty state to prevent unnecessary re-renders
  const emptyState = useMemo(() => {
    try {
      return isMounted && videos.length === 0 && <EmptyStateMessage />;
    } catch (error) {
      console.error('Error creating empty state:', error);
      return null;
    }
  }, [isMounted, videos.length]);

  return (
    <section
      className="w-full py-4 md:py-14 bg-gradient-to-b from-muted/10 via-background to-muted/10 border-t border-muted/30 relative overflow-hidden"
      data-section="video-grid-section"
      id="video-grid-section"
    >
      <div className="container mx-auto px-2 md:px-4 max-w-7xl relative z-10" ref={ref}>
        <SectionHeader />

        {/* Video grid - now static */}
        <div className="min-h-[200px] md:min-h-[500px] px-1 md:px-0">
          <VideoGrid
            videos={videos}
            isLoading={!isMounted}
            error={undefined}
          />

          {emptyState}
        </div>
      </div>
    </section>
  );
};

// Wrap with error boundary for iOS safety
export const VideoGridSection = ({ videos }: VideoGridSectionProps) => {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('VideoGridSection error:', {
          error: error.message,
          componentStack: errorInfo.componentStack,
          videosCount: videos?.length || 0,
          userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'unknown'
        });
      }}
    >
      <VideoGridSectionComponent videos={videos} />
    </ErrorBoundary>
  );
};