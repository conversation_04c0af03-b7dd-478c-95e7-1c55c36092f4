"use client"

import { useTaskStore } from "@/store/taskStore"
import { useStatsStore } from "@/store/statsStore"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Brain, Clock, Lightbulb, Target } from "lucide-react"

export function TaskFocusInsights() {
  const { tasks } = useTaskStore()
  const { todayCompleted, focusTimeToday } = useStatsStore()

  // Calculate insights
  const completedTasks = tasks.filter((task) => task.status === "completed")
  const totalPomodoros = completedTasks.reduce((sum, task) => sum + task.pomodoroCount, 0)

  // Calculate average focus time per task
  const avgFocusTimePerTask = completedTasks.length > 0 ? Math.round((totalPomodoros * 25) / completedTasks.length) : 0

  // Calculate most productive day based on completed pomodoros
  const dayNames = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
  const dayStats = dayNames.map((day) => {
    const count = completedTasks.filter((task) => {
      const taskDate = new Date(task.updatedAt)
      return dayNames[taskDate.getDay()] === day
    }).length

    return { day, count }
  })

  const mostProductiveDay = [...dayStats].sort((a, b) => b.count - a.count)[0]

  // Calculate task completion efficiency (completed tasks / total focus sessions)
  const taskCompletionEfficiency =
    todayCompleted > 0
      ? Math.round(
          (completedTasks.filter((task) => {
            const taskDate = new Date(task.updatedAt)
            const today = new Date()
            return taskDate.toDateString() === today.toDateString()
          }).length /
            todayCompleted) *
            100,
        )
      : 0

  // Generate personalized insights
  const generateInsight = () => {
    if (completedTasks.length === 0) {
      return "Complete your first task to start generating personalized insights."
    }

    const insights = [
      `You're most productive on ${mostProductiveDay.day}s, with ${mostProductiveDay.count} completed tasks.`,
      `On average, you spend ${avgFocusTimePerTask} minutes of focused time per completed task.`,
      `Your task completion efficiency today is ${taskCompletionEfficiency}%.`,
      `You've completed ${completedTasks.length} tasks using ${totalPomodoros} pomodoros.`,
    ]

    // Add more specific insights based on data patterns
    if (taskCompletionEfficiency < 50 && todayCompleted > 3) {
      insights.push(
        "Consider breaking down your tasks into smaller, more manageable pieces to improve completion rate.",
      )
    }

    if (avgFocusTimePerTask > 60) {
      insights.push(
        "Your tasks tend to require significant focus time. Consider scheduling dedicated deep work blocks.",
      )
    }

    // Return a random insight
    return insights[Math.floor(Math.random() * insights.length)]
  }

  return (
    <Card className="border-border bg-card/50 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Brain className="mr-2 h-5 w-5 text-violet-500" /> Task Focus Insights
        </CardTitle>
        <CardDescription>Personalized productivity analysis</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="rounded-lg bg-accent/30 p-4">
          <div className="mb-2 flex items-center">
            <Lightbulb className="mr-2 h-5 w-5 text-amber-500" />
            <h3 className="font-medium">Daily Insight</h3>
          </div>
          <p className="text-sm text-muted-foreground">{generateInsight()}</p>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div className="rounded-lg bg-accent/30 p-4">
            <div className="mb-2 flex items-center">
              <Clock className="mr-2 h-5 w-5 text-emerald-500" />
              <h3 className="font-medium">Focus Distribution</h3>
            </div>
            <div className="mt-2 space-y-2">
              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">Task Focus</span>
                  <span className="text-xs font-medium">{totalPomodoros * 25} min</span>
                </div>
                <div className="h-1.5 w-full rounded-full bg-muted">
                  <div
                    className="h-1.5 rounded-full bg-emerald-500"
                    style={{ width: `${Math.min(100, ((totalPomodoros * 25) / (focusTimeToday || 1)) * 100)}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">Other Focus</span>
                  <span className="text-xs font-medium">{Math.max(0, focusTimeToday - totalPomodoros * 25)} min</span>
                </div>
                <div className="h-1.5 w-full rounded-full bg-muted">
                  <div
                    className="h-1.5 rounded-full bg-blue-500"
                    style={{
                      width: `${Math.min(100, (Math.max(0, focusTimeToday - totalPomodoros * 25) / (focusTimeToday || 1)) * 100)}%`,
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <div className="rounded-lg bg-accent/30 p-4">
            <div className="mb-2 flex items-center">
              <Target className="mr-2 h-5 w-5 text-primary" />
              <h3 className="font-medium">Task Efficiency</h3>
            </div>
            <div className="flex flex-col items-center">
              <div className="relative flex h-24 w-24 items-center justify-center">
                <svg className="h-full w-full" viewBox="0 0 100 100">
                  <circle cx="50" cy="50" r="45" fill="none" stroke="hsl(var(--muted))" strokeWidth="10" />
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    fill="none"
                    stroke="hsl(var(--primary))"
                    strokeWidth="10"
                    strokeDasharray={`${taskCompletionEfficiency * 2.83} 283`}
                    strokeDashoffset="0"
                    transform="rotate(-90 50 50)"
                  />
                </svg>
                <div className="absolute text-xl font-bold">{taskCompletionEfficiency}%</div>
              </div>
              <p className="mt-2 text-center text-xs text-muted-foreground">Task completion efficiency</p>
            </div>
          </div>
        </div>

        <div className="rounded-lg border border-violet-500/20 bg-violet-500/5 p-4">
          <h3 className="mb-2 font-medium text-violet-400">Recommendations</h3>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li className="flex items-start">
              <span className="mr-2 mt-1 h-1.5 w-1.5 rounded-full bg-violet-500"></span>
              {avgFocusTimePerTask > 50
                ? "Break down complex tasks into smaller subtasks for better focus and tracking."
                : "Your task size is well-optimized for focused work sessions."}
            </li>
            <li className="flex items-start">
              <span className="mr-2 mt-1 h-1.5 w-1.5 rounded-full bg-violet-500"></span>
              {taskCompletionEfficiency < 70
                ? "Try the 'two-minute rule': if a task takes less than two minutes, do it immediately."
                : "Your task completion efficiency is excellent. Keep up the good work!"}
            </li>
            <li className="flex items-start">
              <span className="mr-2 mt-1 h-1.5 w-1.5 rounded-full bg-violet-500"></span>
              Schedule your most important tasks on {mostProductiveDay.day}, your most productive day.
            </li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
