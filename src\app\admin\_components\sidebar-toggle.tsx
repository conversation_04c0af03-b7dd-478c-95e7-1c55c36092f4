"use client"

import { ChevronRight } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

import { useSidebar } from "./sidebar-context"

interface SidebarToggleProps {
  className?: string
}

export default function SidebarToggle({ className }: SidebarToggleProps) {
  const { isCollapsed, toggleSidebar } = useSidebar()
  
  if (!isCollapsed) {
    return null
  }

  return (
    <div className={cn("absolute left-[60px] top-4 z-10", className)}>
      <Button
        size="icon"
        variant="outline"
        onClick={toggleSidebar}
        className="h-8 w-8 rounded-full"
        aria-label="Expand sidebar"
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  )
} 