// Timer preset configurations for different duration options

export const focusPresets = [
  { value: 15, label: "15m", description: "Brief" },
  { value: 25, label: "25m", description: "Standard", isDefault: true },
  { value: 45, label: "45m", description: "Extended" }
];

export const shortBreakPresets = [
  { value: 3, label: "3m", description: "Brief" },
  { value: 5, label: "5m", description: "Standard", isDefault: true },
  { value: 10, label: "10m", description: "Extended" }
];

export const longBreakPresets = [
  { value: 10, label: "10m", description: "Brief" },
  { value: 15, label: "15m", description: "Standard", isDefault: true },
  { value: 30, label: "30m", description: "Extended" }
];

export const sessionPresets = [
  { value: 2, label: "2", description: "Few" },
  { value: 4, label: "4", description: "Standard", isDefault: true },
  { value: 6, label: "6", description: "Many" }
]; 