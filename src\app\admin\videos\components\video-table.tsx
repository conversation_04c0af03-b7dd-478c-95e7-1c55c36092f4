"use client";

import React, {
  createContext,
  Fragment,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import ReactDOM from "react-dom";
import invariant from "tiny-invariant";
import { useDeleteVideo, useReorderVideos, VideoOrderItem } from "../../../../../prisma/schema/Video/video-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";
import {
  MoreHorizontal,
  CheckCircle2,
  XCircle,
  SparklesIcon,
  PencilIcon,
  Trash2Icon,
  ArrowUpIcon,
  ArrowDownIcon,
  ImageIcon,
  GripVertical,
  Music,
  Waves
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { formatDistanceToNow } from "date-fns";

import { GetVideos_ResponseTypeSuccess } from "@schemas/Video/video-query";

// Drag and drop imports
import { triggerPostMoveFlash } from "@atlaskit/pragmatic-drag-and-drop-flourish/trigger-post-move-flash";
import {
  attachClosestEdge,
  type Edge,
  extractClosestEdge,
} from "@atlaskit/pragmatic-drag-and-drop-hitbox/closest-edge";
import { getReorderDestinationIndex } from "@atlaskit/pragmatic-drag-and-drop-hitbox/util/get-reorder-destination-index";
import * as liveRegion from "@atlaskit/pragmatic-drag-and-drop-live-region";
import { DropIndicator } from "@atlaskit/pragmatic-drag-and-drop-react-drop-indicator/box";
import { combine } from "@atlaskit/pragmatic-drag-and-drop/combine";
import {
  draggable,
  dropTargetForElements,
  type ElementDropTargetEventBasePayload,
  monitorForElements,
} from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { pointerOutsideOfPreview } from "@atlaskit/pragmatic-drag-and-drop/element/pointer-outside-of-preview";
import { setCustomNativeDragPreview } from "@atlaskit/pragmatic-drag-and-drop/element/set-custom-native-drag-preview";
import { reorder } from "@atlaskit/pragmatic-drag-and-drop/reorder";
import { cn } from "@/lib/utils";


type VideoTableProps = {
  videos: GetVideos_ResponseTypeSuccess;
  onEdit: (id: string) => void;
};

// Drag and drop types
type VideoItem = GetVideos_ResponseTypeSuccess[0];
type ItemPosition = 'first' | 'last' | 'middle' | 'only';
type CleanupFn = () => void;
type ItemEntry = { itemId: string; element: HTMLElement };

type ListContextValue = {
  getListLength: () => number;
  registerItem: (entry: ItemEntry) => CleanupFn;
  reorderItem: (args: {
    startIndex: number;
    indexOfTarget: number;
    closestEdgeOfTarget: Edge | null;
  }) => void;
  instanceId: symbol;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onImageClick: (src: string, title: string) => void;
};

const ListContext = createContext<ListContextValue | null>(null);

function useListContext() {
  const listContext = useContext(ListContext);
  invariant(listContext !== null);
  return listContext;
}

const videoKey = Symbol('video');
type VideoData = {
  [videoKey]: true;
  video: VideoItem;
  index: number;
  instanceId: symbol;
};

function getVideoData({
  video,
  index,
  instanceId,
}: {
  video: VideoItem;
  index: number;
  instanceId: symbol;
}): VideoData {
  return {
    [videoKey]: true,
    video,
    index,
    instanceId,
  };
}

function isVideoData(data: Record<string | symbol, unknown>): data is VideoData {
  return data[videoKey] === true;
}

function getItemPosition({ index, videos }: { index: number; videos: VideoItem[] }): ItemPosition {
  if (videos.length === 1) {
    return 'only';
  }

  if (index === 0) {
    return 'first';
  }

  if (index === videos.length - 1) {
    return 'last';
  }

  return 'middle';
}

// Draggable state types
type DraggableState =
  | { type: 'idle' }
  | { type: 'preview'; container: HTMLElement }
  | { type: 'dragging' };

const idleState: DraggableState = { type: 'idle' };
const draggingState: DraggableState = { type: 'dragging' };

// Draggable Video Row Component
function DraggableVideoRow({
  video,
  index,
  position,
}: {
  video: VideoItem;
  index: number;
  position: ItemPosition;
}) {
  const { registerItem, instanceId, onEdit, onDelete, onImageClick } = useListContext();

  const ref = useRef<HTMLDivElement>(null);
  const dragHandleRef = useRef<HTMLButtonElement>(null);

  const [draggableState, setDraggableState] = useState<DraggableState>(idleState);
  const [closestEdge, setClosestEdge] = useState<Edge | null>(null);

  useEffect(() => {
    const element = ref.current;
    const dragHandle = dragHandleRef.current;
    invariant(element);
    invariant(dragHandle);

    const data = getVideoData({ video, index, instanceId });

    function onChange({ source, self }: ElementDropTargetEventBasePayload) {
      const isSource = source.element === dragHandle;
      if (isSource) {
        setClosestEdge(null);
        return;
      }

      const closestEdge = extractClosestEdge(self.data);

      const sourceIndex = source.data.index;
      invariant(typeof sourceIndex === 'number');

      const isItemBeforeSource = index === sourceIndex - 1;
      const isItemAfterSource = index === sourceIndex + 1;

      const isDropIndicatorHidden =
        (isItemBeforeSource && closestEdge === 'bottom') ||
        (isItemAfterSource && closestEdge === 'top');

      if (isDropIndicatorHidden) {
        setClosestEdge(null);
        return;
      }

      setClosestEdge(closestEdge);
    }

    return combine(
      registerItem({ itemId: video.id, element }),
      draggable({
        element: dragHandle,
        getInitialData: () => data,
        onGenerateDragPreview({ nativeSetDragImage }) {
          setCustomNativeDragPreview({
            nativeSetDragImage,
            getOffset: pointerOutsideOfPreview({
              x: '16px',
              y: '8px',
            }),
            render({ container }) {
              setDraggableState({ type: 'preview', container });
              return () => setDraggableState(draggingState);
            },
          });
        },
        onDragStart() {
          setDraggableState(draggingState);
        },
        onDrop() {
          setDraggableState(idleState);
        },
      }),
      dropTargetForElements({
        element,
        canDrop({ source }) {
          return isVideoData(source.data) && source.data.instanceId === instanceId;
        },
        getData({ input }) {
          return attachClosestEdge(data, {
            element,
            input,
            allowedEdges: ['top', 'bottom'],
          });
        },
        onDragEnter: onChange,
        onDrag: onChange,
        onDragLeave() {
          setClosestEdge(null);
        },
        onDrop() {
          setClosestEdge(null);
        },
      }),
    );
  }, [instanceId, video, index, registerItem, onEdit, onDelete, onImageClick]);

  return (
    <Fragment>
      <div
        ref={ref}
        className={cn(
          "relative bg-background border-b border-border",
          draggableState.type === 'dragging' && "opacity-40"
        )}
      >
        <div className="grid grid-cols-[40px_64px_1fr_100px_100px_120px_120px_60px] gap-4 items-center px-4 py-3">
          {/* Drag Handle */}
          <Button
            ref={dragHandleRef}
            variant="ghost"
            size="sm"
            className="cursor-grab active:cursor-grabbing p-1 h-8 w-8 justify-center"
          >
            <GripVertical className="h-4 w-4" />
          </Button>

          {/* Thumbnail */}
          <div
            className="relative w-full h-10 rounded-md overflow-hidden cursor-pointer hover:opacity-80 transition-opacity"
            onClick={() => onImageClick(video.thumbnail, video.title)}
          >
            {video.thumbnail ? (
              <Image
                src={video.thumbnail}
                alt={video.title}
                fill
                className="object-cover"
                sizes="64px"
              />
            ) : (
              <div className="w-full h-full bg-muted flex items-center justify-center">
                <ImageIcon className="h-4 w-4 text-muted-foreground" />
              </div>
            )}
          </div>

          {/* Title */}
          <div className="flex flex-col min-w-0">
            <Link
              href={`/admin/videos/${video.id}`}
              className="truncate max-w-[300px] hover:text-primary hover:underline cursor-pointer transition-colors font-medium"
            >
              {video.title}
            </Link>
            <div className="flex flex-row gap-2 mt-1">
              {video.musicPlaylist && (
                <Badge variant="outline" className="w-fit text-xs flex items-center gap-1">
                  <Music className="h-3 w-3 text-blue-500 flex-shrink-0" />
                  {video.musicPlaylist.name}
                </Badge>
              )}
              {video.naturePlaylist && (
                <Badge variant="outline" className="w-fit text-xs flex items-center gap-1">
                  <Waves className="h-3 w-3 text-green-500 flex-shrink-0" />
                  {video.naturePlaylist.name}
                </Badge>
              )}
            </div>
          </div>

          {/* Status */}
          <div className="flex items-center justify-start min-w-0">
            {video.isPublic ? (
              <>
                <CheckCircle2 className="mr-1 h-4 w-4 text-green-500 flex-shrink-0" />
                <span className="text-sm truncate">Public</span>
              </>
            ) : (
              <>
                <XCircle className="mr-1 h-4 w-4 text-red-500 flex-shrink-0" />
                <span className="text-sm truncate">Private</span>
              </>
            )}
          </div>

          {/* Premium */}
          <div className="flex items-center justify-start min-w-0">
            {video.isPremium ? (
              <>
                <SparklesIcon className="mr-1 h-4 w-4 text-amber-500 flex-shrink-0" />
                <span className="text-sm truncate">Premium</span>
              </>
            ) : (
              <span className="text-sm text-muted-foreground truncate">Free</span>
            )}
          </div>

          {/* Genres */}
          <div className="flex flex-wrap gap-1 min-w-0">
            {video.videoGenre && video.videoGenre.length > 0 ? (
              video.videoGenre.slice(0, 1).map((genre, index) => (
                <Badge key={index} variant="secondary" className="text-xs truncate">
                  {genre.toLowerCase()}
                </Badge>
              ))
            ) : (
              <span className="text-sm text-muted-foreground truncate">No genres</span>
            )}
            {video.videoGenre && video.videoGenre.length > 1 && (
              <Badge variant="secondary" className="text-xs">
                +{video.videoGenre.length - 1}
              </Badge>
            )}
          </div>

          {/* Created */}
          <div className="text-sm text-muted-foreground truncate">
            {formatDistanceToNow(new Date(video.createdAt), { addSuffix: true })}
          </div>

          {/* Actions */}
          <div className="flex justify-center">
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onEdit(video.id)} className="cursor-pointer">
                  <PencilIcon className="mr-2 h-4 w-4" />
                  <span>Edit</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onDelete(video.id)}
                  className="cursor-pointer text-red-600 focus:text-red-600"
                >
                  <Trash2Icon className="mr-2 h-4 w-4" />
                  <span>Delete</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        {closestEdge && <DropIndicator edge={closestEdge} gap="1px" />}
      </div>
      {draggableState.type === 'preview' &&
        ReactDOM.createPortal(
          <div className="bg-background border rounded-lg p-2 shadow-lg max-w-[300px] truncate">
            {video.title}
          </div>,
          draggableState.container,
        )}
    </Fragment>
  );
}

export function VideoTable({ videos, onEdit }: VideoTableProps) {
  const [deleteVideoId, setDeleteVideoId] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<{ src: string; title: string } | null>(null);
  const deleteVideoMutation = useDeleteVideo();
  const reorderVideosMutation = useReorderVideos();

  // Ensure videos are sorted by order
  const sortedVideos = [...videos].sort((a, b) => a.order - b.order);

  // Drag and drop state
  const [lastVideoMoved, setLastVideoMoved] = useState<{
    video: VideoItem;
    previousIndex: number;
    currentIndex: number;
    numberOfItems: number;
  } | null>(null);

  // Registry for tracking video elements
  const [registry] = useState(() => {
    const registry = new Map<string, HTMLElement>();

    function register({ itemId, element }: ItemEntry) {
      registry.set(itemId, element);
      return function unregister() {
        registry.delete(itemId);
      };
    }

    function getElement(itemId: string): HTMLElement | null {
      return registry.get(itemId) ?? null;
    }

    return { register, getElement };
  });

  // Isolated instances of this component from one another
  const [instanceId] = useState(() => Symbol('instance-id'));

  const handleDelete = () => {
    if (deleteVideoId) {
      deleteVideoMutation.mutate({ id: deleteVideoId });
      setDeleteVideoId(null);
    }
  };

  const handleImageClick = useCallback((src: string, title: string) => {
    setSelectedImage({ src, title });
  }, []);

  const reorderItem = useCallback(
    ({
      startIndex,
      indexOfTarget,
      closestEdgeOfTarget,
    }: {
      startIndex: number;
      indexOfTarget: number;
      closestEdgeOfTarget: Edge | null;
    }) => {
      const finishIndex = getReorderDestinationIndex({
        startIndex,
        closestEdgeOfTarget,
        indexOfTarget,
        axis: 'vertical',
      });

      if (finishIndex === startIndex) {
        // If there would be no change, we skip the update
        return;
      }

      const reorderedVideos = reorder({
        list: sortedVideos,
        startIndex,
        finishIndex,
      });

      // Create video orders for the mutation
      const videoOrders: VideoOrderItem[] = reorderedVideos.map((video, index) => ({
        id: video.id,
        order: index,
      }));

      // Track the moved video for post-move effects
      const movedVideo = sortedVideos[startIndex];
      setLastVideoMoved({
        video: movedVideo,
        previousIndex: startIndex,
        currentIndex: finishIndex,
        numberOfItems: sortedVideos.length,
      });

      reorderVideosMutation.mutate({ videoOrders });
    },
    [sortedVideos, reorderVideosMutation],
  );

  // Monitor for drag and drop events
  useEffect(() => {
    return monitorForElements({
      canMonitor({ source }) {
        return isVideoData(source.data) && source.data.instanceId === instanceId;
      },
      onDrop({ location, source }) {
        const target = location.current.dropTargets[0];
        if (!target) {
          return;
        }

        const sourceData = source.data;
        const targetData = target.data;
        if (!isVideoData(sourceData) || !isVideoData(targetData)) {
          return;
        }

        const indexOfTarget = sortedVideos.findIndex((video) => video.id === targetData.video.id);
        if (indexOfTarget < 0) {
          return;
        }

        const closestEdgeOfTarget = extractClosestEdge(targetData);

        reorderItem({
          startIndex: sourceData.index,
          indexOfTarget,
          closestEdgeOfTarget,
        });
      },
    });
  }, [instanceId, sortedVideos, reorderItem]);

  // Post-move effects
  useEffect(() => {
    if (lastVideoMoved === null) {
      return;
    }

    const { video, previousIndex, currentIndex, numberOfItems } = lastVideoMoved;
    const element = registry.getElement(video.id);
    if (element) {
      triggerPostMoveFlash(element);
    }

    liveRegion.announce(
      `You've moved ${video.title} from position ${
        previousIndex + 1
      } to position ${currentIndex + 1} of ${numberOfItems}.`,
    );

    // Clear the moved video state
    setLastVideoMoved(null);
  }, [lastVideoMoved, registry]);

  // Cleanup live region when component unmounts
  useEffect(() => {
    return function cleanup() {
      liveRegion.cleanup();
    };
  }, []);

  const getListLength = useCallback(() => sortedVideos.length, [sortedVideos.length]);

  const contextValue: ListContextValue = useMemo(() => {
    return {
      registerItem: registry.register,
      reorderItem,
      instanceId,
      getListLength,
      onEdit,
      onDelete: setDeleteVideoId,
      onImageClick: handleImageClick,
    };
  }, [registry.register, reorderItem, instanceId, getListLength, onEdit, handleImageClick]);

  return (
    <>
      <ListContext.Provider value={contextValue}>
        <div className="rounded-md border">
          {/* Header */}
          <div className="grid grid-cols-[40px_64px_1fr_100px_100px_120px_120px_60px] gap-4 items-center px-4 py-3 bg-muted/50 border-b font-medium text-sm">
            <div></div> {/* Drag handle column */}
            <div>Video</div> {/* Thumbnail column */}
            <div></div> {/* Title column */}
            <div>Status</div>
            <div>Premium</div>
            <div>Genres</div>
            <div>Created</div>
            <div>Actions</div>
          </div>

          {/* Video List */}
          {sortedVideos.length === 0 ? (
            <div className="p-8 text-center text-muted-foreground">
              No videos found
            </div>
          ) : (
            sortedVideos.map((video, index) => (
              <DraggableVideoRow
                key={video.id}
                video={video}
                index={index}
                position={getItemPosition({ index, videos: sortedVideos })}
              />
            ))
          )}
        </div>
      </ListContext.Provider>

      {/* Delete confirmation dialog */}
      <AlertDialog open={!!deleteVideoId} onOpenChange={(open) => !open && setDeleteVideoId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the video.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 focus:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Image expansion dialog */}
      <Dialog open={!!selectedImage} onOpenChange={(open) => !open && setSelectedImage(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Video Thumbnail</DialogTitle>
            <DialogDescription>
              {selectedImage?.title}
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4 overflow-hidden rounded-lg">
            {selectedImage && (
              <div className="relative w-full aspect-video">
                <Image
                  src={selectedImage.src}
                  alt={selectedImage.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 1200px) 100vw, 1200px"
                />
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}