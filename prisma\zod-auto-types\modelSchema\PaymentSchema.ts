import { z } from 'zod';
import { JsonValueSchema } from '../inputTypeSchemas/JsonValueSchema'
import { PaymentStatusSchema } from '../inputTypeSchemas/PaymentStatusSchema'
import { SubscriptionTypeSchema } from '../inputTypeSchemas/SubscriptionTypeSchema'
import type { JsonValueType } from '../inputTypeSchemas/JsonValueSchema';
import { SubscriptionWithRelationsSchema, SubscriptionPartialWithRelationsSchema } from './SubscriptionSchema'
import type { SubscriptionWithRelations, SubscriptionPartialWithRelations } from './SubscriptionSchema'
import { UserWithRelationsSchema, UserPartialWithRelationsSchema } from './UserSchema'
import type { UserWithRelations, UserPartialWithRelations } from './UserSchema'

/////////////////////////////////////////
// PAYMENT SCHEMA
/////////////////////////////////////////

export const PaymentSchema = z.object({
  status: PaymentStatusSchema,
  subscriptionType: SubscriptionTypeSchema,
  id: z.string().cuid(),
  subscriptionId: z.string().nullish(),
  userId: z.string().nullish(),
  amount: z.number(),
  currency: z.string(),
  externalId: z.string().nullish(),
  checkoutId: z.string().nullish(),
  productId: z.string().nullish(),
  priceId: z.string().nullish(),
  metadata: JsonValueSchema.nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Payment = z.infer<typeof PaymentSchema>

/////////////////////////////////////////
// PAYMENT PARTIAL SCHEMA
/////////////////////////////////////////

export const PaymentPartialSchema = PaymentSchema.partial()

export type PaymentPartial = z.infer<typeof PaymentPartialSchema>

/////////////////////////////////////////
// PAYMENT RELATION SCHEMA
/////////////////////////////////////////

export type PaymentRelations = {
  subscription?: SubscriptionWithRelations | null;
  user?: UserWithRelations | null;
};

export type PaymentWithRelations = Omit<z.infer<typeof PaymentSchema>, "metadata"> & {
  metadata?: JsonValueType | null;
} & PaymentRelations

export const PaymentWithRelationsSchema: z.ZodType<PaymentWithRelations> = PaymentSchema.merge(z.object({
  subscription: z.lazy(() => SubscriptionWithRelationsSchema).nullish(),
  user: z.lazy(() => UserWithRelationsSchema).nullish(),
}))

/////////////////////////////////////////
// PAYMENT PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type PaymentPartialRelations = {
  subscription?: SubscriptionPartialWithRelations | null;
  user?: UserPartialWithRelations | null;
};

export type PaymentPartialWithRelations = Omit<z.infer<typeof PaymentPartialSchema>, "metadata"> & {
  metadata?: JsonValueType | null;
} & PaymentPartialRelations

export const PaymentPartialWithRelationsSchema: z.ZodType<PaymentPartialWithRelations> = PaymentPartialSchema.merge(z.object({
  subscription: z.lazy(() => SubscriptionPartialWithRelationsSchema).nullish(),
  user: z.lazy(() => UserPartialWithRelationsSchema).nullish(),
})).partial()

export type PaymentWithPartialRelations = Omit<z.infer<typeof PaymentSchema>, "metadata"> & {
  metadata?: JsonValueType | null;
} & PaymentPartialRelations

export const PaymentWithPartialRelationsSchema: z.ZodType<PaymentWithPartialRelations> = PaymentSchema.merge(z.object({
  subscription: z.lazy(() => SubscriptionPartialWithRelationsSchema).nullish(),
  user: z.lazy(() => UserPartialWithRelationsSchema).nullish(),
}).partial())

export default PaymentSchema;
