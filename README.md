# Pomodoro 365

A modern Pomodoro timer application built with Next.js 15, Bun, Prisma, and Tailwind CSS.

## Features

- 🕐 Customizable Pomodoro, short break, and long break timers
- 🎨 Beautiful UI with Tailwind CSS
- 💾 **Local Storage & Cloud Sync**: Works offline, syncs when you sign in
- 📊 **Complete Analytics**: Full statistics available even without account
- 🔔 Enhanced notification system
  - Multiple sound options (Bird, Bell, Digital, Chord, Ding)
  - Adjustable volume control
  - Configurable repeat count for notifications (1-10 repeats)
  - Optimized sound preloading for instant playback
- 🎛️ Timer positioning on screen
- 📏 Resizable timer with presets
- 🔄 **Session Management**: Automatic tracking with interruption detection

## Local Storage & Session Sync System

The Pomodoro timer now features a comprehensive local storage system that provides full functionality even without user authentication:

### For Unauthenticated Users
- ✅ **Complete Offline Functionality**: All timer features work without signing in
- 💾 **Local Session Storage**: All Pomodoro sessions automatically saved to browser storage
- 📈 **Full Statistics**: View detailed analytics including focus time, completion rates, and session history
- 🔄 **Session Tracking**: Records focus sessions, breaks, interruptions, and completion status
- ⏱️ **Minimum Duration Validation**: Only sessions meeting minimum requirements are saved
  - Focus sessions: 5+ minutes
  - Break sessions: 1+ minute

### For Authenticated Users
- ☁️ **Automatic Cloud Sync**: Local sessions automatically transfer to your account upon sign-in
- 🔄 **Bulk Transfer**: All qualifying local sessions uploaded in one operation
- 🚫 **Duplicate Prevention**: Smart filtering prevents duplicate session uploads
- 📱 **Cross-Device Access**: Access your data from any device
- 🗑️ **Local Cleanup**: Local storage cleared after successful sync

### Session Data Structure
Each session captures comprehensive information:
```typescript
{
  startTime: "2024-01-01T10:00:00Z",
  endTime: "2024-01-01T10:25:00Z",
  totalDuration: 1500, // Total elapsed time in seconds
  focusDuration: 1500,  // Active focus time (excludes interruptions)
  intervalType: "FOCUS" | "SHORT_BREAK" | "LONG_BREAK",
  completed: true,      // Whether session completed naturally
  interrupted: false,   // Whether session was manually stopped
  interruptedSessions: [...] // Pause/resume timestamps
}
```

## Notification System

The Pomodoro timer includes a robust notification system that:

- Notifies you when your focus session ends
- Notifies you when your break ends
- Allows you to customize notification sounds
- Supports volume adjustment
- Allows setting how many times a notification sound plays
- **Preloads sounds** when the timer is about to finish (10 seconds before) for immediate playback, enhancing user experience

## Sound Preloading

The notification system is optimized for better user experience:

- Sounds are preloaded 10 seconds before the timer finishes
- Notification tab preloads sounds when opened
- Sound is immediately available when the timer ends
- Repeat playback occurs with a 1-second delay between repeats
- Volume control is applied in real-time

## Stats Popover

The stats popover provides quick access to your productivity metrics:

### For All Users (Authenticated & Unauthenticated)
- 📊 **Today's Focus Time**: Total minutes of focus work completed today
- 🎯 **Completed Sessions**: Number of successfully finished focus sessions
- 📅 **Week Total**: Total focus time for the last 7 days
- 📈 **Completion Rate**: Percentage of started sessions that were completed
- 📝 **Session History**: Recent focus sessions with timestamps and status

### Sync Indicator for Local Users
- 🔄 **Local Data Notice**: Clear indication when viewing local-only data
- 📱 **Cross-Device Sync Prompt**: Encourages sign-in to access data across devices
- ☁️ **One-Click Sign In**: Direct link to authentication for immediate sync

## Getting Started

1. Clone the repository
2. Install dependencies:
   ```
   bun install
   ```
3. Set up environment variables:
   ```
   cp .env.example .env.local
   ```
4. Initialize the database:
   ```
   bun prisma generate
   bun prisma db push
   ```
5. Start the development server:
   ```
   bun dev
   ```
6. Open [http://localhost:2604](http://localhost:2604) with your browser

## API Endpoints

### Session Management
- `POST /api/pomodoro/bulk-transfer` - Transfer local sessions to database
- `GET /api/pomodoro/quick-stats` - Lightweight stats for popover display
- `GET /api/pomodoro/stats` - Comprehensive analytics data

## Tech Stack

- [Next.js 15](https://nextjs.org/)
- [Bun](https://bun.sh/)
- [Prisma](https://www.prisma.io/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Lucide Icons](https://lucide.dev/)
- [Zustand](https://zustand-demo.pmnd.rs/) - State management with persistence
- [TanStack Query](https://tanstack.com/query) - Server state management 