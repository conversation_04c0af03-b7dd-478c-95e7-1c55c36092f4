'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { systemPlayerManager } from './system-player-manager';
import { MusicControlProps } from './types';
import { useAudioStore } from '@/lib/audio-store';

// Hook to connect React components to the global SystemPlayerManager
export function useSystemPlayerManager(playlist: MusicControlProps['playlist']) {
  // Global audio control
  const requestAudioControl = useAudioStore((state) => state.requestAudioControl);
  const releaseAudioControl = useAudioStore((state) => state.releaseAudioControl);
  const shouldAutoPlay = useAudioStore((state) => state.globalAudioControl.shouldAutoPlay);
  
  // Unique player ID for this hook instance
  const playerIdRef = useRef(`system-player-manager-${Math.random().toString(36).substr(2, 9)}`);
  const playerId = playerIdRef.current;
  
  // State from the global manager
  const [state, setState] = useState(() => systemPlayerManager.getState());
  
  // Track if this instance has audio control
  const [hasAudioControl, setHasAudioControl] = useState(false);

  // Update state when the global manager changes
  useEffect(() => {
    const handleStateChange = (event: CustomEvent) => {
      setState(event.detail);
    };

    window.addEventListener('system-player-state-change', handleStateChange as EventListener);
    
    return () => {
      window.removeEventListener('system-player-state-change', handleStateChange as EventListener);
    };
  }, []);

  // Initialize the global manager when playlist changes
  useEffect(() => {
    if (!playlist) return;

    console.log(`useSystemPlayerManager ${playerId}: Requesting audio control for playlist`, playlist.name);
    
    // Request audio control
    const hasControl = requestAudioControl(playerId);
    setHasAudioControl(hasControl);
    
    console.log(`useSystemPlayerManager ${playerId}: Audio control ${hasControl ? 'granted' : 'denied'}`);

    if (hasControl) {
      // Initialize the global manager with this playlist
      systemPlayerManager.initialize(playlist, shouldAutoPlay);
    }

    return () => {
      // Release audio control when component unmounts
      releaseAudioControl(playerId);
      console.log(`useSystemPlayerManager ${playerId}: Released audio control`);
    };
  }, [playlist, playerId, shouldAutoPlay, requestAudioControl, releaseAudioControl]);

  // Control functions - only work if this instance has audio control
  const playMainTrack = useCallback((index: number) => {
    if (hasAudioControl) {
      systemPlayerManager.playMainTrack(index);
    }
  }, [hasAudioControl]);

  const toggleMainPlay = useCallback(() => {
    if (hasAudioControl) {
      systemPlayerManager.toggleMainPlay();
    }
  }, [hasAudioControl]);

  const skipToNext = useCallback(() => {
    if (hasAudioControl) {
      systemPlayerManager.skipToNext();
    }
  }, [hasAudioControl]);

  const skipToPrevious = useCallback(() => {
    if (hasAudioControl) {
      systemPlayerManager.skipToPrevious();
    }
  }, [hasAudioControl]);

  const toggleMainMute = useCallback(() => {
    if (hasAudioControl) {
      systemPlayerManager.toggleMainMute();
    }
  }, [hasAudioControl]);

  const handleMainVolumeChange = useCallback((volume: number) => {
    if (hasAudioControl) {
      systemPlayerManager.setMainVolume(volume);
    }
  }, [hasAudioControl]);

  const togglePlaybackMode = useCallback(() => {
    if (hasAudioControl) {
      systemPlayerManager.togglePlaybackMode();
    }
  }, [hasAudioControl]);

  const handleSeek = useCallback((time: number) => {
    if (hasAudioControl) {
      systemPlayerManager.handleSeek(time);
    }
  }, [hasAudioControl]);

  const toggleNatureSound = useCallback((soundId: string) => {
    if (hasAudioControl) {
      systemPlayerManager.toggleNatureSound(soundId);
    }
  }, [hasAudioControl]);

  const handleNatureSoundVolume = useCallback((soundId: string, volume: number) => {
    if (hasAudioControl) {
      systemPlayerManager.handleNatureSoundVolume(soundId, volume);
    }
  }, [hasAudioControl]);

  const toggleNatureSoundMute = useCallback((soundId: string) => {
    if (hasAudioControl) {
      systemPlayerManager.toggleNatureSoundMute(soundId);
    }
  }, [hasAudioControl]);

  const pauseAll = useCallback(() => {
    if (hasAudioControl) {
      systemPlayerManager.pauseAll();
    }
  }, [hasAudioControl]);

  // Return state and control functions
  return {
    // State
    currentTrack: state.currentTrack,
    currentMusicIndex: state.currentMusicIndex,
    isMainPlaying: state.isMainPlaying,
    mainVolume: state.mainVolume,
    isMainMuted: state.isMainMuted,
    playbackMode: state.playbackMode,
    currentTime: state.currentTime,
    duration: state.duration,
    natureSounds: state.natureSounds,
    playlist: state.playlist,
    isInitialized: state.isInitialized,
    musicCount: state.musicCount,
    playingNatureSoundsCount: systemPlayerManager.getPlayingNatureSoundsCount(),
    
    // Control functions
    playMainTrack,
    toggleMainPlay,
    skipToNext,
    skipToPrevious,
    toggleMainMute,
    handleMainVolumeChange,
    togglePlaybackMode,
    handleSeek,
    toggleNatureSound,
    handleNatureSoundVolume,
    toggleNatureSoundMute,
    pauseAll,
    
    // Control state
    hasAudioControl,
    playerId,
  };
}
