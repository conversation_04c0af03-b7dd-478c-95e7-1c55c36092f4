'use client';

import { useEffect, useRef, useCallback } from 'react';
import { usePathname } from 'next/navigation';
import { useAudioStore } from '@/lib/audio-store';
import { systemPlayerManager } from '@/app/timer/_components/musics-control/system-player-manager';

/**
 * Hook that handles audio cleanup when navigating away from the timer page.
 * Ensures all audio sources are properly stopped and resources are cleaned up.
 */
export function useNavigationAudioCleanup() {
  const pathname = usePathname();
  const previousPathnameRef = useRef<string | null>(null);
  
  // Audio store cleanup functions
  const stopAllAudioSources = useAudioStore((state) => state.stopAllAudioSources);
  const cleanupAllAudioResources = useAudioStore((state) => state.cleanupAllAudioResources);
  const resetAudioState = useAudioStore((state) => state.resetAudioState);

  // Comprehensive audio cleanup function
  const performAudioCleanup = useCallback((reason: string) => {
    console.log(`🔇 Navigation Audio Cleanup: ${reason}`);
    
    try {
      // 1. Stop all audio sources through global control
      stopAllAudioSources();
      
      // 2. Stop system player manager audio
      systemPlayerManager.stopAllForNavigation();
      
      // 3. Clean up audio resources
      cleanupAllAudioResources();
      
      // 4. Reset audio state
      resetAudioState();
      
      console.log('✅ Navigation Audio Cleanup: Completed successfully');
    } catch (error) {
      console.error('❌ Navigation Audio Cleanup: Error during cleanup:', error);
    }
  }, [stopAllAudioSources, cleanupAllAudioResources, resetAudioState]);

  // Detect navigation away from timer page
  useEffect(() => {
    const currentPath = pathname;
    const previousPath = previousPathnameRef.current;
    
    // If we're navigating away from the timer page
    if (previousPath === '/timer' && currentPath !== '/timer') {
      performAudioCleanup(`Navigating from /timer to ${currentPath}`);
    }
    
    // Update the previous pathname
    previousPathnameRef.current = currentPath;
  }, [pathname, performAudioCleanup]);

  // Note: Removed tab visibility change handler to allow music to continue playing
  // when switching tabs. Music will only be paused when navigating away from timer page
  // or when closing/refreshing the page.

  // Cleanup on beforeunload (user closes tab/refreshes)
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (pathname === '/timer') {
        performAudioCleanup('Page unload/refresh');
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [pathname, performAudioCleanup]);

  // Return cleanup function for manual use
  return {
    performAudioCleanup,
    isTimerPage: pathname === '/timer',
  };
}

/**
 * Hook specifically for the timer page component to handle cleanup on unmount
 */
export function useTimerPageAudioCleanup() {
  const { performAudioCleanup } = useNavigationAudioCleanup();

  // Cleanup when timer page component unmounts
  useEffect(() => {
    return () => {
      performAudioCleanup('Timer page component unmount');
    };
  }, [performAudioCleanup]);

  return { performAudioCleanup };
}

/**
 * Hook for components that need to trigger audio cleanup manually
 */
export function useManualAudioCleanup() {
  const stopAllAudioSources = useAudioStore((state) => state.stopAllAudioSources);
  const cleanupAllAudioResources = useAudioStore((state) => state.cleanupAllAudioResources);
  const resetAudioState = useAudioStore((state) => state.resetAudioState);

  const cleanupAudio = useCallback((reason: string = 'Manual cleanup') => {
    console.log(`🔇 Manual Audio Cleanup: ${reason}`);
    
    try {
      // Stop all audio sources
      stopAllAudioSources();
      
      // Stop system player
      systemPlayerManager.stopAllForNavigation();
      
      // Clean up resources
      cleanupAllAudioResources();
      
      // Reset state
      resetAudioState();
      
      console.log('✅ Manual Audio Cleanup: Completed');
    } catch (error) {
      console.error('❌ Manual Audio Cleanup: Error:', error);
    }
  }, [stopAllAudioSources, cleanupAllAudioResources, resetAudioState]);

  const stopAudio = useCallback((reason: string = 'Manual stop') => {
    console.log(`⏸️ Manual Audio Stop: ${reason}`);
    stopAllAudioSources();
    systemPlayerManager.pauseAll();
  }, [stopAllAudioSources]);

  return {
    cleanupAudio,
    stopAudio,
  };
}
