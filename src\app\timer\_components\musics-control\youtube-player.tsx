'use client';

import React, { useEffect } from 'react';
import { Play, Pause, Volume2, VolumeX } from 'lucide-react';
import { useYouTubePlayer } from './use-youtube-player';
import { useAudioContext } from './music-control';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';

interface YouTubePlayerProps {
  className?: string;
  onPlayerRef?: (ref: any) => void;
}

export function YouTubePlayer({ className, onPlayerRef }: YouTubePlayerProps) {
  const audioContext = useAudioContext();

  const {
    playerState,
    togglePlay,
    handleVolumeChange,
    toggleMute,
    handleUrlChange,
    loadVideo,
    pauseVideo,
    isPlayerReady,
  } = useYouTubePlayer({
    onPlayStart: () => {
      // Pause system audio when YouTube video starts playing
      audioContext.pauseSystemAudio();
    }
  });

  // Provide pauseVideo function to parent
  useEffect(() => {
    if (onPlayerRef) {
      onPlayerRef({ pauseVideo });
    }
  }, [onPlayerRef, pauseVideo]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    loadVideo();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      loadVideo();
    }
  };

  const handleExternalLinkClick = () => {
    const url = playerState.inputUrl || playerState.currentVideoId;
    if (url) {
      let youtubeUrl = '';
      if (url.includes('youtube.com') || url.includes('youtu.be')) {
        youtubeUrl = url;
      } else {
        // Assume it's a video ID
        youtubeUrl = `https://www.youtube.com/watch?v=${url}`;
      }
      window.open(youtubeUrl, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Compact Header - matching main player */}
      <div className="flex items-center gap-2">
        <div className="h-5 w-5 bg-gradient-to-br from-red-500/20 to-orange-500/20 rounded flex items-center justify-center">
          <Play className="h-2.5 w-2.5 text-red-500" />
        </div>
        <div>
          <h3 className="text-xs font-medium text-foreground">YouTube</h3>
        </div>
      </div>

      {/* Compact URL Input - matching main player input sizing */}
      <form onSubmit={handleSubmit} className="flex gap-2">
        <div className="relative flex-1">
          <Input
            id="youtube-url"
            type="text"
            value={playerState.inputUrl}
            onChange={(e) => handleUrlChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="YouTube URL or video ID"
            className="w-full h-10 sm:h-8 px-3 py-1 text-xs bg-background border border-border rounded-lg
                      focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary
                      transition-all duration-200 placeholder:text-muted-foreground/60"
          />
        </div>
        <Button
          type="submit"
          size="sm"
          className="rounded-lg cursor-pointer flex items-center justify-center h-10 sm:h-8 px-3 text-xs
                    min-h-[2.5rem] sm:min-h-[2rem] min-w-[4rem] sm:min-w-[3rem]"
        >
          Load
        </Button>
      </form>

      {/* Enhanced YouTube Player Embed */}
      <div className="relative w-full">
        <div className="aspect-video bg-muted/20 rounded-xl overflow-hidden border border-border/50 shadow-lg min-h-[160px] sm:min-h-[180px] lg:min-h-[200px] w-full">
          <div
            id="youtube-player"
            className="w-full h-full"
            style={{
              minHeight: '160px',
              width: '100%',
              height: '100%',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0
            }}
          />
        </div>
        
        {/* Enhanced Loading State - matching main player sizing */}
        {!playerState.currentVideoId && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/95 backdrop-blur-sm rounded-xl border border-dashed border-border/30">
            <div className="text-center space-y-2 p-4">
              <div className="w-12 h-12 mx-auto bg-gradient-to-br from-red-500/10 to-orange-500/10 rounded-xl border border-dashed border-red-500/30 flex items-center justify-center">
                <Play className="h-6 w-6 text-red-500/60" />
              </div>
              <div>
                <p className="text-sm font-medium text-foreground">Ready to Play</p>
                <p className="text-xs text-muted-foreground mt-0.5">Enter a YouTube URL to get started</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Compact Control Panel - matching main player styling */}
      <div className="bg-muted/20 rounded-lg p-2.5 border border-border/50 shadow-sm">
        <div className="flex items-center justify-between gap-2">
          {/* Compact Play Button and Status - matching main player */}
          <div className="flex items-center gap-2">
            <button
              onClick={togglePlay}
              disabled={!playerState.currentVideoId || !isPlayerReady}
              className="flex items-center justify-center w-7 h-7 rounded-full 
                        bg-primary text-primary-foreground hover:bg-primary/90 
                        disabled:bg-muted disabled:text-muted-foreground
                        focus:outline-none focus:ring-2 focus:ring-primary/20 
                        transition-all duration-200 disabled:border-muted"
              aria-label={playerState.isPlaying ? 'Pause' : 'Play'}
            >
              {playerState.isPlaying ? (
                <Pause className="h-3 w-3" />
              ) : (
                <Play className="h-3 w-3 ml-0.5" />
              )}
            </button>
            
            {/* Compact Status - matching main player font sizes */}
            <div className="min-w-0">
              {playerState.isPlaying ? (
                <span className="text-xs font-medium text-green-600">Playing</span>
              ) : isPlayerReady ? (
                <span className="text-xs font-medium text-foreground">Ready</span>
              ) : playerState.currentVideoId ? (
                <span className="text-xs font-medium text-muted-foreground">Loading</span>
              ) : (
                <span className="text-xs font-medium text-muted-foreground">No video</span>
              )}
            </div>
          </div>

          {/* Compact Volume Controls - matching main player */}
          <div className="flex items-center gap-2">
            <button
              onClick={toggleMute}
              className="p-1.5 rounded-lg hover:bg-background/80 focus:outline-none 
                        focus:ring-2 focus:ring-primary/20 transition-all duration-200"
              aria-label={playerState.isMuted ? 'Unmute' : 'Mute'}
            >
              {playerState.isMuted || playerState.volume === 0 ? (
                <VolumeX className="h-3.5 w-3.5 text-muted-foreground" />
              ) : (
                <Volume2 className="h-3.5 w-3.5 text-foreground" />
              )}
            </button>
            
            <div className="flex items-center gap-1.5 w-20">
              <Slider
                value={[playerState.isMuted ? 0 : playerState.volume]}
                onValueChange={(values) => handleVolumeChange(values[0])}
                max={100}
                min={0}
                step={1}
                className="flex-1 [&_[data-slot=slider-track]]:h-[3px] [&_[data-slot=slider-thumb]]:h-3 [&_[data-slot=slider-thumb]]:w-3"
                aria-label="Volume"
              />
              <span className="text-xs text-muted-foreground w-6 text-right font-mono tabular-nums">
                {playerState.isMuted ? 0 : Math.round(playerState.volume)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}