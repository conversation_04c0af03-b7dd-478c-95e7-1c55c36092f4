'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { useUserStore } from '@/store/userStore';

// Local task structure matching the backend schema
export interface LocalTask {
  id: string;
  title: string;
  completed: boolean;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
  // Note: userId will be added when transferring to backend
}

export interface LocalTaskStats {
  totalTasks: number;
  completedTasks: number;
  pendingTasks: number;
  todayCreated: number;
  completionRate: number;
}

interface LocalTaskStore {
  tasks: LocalTask[];
  
  // Actions
  addTask: (task: Omit<LocalTask, 'id' | 'createdAt' | 'updatedAt'>) => LocalTask;
  updateTask: (id: string, updates: Partial<Pick<LocalTask, 'title' | 'completed'>>) => void;
  deleteTask: (id: string) => void;
  clearAllTasks: () => void;
  
  // Queries
  getAllTasks: () => LocalTask[];
  getTaskById: (id: string) => LocalTask | undefined;
  getFilteredTasks: (filters: {
    completed?: boolean;
    searchTerm?: string;
  }) => LocalTask[];
  
  // Stats calculations
  getTaskStats: () => LocalTaskStats;
  
  // Export for transfer
  exportTasks: () => LocalTask[];
  
  // Debug functions
  getTaskCount: () => number;
  debugTasks: () => void;
  clearDuplicates: () => void;
}

// Helper functions
const generateId = () => `local_task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

const isToday = (dateString: string): boolean => {
  const today = new Date().toDateString();
  const taskDate = new Date(dateString).toDateString();
  return today === taskDate;
};

export const useLocalTaskStore = create<LocalTaskStore>()(
  persist(
    (set, get) => ({
      tasks: [],

      addTask: (taskData) => {
        // Check if user is authenticated - if so, don't save to local storage
        const isAuthenticated = useUserStore.getState().isAuthenticated;
        if (isAuthenticated) {
          console.warn('User is authenticated - tasks should be saved to database, not local storage');
          // Return a dummy task to maintain interface compatibility
          return {
            ...taskData,
            id: 'temp_id',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
        }

        const now = new Date().toISOString();
        const newTask: LocalTask = {
          ...taskData,
          id: generateId(),
          createdAt: now,
          updatedAt: now,
        };

        console.log('Adding local task:', {
          id: newTask.id,
          title: newTask.title,
          completed: newTask.completed,
          currentTaskCount: get().tasks.length
        });

        set((state) => ({
          tasks: [...state.tasks, newTask]
        }));

        console.log('Local task added. Total tasks:', get().tasks.length);
        return newTask;
      },

      updateTask: (id, updates) => {
        // Check if user is authenticated - if so, don't update local storage
        const isAuthenticated = useUserStore.getState().isAuthenticated;
        if (isAuthenticated) {
          console.warn('User is authenticated - tasks should be updated in database, not local storage');
          return;
        }

        set((state) => ({
          tasks: state.tasks.map((task) =>
            task.id === id
              ? { ...task, ...updates, updatedAt: new Date().toISOString() }
              : task
          )
        }));

        console.log('Local task updated:', { id, updates });
      },

      deleteTask: (id) => {
        // Check if user is authenticated - if so, don't delete from local storage
        const isAuthenticated = useUserStore.getState().isAuthenticated;
        if (isAuthenticated) {
          console.warn('User is authenticated - tasks should be deleted from database, not local storage');
          return;
        }

        set((state) => ({
          tasks: state.tasks.filter((task) => task.id !== id)
        }));

        console.log('Local task deleted:', id);
      },

      clearAllTasks: () => {
        set({ tasks: [] });
        console.log('All local tasks cleared');
      },

      getAllTasks: () => {
        return get().tasks.sort((a, b) => {
          // Sort by completion status first (incomplete first)
          if (a.completed !== b.completed) {
            return a.completed ? 1 : -1;
          }
          // Then by creation date (newest first)
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        });
      },

      getTaskById: (id) => {
        return get().tasks.find(task => task.id === id);
      },

      getFilteredTasks: (filters) => {
        let tasks = get().tasks;

        // Filter by completion status
        if (filters.completed !== undefined) {
          tasks = tasks.filter(task => task.completed === filters.completed);
        }

        // Filter by search term
        if (filters.searchTerm) {
          const searchLower = filters.searchTerm.toLowerCase();
          tasks = tasks.filter(task =>
            task.title.toLowerCase().includes(searchLower)
          );
        }

        // Sort results
        return tasks.sort((a, b) => {
          // Sort by completion status first (incomplete first)
          if (a.completed !== b.completed) {
            return a.completed ? 1 : -1;
          }
          // Then by creation date (newest first)
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        });
      },

      getTaskStats: () => {
        const tasks = get().tasks;
        const totalTasks = tasks.length;
        const completedTasks = tasks.filter(task => task.completed).length;
        const pendingTasks = totalTasks - completedTasks;
        const todayCreated = tasks.filter(task => isToday(task.createdAt)).length;
        const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

        return {
          totalTasks,
          completedTasks,
          pendingTasks,
          todayCreated,
          completionRate,
        };
      },

      exportTasks: () => {
        return get().tasks;
      },

      getTaskCount: () => {
        return get().tasks.length;
      },

      debugTasks: () => {
        console.log('Current local tasks:', get().tasks);
      },

      clearDuplicates: () => {
        set((state) => ({
          tasks: state.tasks.filter((task, index, self) =>
            self.findIndex(t => t.id === task.id) === index
          )
        }));
      },
    }),
    {
      name: 'local-task-storage',
      // Only persist tasks data
      partialize: (state) => ({ tasks: state.tasks }),
    }
  )
);

// Helper function to populate task with sessions (to be called from useTaskManagement)
export const populateTaskWithSessions = (task: LocalTask, sessions: any[]): LocalTask & { pomodoroSessions: any[] } => {
  const associatedSessions = sessions
    .filter(session => session.taskId === task.id)
    .map(session => ({
      id: session.id,
      startTime: session.startTime,
      endTime: session.endTime,
      totalDuration: session.totalDuration,
      focusDuration: session.focusDuration,
      breakDuration: session.breakDuration,
      intervalType: session.intervalType,
      completed: session.completed,
      interrupted: session.interrupted,
      taskId: session.taskId,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt
    }));

  return {
    ...task,
    pomodoroSessions: associatedSessions
  };
}; 