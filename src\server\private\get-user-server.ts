"use server";

import { auth } from "@/server/auth/auth";
import { headers } from "next/headers";
import { Session, User as AuthUser } from "better-auth";

// Extend the User type from better-auth to include Prisma User fields
interface PrismaUser {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string | null;
  premium?: boolean;
  role?: string;
  banned?: boolean;
  banReason?: string | null;
  banExpires?: bigint | null;
  createdAt: Date;
  updatedAt: Date;
  subscriptionType?: "FREE" | "PREMIUM" | "PREMIUM_PLUS";
}

// Create a composite User type that includes both better-auth User fields
// and additional Prisma User fields
export type User = AuthUser & Partial<PrismaUser>;

// Types for function returns
export interface UserResult {
  user: User | null;
  isAuthenticated: boolean;
  error: boolean;
  errorMessage?: string;
}

export interface SessionResult extends UserResult {
  session: Session | null;
}

/**
 * Fetches only the user information
 */
export async function getUserServer(): Promise<UserResult> {
  try {
    const { user, error, errorMessage } = await getUserSessionServer();

    return {
      user,
      isAuthenticated: Boolean(user),
      error,
      errorMessage,
    };
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    console.error("Error in getUserServer:", errorMessage);

    return {
      user: null,
      isAuthenticated: false,
      error: true,
      errorMessage,
    };
  }
}

/**
 * Fetches both user and session information
 */
export async function getUserSessionServer(): Promise<SessionResult> {
  try {
    const headerValues = await headers();
    const sessionResponse = await auth.api.getSession({
      headers: headerValues,
    });

    // Check if session response exists and has the expected structure
    if (!sessionResponse || typeof sessionResponse !== "object") {
      return {
        session: null,
        user: null,
        isAuthenticated: false,
        error: Boolean(sessionResponse),
        errorMessage: "Invalid session response format",
      };
    }

    // Extract the actual session and user from the response
    const session =
      "session" in sessionResponse ? sessionResponse.session : null;
    const user =
      "user" in sessionResponse && sessionResponse.user
        ? sessionResponse.user as User
        : null;

    return {
      session,
      user,
      isAuthenticated: Boolean(user),
      error: false,
    };
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    console.error("Error fetching session:", errorMessage);

    return {
      session: null,
      user: null,
      isAuthenticated: false,
      error: true,
      errorMessage,
    };
  }
}
