"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet"
import { Loader2, Waves } from "lucide-react"
import { useCreateNaturePlaylist } from "@schemas/NaturalPlaylist/nature-playlist-query"
import { toast } from "sonner"

interface NaturePlaylistFormSheetProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
}

export function NaturePlaylistFormSheet({ isOpen, onOpenChange }: NaturePlaylistFormSheetProps) {
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")
  const [isPublic, setIsPublic] = useState(false)

  const createPlaylist = useCreateNaturePlaylist()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!name.trim()) {
      toast.error("Playlist name is required")
      return
    }

    try {
      await createPlaylist.mutateAsync({
        form: {
          name: name.trim(),
          ...(description.trim() && { description: description.trim() }),
          isPublic: String(isPublic)
        }
      })
      
      // Reset form
      setName("")
      setDescription("")
      setIsPublic(false)
      onOpenChange(false)
    } catch (error) {
      console.error("Failed to create nature playlist:", error)
    }
  }

  const handleCancel = () => {
    setName("")
    setDescription("")
    setIsPublic(false)
    onOpenChange(false)
  }

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-md">
        <SheetHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/30 dark:to-rose-900/30">
              <Waves className="h-5 w-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <SheetTitle>Create Nature Playlist</SheetTitle>
              <SheetDescription>
                Create a new playlist for your natural sounds
              </SheetDescription>
            </div>
          </div>
        </SheetHeader>

        <form onSubmit={handleSubmit} className="space-y-6 mt-6">
          <div className="space-y-2">
            <Label htmlFor="name">Playlist Name *</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter playlist name..."
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe your nature playlist..."
              rows={3}
            />
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isPublic"
              checked={isPublic}
              onChange={(e) => setIsPublic(e.target.checked)}
              className="rounded border-gray-300 text-primary focus:ring-primary"
            />
            <Label htmlFor="isPublic" className="text-sm">
              Make this playlist public
            </Label>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              className="flex-1"
              disabled={createPlaylist.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600"
              disabled={createPlaylist.isPending || !name.trim()}
            >
              {createPlaylist.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Playlist"
              )}
            </Button>
          </div>
        </form>
      </SheetContent>
    </Sheet>
  )
}
