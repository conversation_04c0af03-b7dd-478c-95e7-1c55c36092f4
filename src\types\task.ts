export interface Task {
  id: string
  title: string
  description: string
  status: "pending" | "in-progress" | "completed"
  priority: "high" | "medium" | "low"
  dueDate: string | null
  recurrence: {
    type: "daily" | "weekdays" | "weekly" | "monthly" | "custom"
    interval?: number
    days?: number[]
  } | null
  pomodoroEstimate: number
  pomodoroCount: number
  progress: number
  timeSpent: number
  createdAt: string
  updatedAt: string
  order: number
}
