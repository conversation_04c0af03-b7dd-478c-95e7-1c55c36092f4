"use client";

import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>D<PERSON><PERSON>,
  SheetFooter
} from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import {
  useCreateVideo,
  useUpdateVideo,
  useGetVideo
} from "../../../../../prisma/schema/Video/video-query";
import { CreateVideoInput } from "../../../../../prisma/schema/Video/video-type";

import { <PERSON><PERSON><PERSON><PERSON> } from "@prisma/client";


interface VideoFormSheetProps {
  videoId?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function VideoFormSheet({
  videoId,
  open,
  onOpenChange,
  onSuccess
}: VideoFormSheetProps) {
  const isEditMode = !!videoId;
  const title = isEditMode ? "Edit Video" : "Create Video";

  // Form state
  const [formData, setFormData] = useState<CreateVideoInput>({
    title: "",
    src: "",
    thumbnail: "",
    description: "",
    isPublic: true,
    isPremium: false,
    videoGenre: ["MEDITATION"],
    musicPlaylistId: null,
  });

  // Form validation state
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Mutations for creating and updating videos
  const createVideoMutation = useCreateVideo();
  const updateVideoMutation = useUpdateVideo();

  // For edit mode, fetch the video details
  const videoQuery = useGetVideo(videoId);

  // When in edit mode and video data is loaded, populate the form
  useEffect(() => {
    if (isEditMode && videoQuery.data) {
      setFormData({
        title: videoQuery.data.title,
        src: videoQuery.data.src,
        thumbnail: videoQuery.data.thumbnail || "",
        description: videoQuery.data.description || "",
        isPublic: videoQuery.data.isPublic,
        isPremium: videoQuery.data.isPremium,
        videoGenre: videoQuery.data.videoGenre || ["MEDITATION"],
        musicPlaylistId: videoQuery.data.musicPlaylistId || null,
      });
    }
  }, [isEditMode, videoQuery.data]);

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Handle genre checkbox changes
  const handleGenreChange = (genre: VideoGenre, checked: boolean) => {
    setFormData(prev => {
      // Ensure videoGenre is always an array
      const currentGenres = prev.videoGenre || ["MEDITATION"];

      const updatedGenres = checked
        ? [...currentGenres, genre]
        : currentGenres.filter(g => g !== genre);

      return { ...prev, videoGenre: updatedGenres };
    });
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    }

    if (!formData.src.trim()) {
      newErrors.src = "Video URL is required";
    } else {
      try {
        new URL(formData.src);
      } catch {
        newErrors.src = "Please enter a valid URL";
      }
    }

    if (formData.thumbnail && formData.thumbnail.trim()) {
      try {
        new URL(formData.thumbnail);
      } catch {
        newErrors.thumbnail = "Please enter a valid URL";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (isEditMode && videoId) {
      // Explicitly cast the form data for the API
      const updateData = {
        title: formData.title,
        src: formData.src,
        thumbnail: formData.thumbnail || undefined,
        description: formData.description || undefined,
        isPublic: formData.isPublic.toString(),
        isPremium: formData.isPremium.toString(),
        videoGenre: formData.videoGenre || ["MEDITATION"],
        musicPlaylistId: formData.musicPlaylistId || undefined,
      };

      updateVideoMutation.mutate(
        {
          form: updateData,
          param: { id: videoId },
        },
        {
          onSuccess: () => {
            toast.success("Video updated successfully");
            onSuccess?.();
          },
          onError: (error) => {
            toast.error(`Failed to update video: ${error.message}`);
          },
        }
      );
    } else {
      // Explicitly cast the form data for the API
      const createData = {
        title: formData.title,
        src: formData.src,
        thumbnail: formData.thumbnail || undefined,
        description: formData.description || undefined,
        isPublic: formData.isPublic.toString(),
        isPremium: formData.isPremium.toString(),
        videoGenre: formData.videoGenre || ["MEDITATION"],
        musicPlaylistId: formData.musicPlaylistId || undefined,
      };

      createVideoMutation.mutate(
        {
          form: createData,
        },
        {
          onSuccess: () => {
            toast.success("Video created successfully");
            onSuccess?.();
            // Reset form
            setFormData({
              title: "",
              src: "",
              thumbnail: "",
              description: "",
              isPublic: true,
              isPremium: false,
              videoGenre: ["MEDITATION"],
              musicPlaylistId: null,
            });
          },
          onError: (error) => {
            toast.error(`Failed to create video: ${error.message}`);
          },
        }
      );
    }
  };

  // Check if loading or submitting
  const isLoading =
    (isEditMode && videoQuery.isLoading) ||
    createVideoMutation.isPending ||
    updateVideoMutation.isPending;

  // Extract all values from the VideoGenre enum
  const genres = Object.values(VideoGenre);

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-md md:max-w-lg lg:max-w-xl w-full p-0 focus:outline-none">
        <div className="h-full flex flex-col">
          <SheetHeader className="px-6 pt-6 pb-2">
            <SheetTitle className="text-xl font-semibold">{title}</SheetTitle>
            <SheetDescription>
              {isEditMode
                ? "Update your video details below"
                : "Fill in the details to add a new video"
              }
            </SheetDescription>
          </SheetHeader>

          <div className="flex-1 overflow-y-auto px-6">
            <form id="video-form" onSubmit={handleSubmit} className="space-y-5 py-4">
              <div className="space-y-2">
                <Label htmlFor="title" className={errors.title ? 'text-destructive font-medium' : 'font-medium'}>
                  Title <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="title"
                  name="title"
                  placeholder="Enter video title"
                  value={formData.title}
                  onChange={handleChange}
                  className={errors.title ? 'border-destructive' : ''}
                  aria-required="true"
                  autoComplete="off"
                />
                {errors.title && (
                  <p className="text-sm text-destructive mt-1">{errors.title}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="src" className={errors.src ? 'text-destructive font-medium' : 'font-medium'}>
                  Video URL <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="src"
                  name="src"
                  placeholder="https://example.com/video.mp4"
                  value={formData.src}
                  onChange={handleChange}
                  className={errors.src ? 'border-destructive' : ''}
                  aria-required="true"
                  autoComplete="off"
                />
                {errors.src && (
                  <p className="text-sm text-destructive mt-1">{errors.src}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="thumbnail" className={errors.thumbnail ? 'text-destructive font-medium' : 'font-medium'}>
                  Thumbnail URL
                </Label>
                <Input
                  id="thumbnail"
                  name="thumbnail"
                  placeholder="https://example.com/thumbnail.jpg"
                  value={formData.thumbnail}
                  onChange={handleChange}
                  className={errors.thumbnail ? 'border-destructive' : ''}
                  autoComplete="off"
                />
                {errors.thumbnail && (
                  <p className="text-sm text-destructive mt-1">{errors.thumbnail}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description" className="font-medium">
                  Description
                </Label>
                <Textarea
                  id="description"
                  name="description"
                  placeholder="Enter video description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label className="font-medium">Genres</Label>
                <div className="grid grid-cols-2 gap-2">
                  {genres.map(genre => (
                    <div key={genre} className="flex items-center space-x-2">
                      <Checkbox
                        id={`genre-${genre}`}
                        checked={(formData.videoGenre || ["MEDITATION"]).includes(genre)}
                        onCheckedChange={(checked) => handleGenreChange(genre, checked === true)}
                      />
                      <Label htmlFor={`genre-${genre}`} className="text-sm capitalize">
                        {genre.toLowerCase()}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <Separator className="my-4" />

              <div className="flex items-center justify-between">
                <Label htmlFor="isPublic" className="font-medium">
                  Public Video
                </Label>
                <Switch
                  id="isPublic"
                  checked={formData.isPublic}
                  onCheckedChange={(checked) => handleSwitchChange('isPublic', checked === true)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="isPremium" className="font-medium">
                  Premium Content
                </Label>
                <Switch
                  id="isPremium"
                  checked={formData.isPremium}
                  onCheckedChange={(checked) => handleSwitchChange('isPremium', checked === true)}
                />
              </div>
            </form>
          </div>

          <SheetFooter className="px-6 py-4 border-t">
            <Button
              type="submit"
              form="video-form"
              disabled={isLoading}
              className="w-full sm:w-auto"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isEditMode ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  {isEditMode ? 'Update Video' : 'Create Video'}
                </>
              )}
            </Button>
          </SheetFooter>
        </div>
      </SheetContent>
    </Sheet>
  );
}