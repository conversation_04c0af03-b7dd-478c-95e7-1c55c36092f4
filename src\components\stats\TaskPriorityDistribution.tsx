"use client"

import { useTaskStore } from "@/store/taskStore"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, <PERSON><PERSON><PERSON> } from "recharts"

interface CustomTooltipProps {
  active?: boolean
  payload?: Array<{
    name: string
    value: number
    payload: { name: string; value: number; color: string }
  }>
}

export function TaskPriorityDistribution() {
  const { tasks } = useTaskStore()

  // Count tasks by priority
  const highPriorityCount = tasks.filter((task) => task.priority === "high").length
  const mediumPriorityCount = tasks.filter((task) => task.priority === "medium").length
  const lowPriorityCount = tasks.filter((task) => task.priority === "low").length

  // Calculate completion rates by priority
  const highPriorityCompleted = tasks.filter((task) => task.priority === "high" && task.status === "completed").length

  const mediumPriorityCompleted = tasks.filter(
    (task) => task.priority === "medium" && task.status === "completed",
  ).length

  const lowPriorityCompleted = tasks.filter((task) => task.priority === "low" && task.status === "completed").length

  // Calculate completion rates
  const highCompletionRate = highPriorityCount > 0 ? Math.round((highPriorityCompleted / highPriorityCount) * 100) : 0

  const mediumCompletionRate =
    mediumPriorityCount > 0 ? Math.round((mediumPriorityCompleted / mediumPriorityCount) * 100) : 0

  const lowCompletionRate = lowPriorityCount > 0 ? Math.round((lowPriorityCompleted / lowPriorityCount) * 100) : 0

  // Prepare data for pie chart
  const pieData = [
    { name: "High", value: highPriorityCount, color: "#F44336" },
    { name: "Medium", value: mediumPriorityCount, color: "#F48B3A" },
    { name: "Low", value: lowPriorityCount, color: "#3A6CF4" },
  ].filter((item) => item.value > 0)

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload

      let completionRate = 0
      if (data.name === "High") completionRate = highCompletionRate
      if (data.name === "Medium") completionRate = mediumCompletionRate
      if (data.name === "Low") completionRate = lowCompletionRate

      return (
        <div className="rounded-md border border-border bg-card p-3 shadow-md">
          <p className="mb-1 font-medium">{data.name} Priority</p>
          <p className="text-muted-foreground">
            <span className="font-medium">Tasks: </span>
            <span className="font-bold">{data.value}</span>
          </p>
          <p className="text-muted-foreground">
            <span className="font-medium">Completion Rate: </span>
            <span className="font-bold">{completionRate}%</span>
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <Card className="border-border bg-card/50 shadow-lg">
      <CardHeader>
        <CardTitle>Priority Distribution</CardTitle>
        <CardDescription>Tasks by priority level</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div className="h-[200px] w-full">
            {pieData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex h-full items-center justify-center">
                <p className="text-muted-foreground">No tasks available</p>
              </div>
            )}
          </div>

          <div className="flex flex-col justify-center space-y-4">
            <div>
              <div className="mb-1 flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-2 h-3 w-3 rounded-full" style={{ backgroundColor: "#F44336" }}></div>
                  <span className="text-sm">High Priority</span>
                </div>
                <span className="text-sm font-medium">{highPriorityCount} tasks</span>
              </div>
              <div className="h-2 w-full rounded-full bg-muted">
                <div 
                  className="h-2 rounded-full" 
                  style={{ width: `${highCompletionRate}%`, backgroundColor: "#F44336" }}
                ></div>
              </div>
              <div className="mt-1 text-right text-xs text-muted-foreground">{highCompletionRate}% completed</div>
            </div>

            <div>
              <div className="mb-1 flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-2 h-3 w-3 rounded-full" style={{ backgroundColor: "#F48B3A" }}></div>
                  <span className="text-sm">Medium Priority</span>
                </div>
                <span className="text-sm font-medium">{mediumPriorityCount} tasks</span>
              </div>
              <div className="h-2 w-full rounded-full bg-muted">
                <div 
                  className="h-2 rounded-full"
                  style={{ width: `${mediumCompletionRate}%`, backgroundColor: "#F48B3A" }}
                ></div>
              </div>
              <div className="mt-1 text-right text-xs text-muted-foreground">{mediumCompletionRate}% completed</div>
            </div>

            <div>
              <div className="mb-1 flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-2 h-3 w-3 rounded-full" style={{ backgroundColor: "#3A6CF4" }}></div>
                  <span className="text-sm">Low Priority</span>
                </div>
                <span className="text-sm font-medium">{lowPriorityCount} tasks</span>
              </div>
              <div className="h-2 w-full rounded-full bg-muted">
                <div 
                  className="h-2 rounded-full"
                  style={{ width: `${lowCompletionRate}%`, backgroundColor: "#3A6CF4" }}
                ></div>
              </div>
              <div className="mt-1 text-right text-xs text-muted-foreground">{lowCompletionRate}% completed</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
