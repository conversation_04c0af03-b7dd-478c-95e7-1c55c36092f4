import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { InferRequestType, InferResponseType } from "hono";
import client from "@/lib/trpc";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

// Get all videos
export type GetVideos_ResponseType = InferResponseType<
  (typeof client.api.videos)["$get"],
  200
>;

export type GetVideos_ResponseTypeSuccess = Extract<
  GetVideos_ResponseType,
  { data: object }
>["data"];

// Get Playlist type from GetVideos_ResponseTypeSuccess
export type PlaylistTypeResponse = GetVideos_ResponseTypeSuccess[0]['musicPlaylist'] | GetVideos_ResponseTypeSuccess[0]['naturePlaylist'];

export const useGetVideos = (filters?: {
  isPublic?: boolean;
  isPremium?: boolean;
  musicPlaylistId?: string;
  naturePlaylistId?: string;
}) => {
  return useQuery({
    queryKey: ["videos", filters],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      
      if (filters?.isPublic !== undefined) {
        queryParams.append("isPublic", String(filters.isPublic));
      }
      
      if (filters?.isPremium !== undefined) {
        queryParams.append("isPremium", String(filters.isPremium));
      }
      
      if (filters?.musicPlaylistId) {
        queryParams.append("musicPlaylistId", filters.musicPlaylistId);
      }

      if (filters?.naturePlaylistId) {
        queryParams.append("naturePlaylistId", filters.naturePlaylistId);
      }
      
      const queryString = queryParams.toString();
      
      let response;
      if (queryString) {
        response = await client.api.videos.$get({
          query: { 
            isPublic: filters?.isPublic !== undefined ? String(filters.isPublic) : undefined,
            isPremium: filters?.isPremium !== undefined ? String(filters.isPremium) : undefined,
            musicPlaylistId: filters?.musicPlaylistId,
            naturePlaylistId: filters?.naturePlaylistId
          }
        });
      } else {
        response = await client.api.videos.$get();
      }
      
      if (!response.ok) {
        throw new Error("Failed to fetch videos");
      }
      const { data } = await response.json();
      return data;
    },
  });
};

// Get single video
type GetVideo_ResponseType = InferResponseType<
  (typeof client.api.videos)[":id"]["$get"],
  200
>;

export type GetVideo_ResponseTypeSuccess = Extract<
  GetVideo_ResponseType,
  { data: object }
>["data"];

export const useGetVideo = (id?: string) => {
  return useQuery({
    queryKey: ["videos", { id }],
    queryFn: async () => {
      if (!id) throw new Error("No video ID provided");

      const response = await client.api.videos[":id"]["$get"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch video");
      }

      const { data } = await response.json();
      return data;
    },
    enabled: !!id,
  });
};

// Create video
interface CreateVideoSuccessResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

type CreateVideoRequest = InferRequestType<
  (typeof client.api.videos)["$post"]
>;

export const useCreateVideo = () => {
  const router = useRouter();
  const queryClient = useQueryClient();

  const mutation = useMutation<
    CreateVideoSuccessResponse,
    Error,
    CreateVideoRequest
  >({
    mutationFn: async ({ form }) => {
      const response = await client.api.videos.$post({ form });

      if (!response.ok) {
        throw new Error("Failed to create video");
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Video created successfully");
      router.refresh();
      queryClient.invalidateQueries({ queryKey: ["videos"] });
    },
    onError: (error) => {
      toast.error(`Failed to create video: ${error.message}`);
    },
  });

  return mutation;
};

// Update video
type UpdateVideo_ResponseType = InferResponseType<
  (typeof client.api.videos)[":id"]["$patch"],
  200
>;

export type UpdateVideo_ResponseTypeSuccess = Extract<
  UpdateVideo_ResponseType,
  { data: object }
>["data"];

type UpdateVideoRequest = InferRequestType<
  (typeof client.api.videos)[":id"]["$patch"]
>;

export const useUpdateVideo = () => {
  const queryClient = useQueryClient();
  
  return useMutation<
    UpdateVideo_ResponseType,
    Error,
    UpdateVideoRequest
  >({
    mutationFn: async (variables) => {
      const { form, param } = variables;

      if (!param?.id) {
        throw new Error("No video ID provided");
      }

      const response = await client.api.videos[":id"]["$patch"]({
        form,
        param: { id: param.id },
      });

      if (!response.ok) {
        throw new Error(`Failed to update video. Status: ${response.status}`);
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Video updated successfully");
      const videoId = data?.id;
      if (videoId) {
        queryClient.invalidateQueries({ queryKey: ["videos", { id: videoId }] });
      }
      queryClient.invalidateQueries({ queryKey: ["videos"] });
    },
    onError: (error) => {
      toast.error(`Failed to update video: ${error.message}`);
    },
  });
};

// Delete video
type DeleteVideo_ResponseType = InferResponseType<
  (typeof client.api.videos)[":id"]["$delete"],
  200
>;

export const useDeleteVideo = () => {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation<DeleteVideo_ResponseType, Error, { id: string }>({
    mutationFn: async ({ id }) => {
      if (!id) throw new Error("No video ID provided");

      const response = await client.api.videos[":id"]["$delete"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to delete video");
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Video deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["videos"] });
      router.push("/videos"); // Redirect to videos list
    },
    onError: (error) => {
      toast.error(`Failed to delete video: ${error.message}`);
    },
  });
};

// Toggle favorite status
type ToggleFavorite_ResponseType = InferResponseType<
  (typeof client.api.videos)[":id"]["favorite"]["$post"],
  200
>;

export type ToggleFavorite_ResponseTypeSuccess = Extract<
  ToggleFavorite_ResponseType,
  { data: object }
>["data"];

export const useToggleFavorite = () => {
  const queryClient = useQueryClient();

  return useMutation<ToggleFavorite_ResponseType, Error, { id: string }>({
    mutationFn: async ({ id }) => {
      if (!id) throw new Error("No video ID provided");

      const response = await client.api.videos[":id"]["favorite"]["$post"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to toggle favorite status");
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      const { id } = variables;
      queryClient.invalidateQueries({ queryKey: ["videos", { id }] });
      queryClient.invalidateQueries({ queryKey: ["videos"] });
      queryClient.invalidateQueries({ queryKey: ["videos", "favorites"] });
    },
    onError: (error) => {
      toast.error(`Failed to update favorite status: ${error.message}`);
    },
  });
};

// Get favorite videos
export const useGetFavoriteVideos = () => {
  return useQuery({
    queryKey: ["videos", "favorites"],
    queryFn: async () => {
      const response = await client.api.videos.favorites.$get();
      if (!response.ok) {
        throw new Error("Failed to fetch favorite videos");
      }
      const { data } = await response.json();
      return data;
    },
  });
};

// Update video's music playlist
interface UpdateVideoMusicPlaylistResponse {
  data: {
    id: string;
    musicPlaylistId: string | null;
    musicPlaylist?: {
      id: string;
      name: string;
    };
    [key: string]: unknown;
  };
}

export const useUpdateVideoMusicPlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    UpdateVideoMusicPlaylistResponse,
    Error,
    { videoId: string; musicPlaylistId: string | null }
  >({
    mutationFn: async ({ videoId, musicPlaylistId }) => {
      if (!videoId) throw new Error("No video ID provided");

      const response = await fetch(`/api/videos/${videoId}/musicPlaylist`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ musicPlaylistId }),
      });

      if (!response.ok) {
        throw new Error("Failed to update video music playlist");
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Video music playlist updated successfully");
      queryClient.invalidateQueries({ queryKey: ["videos", { id: data.id }] });
      queryClient.invalidateQueries({ queryKey: ["videos"] });
    },
    onError: (error) => {
      toast.error(`Failed to update video music playlist: ${error.message}`);
    },
  });
};

// Update video's nature playlist
interface UpdateVideoNaturePlaylistResponse {
  data: {
    id: string;
    naturePlaylistId: string | null;
    naturePlaylist?: {
      id: string;
      name: string;
    };
    [key: string]: unknown;
  };
}

export const useUpdateVideoNaturePlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    UpdateVideoNaturePlaylistResponse,
    Error,
    { videoId: string; naturePlaylistId: string | null }
  >({
    mutationFn: async ({ videoId, naturePlaylistId }) => {
      if (!videoId) throw new Error("No video ID provided");

      const response = await fetch(`/api/videos/${videoId}/naturePlaylist`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ naturePlaylistId }),
      });

      if (!response.ok) {
        throw new Error("Failed to update video nature playlist");
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Video nature playlist updated successfully");
      queryClient.invalidateQueries({ queryKey: ["videos", { id: data.id }] });
      queryClient.invalidateQueries({ queryKey: ["videos"] });
    },
    onError: (error) => {
      toast.error(`Failed to update video nature playlist: ${error.message}`);
    },
  });
};

// Reorder videos
interface ReorderVideosResponse {
  data: {
    success: boolean;
    count: number;
  };
}

export interface VideoOrderItem {
  id: string;
  order: number;
}

export const useReorderVideos = () => {
  const queryClient = useQueryClient();

  return useMutation<
    ReorderVideosResponse,
    Error,
    { videoOrders: VideoOrderItem[] }
  >({
    mutationFn: async ({ videoOrders }) => {
      if (!videoOrders || videoOrders.length === 0) {
        throw new Error("No video orders provided");
      }

      const response = await client.api.videos.reorder.$patch({
        json: { videoOrders }
      });

      if (!response.ok) {
        throw new Error("Failed to reorder videos");
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Videos reordered successfully");
      queryClient.invalidateQueries({ queryKey: ["videos"] });
    },
    onError: (error) => {
      toast.error(`Failed to reorder videos: ${error.message}`);
    },
  });
}; 