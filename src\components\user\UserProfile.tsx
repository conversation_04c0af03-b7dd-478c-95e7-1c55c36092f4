"use client";

import { useUserStore } from "@/store/userStore";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import {
  User,
  Crown,
  Shield,
  Mail,
  Calendar,
  Target,
  Clock,
  Flame,
  Settings,
  Bell,
  Volume2,
  Eye
} from "lucide-react";

export function UserProfile() {
  const {
    user,
    isAuthenticated,
    preferences,
    stats,
    getDisplayName,
    getAvatarUrl,
    isPremium,
    isAdmin,
    updatePreferences,
    updateStats,
    clearUser,
  } = useUserStore();

  if (!isAuthenticated || !user) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Please sign in to view your profile.
          </div>
        </CardContent>
      </Card>
    );
  }

  const handlePreferenceChange = (key: string, value: any) => {
    updatePreferences({ [key]: value });
  };

  const handleNestedPreferenceChange = (category: string, key: string, value: any) => {
    // Type-safe handling of nested preference updates
    if (category === "notifications") {
      updatePreferences({
        notifications: {
          ...preferences.notifications,
          [key]: value,
        },
      });
    } else if (category === "pomodoro") {
      updatePreferences({
        pomodoro: {
          ...preferences.pomodoro,
          [key]: value,
        },
      });
    } else if (category === "privacy") {
      updatePreferences({
        privacy: {
          ...preferences.privacy,
          [key]: value,
        },
      });
    } else if (category === "ui") {
      updatePreferences({
        ui: {
          ...preferences.ui,
          [key]: value,
        },
      });
    }
  };

  const simulateStatsUpdate = () => {
    updateStats({
      totalPomodoroSessions: stats.totalPomodoroSessions + 1,
      totalFocusTime: stats.totalFocusTime + 1500, // 25 minutes
      currentStreak: stats.currentStreak + 1,
      longestStreak: Math.max(stats.longestStreak, stats.currentStreak + 1),
    });
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* User Info Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={getAvatarUrl() || undefined} alt={getDisplayName()} />
              <AvatarFallback>
                <User className="h-8 w-8" />
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <CardTitle className="flex items-center gap-2">
                {getDisplayName()}
                {isPremium() && <Crown className="h-5 w-5 text-yellow-500" />}
                {isAdmin() && <Shield className="h-5 w-5 text-blue-500" />}
              </CardTitle>
              <CardDescription className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                {user.email}
              </CardDescription>
              <div className="flex gap-2 mt-2">
                <Badge variant={user.emailVerified ? "default" : "secondary"}>
                  {user.emailVerified ? "Verified" : "Unverified"}
                </Badge>
                <Badge variant={isPremium() ? "default" : "outline"}>
                  {user.subscriptionType || "FREE"}
                </Badge>
                <Badge variant="outline">{user.role}</Badge>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>Joined: {new Date(user.createdAt).toLocaleDateString()}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span>Last active: {new Date(stats.lastActiveDate).toLocaleDateString()}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Your Statistics
          </CardTitle>
          <CardDescription>Track your productivity journey</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.totalPomodoroSessions}</div>
              <div className="text-sm text-muted-foreground">Total Sessions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {Math.round(stats.totalFocusTime / 3600)}h
              </div>
              <div className="text-sm text-muted-foreground">Focus Time</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600 flex items-center justify-center gap-1">
                <Flame className="h-6 w-6" />
                {stats.currentStreak}
              </div>
              <div className="text-sm text-muted-foreground">Current Streak</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{stats.tasksCompleted}</div>
              <div className="text-sm text-muted-foreground">Tasks Done</div>
            </div>
          </div>
          <div className="mt-4">
            <Button onClick={simulateStatsUpdate} variant="outline" size="sm">
              Simulate Pomodoro Session
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Preferences Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Preferences
          </CardTitle>
          <CardDescription>Customize your experience</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Theme */}
          <div>
            <Label className="text-base font-medium">Theme</Label>
            <div className="mt-2">
              <select
                value={preferences.theme}
                onChange={(e) => handlePreferenceChange("theme", e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="light">Light</option>
                <option value="dark">Dark</option>
                <option value="system">System</option>
              </select>
            </div>
          </div>

          <Separator />

          {/* Notifications */}
          <div>
            <Label className="text-base font-medium flex items-center gap-2">
              <Bell className="h-4 w-4" />
              Notifications
            </Label>
            <div className="mt-3 space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="email-notifications">Email notifications</Label>
                <Switch
                  id="email-notifications"
                  checked={preferences.notifications.email}
                  onCheckedChange={(checked) =>
                    handleNestedPreferenceChange("notifications", "email", checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="push-notifications">Push notifications</Label>
                <Switch
                  id="push-notifications"
                  checked={preferences.notifications.push}
                  onCheckedChange={(checked) =>
                    handleNestedPreferenceChange("notifications", "push", checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="pomodoro-reminders">Pomodoro reminders</Label>
                <Switch
                  id="pomodoro-reminders"
                  checked={preferences.notifications.pomodoroReminders}
                  onCheckedChange={(checked) =>
                    handleNestedPreferenceChange("notifications", "pomodoroReminders", checked)
                  }
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Pomodoro Settings */}
          <div>
            <Label className="text-base font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Pomodoro Settings
            </Label>
            <div className="mt-3 space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="auto-start-breaks">Auto-start breaks</Label>
                <Switch
                  id="auto-start-breaks"
                  checked={preferences.pomodoro.autoStartBreaks}
                  onCheckedChange={(checked) =>
                    handleNestedPreferenceChange("pomodoro", "autoStartBreaks", checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="auto-start-pomodoros">Auto-start pomodoros</Label>
                <Switch
                  id="auto-start-pomodoros"
                  checked={preferences.pomodoro.autoStartPomodoros}
                  onCheckedChange={(checked) =>
                    handleNestedPreferenceChange("pomodoro", "autoStartPomodoros", checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="sound-enabled">Sound enabled</Label>
                <Switch
                  id="sound-enabled"
                  checked={preferences.pomodoro.soundEnabled}
                  onCheckedChange={(checked) =>
                    handleNestedPreferenceChange("pomodoro", "soundEnabled", checked)
                  }
                />
              </div>
              <div>
                <Label className="flex items-center gap-2">
                  <Volume2 className="h-4 w-4" />
                  Volume: {preferences.pomodoro.volume}%
                </Label>
                <Slider
                  value={[preferences.pomodoro.volume]}
                  onValueChange={([value]) =>
                    handleNestedPreferenceChange("pomodoro", "volume", value)
                  }
                  max={100}
                  step={1}
                  className="mt-2"
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Privacy Settings */}
          <div>
            <Label className="text-base font-medium flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Privacy
            </Label>
            <div className="mt-3 space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="profile-visible">Profile visible to others</Label>
                <Switch
                  id="profile-visible"
                  checked={preferences.privacy.profileVisible}
                  onCheckedChange={(checked) =>
                    handleNestedPreferenceChange("privacy", "profileVisible", checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="stats-visible">Stats visible to others</Label>
                <Switch
                  id="stats-visible"
                  checked={preferences.privacy.statsVisible}
                  onCheckedChange={(checked) =>
                    handleNestedPreferenceChange("privacy", "statsVisible", checked)
                  }
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardContent className="p-6">
          <Button onClick={clearUser} variant="destructive">
            Sign Out (Clear User Data)
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
