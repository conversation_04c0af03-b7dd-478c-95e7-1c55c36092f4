'use client';

import { useAudioStore, type Audio as AudioType } from '@/lib/audio-store';
import { cn } from '@/lib/utils';
import { Clock, Music, Pause, Headphones, Volume2, VolumeX, CheckCircle, Search, X } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';

// Assuming AudioType doesn't have description field by default
interface EnhancedAudioType extends AudioType {
  description?: string;
}

// Tab IDs
const SELECTED_TAB_ID = "selected";
const SONGS_TAB_ID = "songs";

export function AudioSettings() {
  const { 
    audios, 
    selectedAudios, 
    toggleAudioSelection, 
    useVideoDefaultAudio, 
    setUseVideoDefaultAudio,
    categories,
    rememberAudioSelections,
    toggleRememberAudioSelections
  } = useAudioStore();
  
  const [previewAudio, setPreviewAudio] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [playingAudios, setPlayingAudios] = useState<string[]>([]);
  const [volume, setVolume] = useState<number>(50);
  const [isMuted, setIsMuted] = useState<boolean>(false);
  const [currentTab, setCurrentTab] = useState<string>(SONGS_TAB_ID);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  
  // Initialize audio element once
  useEffect(() => {
    if (typeof window !== 'undefined') {
      audioRef.current = new Audio();
      audioRef.current.volume = volume / 100;
    }
    
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
      }
    };
  }, [volume]);

  // Update volume when it changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume / 100;
    }
  }, [volume, isMuted]);

  // Track which audios are currently playing in the main session
  useEffect(() => {
    // In a real app, this would sync with the actual audio playback state
    if (!useVideoDefaultAudio && selectedAudios.length > 0) {
      setPlayingAudios(selectedAudios.map(audio => audio.id));
    } else {
      setPlayingAudios([]);
    }
  }, [selectedAudios, useVideoDefaultAudio]);

  // Preview audio function
  const playPreview = (audioSrc: string) => {
    if (audioRef.current) {
      if (previewAudio === audioSrc) {
        audioRef.current.pause();
        setPreviewAudio(null);
      } else {
        audioRef.current.src = audioSrc;
        audioRef.current.currentTime = 0;
        audioRef.current.play().catch(err => console.error('Failed to play preview:', err));
        setPreviewAudio(audioSrc);
      }
    }
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const toggleFilter = (categoryId: string) => {
    setActiveFilters(prev => 
      prev.includes(categoryId) 
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const clearAllFilters = () => {
    setActiveFilters([]);
    setSearchQuery('');
  };

  // Get category name for an audio
  const getCategoryNameForAudio = (audio: AudioType) => {
    const category = categories.find(cat => cat.id === audio.categoryId);
    return category?.name || 'Uncategorized';
  };

  // Filter audios based on search query and category filters
  const filteredAudios = audios.filter(audio => {
    const matchesSearch = searchQuery 
      ? audio.title.toLowerCase().includes(searchQuery.toLowerCase())
      : true;
    
    const matchesCategory = activeFilters.length > 0
      ? activeFilters.includes(audio.categoryId)
      : true;
    
    return matchesSearch && matchesCategory;
  });

  // Total selected count
  const totalSelectedCount = selectedAudios.length;

  if (!audios.length) {
    return <AudioPlaceholder />;
  }

  return (
    <div className="w-full space-y-4">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <Switch 
            id="use-video-audio"
            checked={useVideoDefaultAudio}
            onCheckedChange={setUseVideoDefaultAudio}
          />
          <Label htmlFor="use-video-audio" className="font-medium">Use audio from selected video</Label>
        </div>
        
        <div className="flex items-center gap-3">
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-8 w-8" 
            onClick={toggleMute}
            aria-label={isMuted ? "Unmute" : "Mute"}
          >
            {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
          </Button>
          <Slider
            value={[volume]}
            min={0}
            max={100}
            step={1}
            onValueChange={(value) => setVolume(value[0])}
            className="w-16"
            aria-label="Volume control"
          />
        </div>
      </div>
      
      {!useVideoDefaultAudio && (
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Select sounds to play during your focus sessions:</span>
            <Button 
              variant="outline" 
              size="sm" 
              className="h-8 text-xs whitespace-nowrap ml-2"
              onClick={() => selectedAudios.forEach(audio => toggleAudioSelection(audio))}
              disabled={selectedAudios.length === 0}
              aria-disabled={selectedAudios.length === 0}
            >
              Clear all selections
            </Button>
          </div>
          
          <Tabs 
            value={currentTab}
            defaultValue={currentTab} 
            className="w-full"
            onValueChange={setCurrentTab}
          >
            <TabsList className="w-full h-10 p-0 bg-background border-b rounded-none justify-start">
              {/* Selected tab */}
              <TabsTrigger 
                value={SELECTED_TAB_ID}
                className={cn(
                  "flex items-center gap-1 rounded-none h-10 px-3 font-medium",
                  "border-0 border-b-2 data-[state=active]:border-primary",
                  "data-[state=active]:bg-white/5 data-[state=active]:text-primary",
                  "hover:bg-muted/30 transition-colors",
                  totalSelectedCount === 0 ? "text-muted-foreground/70" : "text-primary"
                )}
              >
                <CheckCircle className="h-4 w-4 mr-1" />
                Selected
                <span className={cn(
                  "ml-1.5 h-5 min-w-[20px] px-1.5 rounded-full text-xs flex items-center justify-center",
                  totalSelectedCount > 0 
                    ? "bg-primary/10 text-primary" 
                    : "bg-muted text-muted-foreground/70"
                )}>
                  {totalSelectedCount}
                </span>
              </TabsTrigger>
              
              {/* Songs tab */}
              <TabsTrigger 
                value={SONGS_TAB_ID}
                className="rounded-none border-0 border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none h-10 px-3 hover:bg-muted/30 transition-colors"
              >
                Songs
              </TabsTrigger>
            </TabsList>
            
            {/* Selected Tab Content */}
            <TabsContent value={SELECTED_TAB_ID} className="pt-3 pb-0 focus-visible:outline-none focus-visible:ring-0">
              <ScrollArea className="h-[265px] rounded-md border">
                {selectedAudios.length > 0 ? (
                  <div className="p-3 space-y-2">
                    {selectedAudios.map((audio) => (
                      <CompactAudioCard
                        key={audio.id}
                        audio={audio as EnhancedAudioType}
                        isSelected={true}
                        isPlaying={playingAudios.includes(audio.id)}
                        onToggleSelect={toggleAudioSelection}
                        isPlayingPreview={previewAudio === audio.src}
                        onPreview={() => playPreview(audio.src)}
                        categoryName={getCategoryNameForAudio(audio)}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-full p-6 text-center">
                    <div className="w-14 h-14 rounded-full bg-muted/50 flex items-center justify-center mb-3">
                      <Music className="h-7 w-7 text-muted-foreground/50" />
                    </div>
                    <h3 className="text-base font-medium mb-1">No audio selected</h3>
                    <p className="text-sm text-muted-foreground max-w-md">
                      Select audio tracks from the Songs tab to create your focus session mix.
                    </p>
                  </div>
                )}
              </ScrollArea>
              
              {/* Remember audio selections checkbox */}
              <div className="mt-2 flex items-center">
                <Checkbox 
                  id="remember-audio-selections"
                  checked={rememberAudioSelections}
                  onCheckedChange={toggleRememberAudioSelections}
                  className="mr-2"
                />
                <Label 
                  htmlFor="remember-audio-selections" 
                  className="text-sm cursor-pointer"
                >
                  Remember selected songs for future sessions
                </Label>
              </div>
            </TabsContent>
            
            {/* Songs Tab Content */}
            <TabsContent value={SONGS_TAB_ID} className="pt-3 pb-0 focus-visible:outline-none focus-visible:ring-0">
              {/* Search and filters */}
              <div className="mb-3 space-y-3">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search songs..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-9 h-9"
                  />
                  {searchQuery && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-1 top-1 h-7 w-7"
                      onClick={() => setSearchQuery('')}
                      aria-label="Clear search"
                    >
                      <X className="h-3.5 w-3.5" />
                    </Button>
                  )}
                </div>
                
                <div className="flex flex-wrap gap-2 items-center">
                  <span className="text-sm text-muted-foreground mr-1">Filter:</span>
                  {categories.map((category) => (
                    <Badge
                      key={category.id}
                      variant={activeFilters.includes(category.id) ? "default" : "outline"}
                      className={cn(
                        "cursor-pointer transition-colors",
                        activeFilters.includes(category.id) 
                          ? "bg-primary/80 hover:bg-primary/70" 
                          : "hover:bg-muted/50"
                      )}
                      onClick={() => toggleFilter(category.id)}
                    >
                      {category.name}
                    </Badge>
                  ))}
                  
                  {(activeFilters.length > 0 || searchQuery) && (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-7 text-xs ml-1"
                      onClick={clearAllFilters}
                    >
                      Clear filters
                    </Button>
                  )}
                </div>
              </div>
              
              <ScrollArea className="h-[225px] rounded-md border">
                {filteredAudios.length > 0 ? (
                  <div className="p-3 space-y-2">
                    {filteredAudios.map((audio) => (
                      <CompactAudioCard
                        key={audio.id}
                        audio={audio as EnhancedAudioType}
                        isSelected={selectedAudios.some(a => a.id === audio.id)}
                        isPlaying={playingAudios.includes(audio.id)}
                        onToggleSelect={toggleAudioSelection}
                        isPlayingPreview={previewAudio === audio.src}
                        onPreview={() => playPreview(audio.src)}
                        categoryName={getCategoryNameForAudio(audio)}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-full p-6 text-center">
                    <div className="w-12 h-12 rounded-full bg-muted/50 flex items-center justify-center mb-2">
                      <Music className="h-6 w-6 text-muted-foreground/50" />
                    </div>
                    <h3 className="text-sm font-medium mb-1">No songs found</h3>
                    <p className="text-xs text-muted-foreground max-w-md">
                      Try adjusting your search or filters
                    </p>
                  </div>
                )}
              </ScrollArea>
              
              <div className="flex justify-between items-center mt-2 text-xs text-muted-foreground">
                <span>
                  {filteredAudios.length} songs displayed
                </span>
                
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-7 text-xs"
                  onClick={() => {
                    // Toggle selection for all visible/filtered audios
                    const allSelected = filteredAudios.every(
                      audio => selectedAudios.some(a => a.id === audio.id)
                    );
                    
                    filteredAudios.forEach(audio => {
                      const isSelected = selectedAudios.some(a => a.id === audio.id);
                      if (allSelected && isSelected) {
                        toggleAudioSelection(audio);
                      } else if (!allSelected && !isSelected) {
                        toggleAudioSelection(audio);
                      }
                    });
                  }}
                >
                  {filteredAudios.every(
                    audio => selectedAudios.some(a => a.id === audio.id)
                  ) ? 'Deselect all' : 'Select all'}
                </Button>
              </div>
            </TabsContent>
          </Tabs>
          
          <div className="flex justify-between items-center pt-1 border-t mt-2">
            <div className="flex items-center gap-1">
              <Headphones className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                {totalSelectedCount} {totalSelectedCount === 1 ? 'track' : 'tracks'} will play during focus
              </span>
            </div>
            
            {totalSelectedCount > 1 && (
              <span className="text-xs text-primary">All selected tracks will play simultaneously</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

function AudioPlaceholder() {
  return (
    <div className="flex items-center justify-center w-full h-64 bg-muted/40 rounded-lg border border-dashed border-muted-foreground/25">
      <div className="text-center">
        <Music className="w-12 h-12 mx-auto mb-3 text-muted-foreground/50" />
        <p className="text-sm text-muted-foreground">No audio tracks available</p>
        <p className="text-xs text-muted-foreground/70 mt-1 max-w-64 mx-auto">Add audio tracks to enhance your focus sessions</p>
      </div>
    </div>
  );
}

interface CompactAudioCardProps {
  audio: EnhancedAudioType;
  isSelected: boolean;
  isPlaying: boolean;
  onToggleSelect: (audio: AudioType) => void;
  isPlayingPreview: boolean;
  onPreview: () => void;
  categoryName?: string;
}

function CompactAudioCard({ 
  audio, 
  isSelected, 
  isPlaying, 
  onToggleSelect, 
  isPlayingPreview, 
  onPreview,
  categoryName
}: CompactAudioCardProps) {
  // Format duration (example: convert seconds to MM:SS)
  const formatDuration = (seconds?: number) => {
    if (!seconds) return "--:--";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div 
      className={cn(
        "flex items-center p-2 rounded-md border cursor-pointer",
        isSelected 
          ? "border-primary/80 bg-primary/5" 
          : "border-muted hover:border-muted-foreground/20 bg-card",
        isPlaying && "shadow-sm"
      )}
      onClick={() => onToggleSelect(audio)}
      role="button"
      aria-pressed={isSelected}
    >
      {/* Music icon */}
      <div 
        className={cn(
          "w-10 h-10 rounded-md overflow-hidden flex-shrink-0 flex items-center justify-center mr-3",
          isSelected ? "bg-primary/20" : "bg-muted",
        )}
      >
        <Music className={cn(
          "h-5 w-5",
          isSelected ? "text-primary" : "text-muted-foreground/50"
        )} />
      </div>
      
      {/* Audio info section */}
      <div className="flex-1 min-w-0">
        <div className="flex flex-col">
          <div className="font-medium text-sm truncate">{audio.title}</div>
          <div className="flex items-center text-xs text-muted-foreground gap-2">
            <div className="flex items-center">
              <Clock className="h-3 w-3 mr-1 inline-flex" />
              <span>{formatDuration(audio.duration)}</span>
            </div>
            
            {categoryName && (
              <span className="text-xs text-muted-foreground">{categoryName}</span>
            )}
            
            {isPlaying && isPlayingPreview && (
              <span className="text-primary text-xs font-medium flex items-center gap-1">
                <span className="relative flex h-2 w-2">
                  <span className="animate-pulse absolute inline-flex h-full w-full rounded-full bg-primary opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-2 w-2 bg-primary"></span>
                </span>
                Playing
              </span>
            )}
          </div>
        </div>
      </div>
      
      {/* Preview button */}
      <Button
        variant={isPlayingPreview ? "default" : "ghost"}
        size="icon"
        className="h-8 w-8 rounded-full ml-2"
        onClick={(e) => {
          e.stopPropagation();
          onPreview();
        }}
        aria-label={isPlayingPreview ? "Stop preview" : "Listen to preview"}
      >
        {isPlayingPreview ? (
          <Pause className="h-3.5 w-3.5" />
        ) : (
          <Headphones className="h-3.5 w-3.5" />
        )}
      </Button>
    </div>
  );
} 