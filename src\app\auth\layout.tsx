import { Metadata } from "next";
import { Clock, Timer, Sparkles, Music, Image as ImageIcon } from "lucide-react";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Authentication | Pomodoro 365",
  description: "Sign in or create an account to enhance your productivity with Pomodoro 365's beautiful backgrounds and chilling music.",
  keywords: ["login", "signup", "authentication", "pomodoro", "productivity", "background videos", "ambient music", "focus timer", "time management"],
  openGraph: {
    title: "Join Pomodoro 365 - Enhance Your Productivity",
    description: "Sign in or create an account to access beautiful backgrounds and chilling music that help you stay focused and productive.",
    images: ['/og-auth.png'],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Join Pomodoro 365 - Enhance Your Productivity",
    description: "Sign in or create an account to access beautiful backgrounds and chilling music that help you stay focused and productive.",
    images: ['/og-auth.png'],
  },
};

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen flex md:flex-row flex-col">
      {/* Left sidebar - brand + information (hidden on mobile) */}
      <div className="md:w-1/2 hidden md:flex flex-col relative overflow-hidden bg-gradient-to-br from-rose-50/40 via-background to-rose-50/30 dark:from-rose-950/10 dark:via-background dark:to-rose-950/5 border-r border-border">
        {/* Logo */}
        <div className="absolute top-8 left-8 z-20">
          <Link href="/" className="flex items-center gap-2 transition-opacity hover:opacity-90">
            <div className="h-10 w-10 rounded-lg bg-background flex items-center justify-center shadow-sm border border-border">
              <Timer className="h-5 w-5 text-primary/80" aria-hidden="true" />
            </div>
            <h1 className="text-xl font-bold text-foreground">
              Pomodoro <span className="text-primary/80">365</span>
            </h1>
          </Link>
        </div>

        {/* Main content area */}
        <div className="flex-1 flex flex-col items-center justify-center p-8 relative z-10">
          <div className="max-w-md">
            <div className="flex items-center gap-2 mb-3">
              <Sparkles className="h-4 w-4 text-primary/70" aria-hidden="true" />
              <span className="text-sm font-medium text-primary/70">Immersive Experience</span>
            </div>
            <h2 className="text-3xl font-bold text-foreground mb-3 tracking-tight">Enhance your productivity journey</h2>
            {/* <p className="text-muted-foreground mb-6">Pomodoro 365 helps you manage your time effectively with customizable work sessions, beautiful backgrounds, and chilling music.</p> */}

            {/* Pomodoro cycle visualization */}
            <div className="flex items-center justify-center gap-3 mb-8 mt-10">
              <div className="px-4 py-2 bg-background rounded-md text-foreground text-sm flex items-center border border-border shadow-sm">
                <Clock className="h-4 w-4 mr-2 text-primary/70" aria-hidden="true" />
                <span>Focus Session</span>
              </div>
              <div className="w-2 h-0.5 bg-border rounded-full"></div>
              <div className="px-4 py-2 bg-background rounded-md text-foreground text-sm flex items-center border border-border shadow-sm">
                <Clock className="h-4 w-4 mr-2 text-primary/70" aria-hidden="true" />
                <span>Break Time</span>
              </div>
            </div>

            {/* Features */}
            <div className="space-y-5 mb-8">
              <div className="flex items-start gap-3.5">
                <div className="h-9 w-9 rounded-md bg-background flex items-center justify-center border border-border mt-0.5 shadow-sm">
                  <Clock className="h-4.5 w-4.5 text-primary/70" aria-hidden="true" />
                </div>
                <div>
                  <h3 className="font-medium text-foreground">Customizable Timers</h3>
                  <p className="text-muted-foreground text-sm">Set work and break durations that fit your workflow</p>
                </div>
              </div>
              <div className="flex items-start gap-3.5">
                <div className="h-9 w-9 rounded-md bg-background flex items-center justify-center border border-border mt-0.5 shadow-sm">
                  <ImageIcon className="h-4.5 w-4.5 text-primary/70" aria-hidden="true" />
                </div>
                <div>
                  <h3 className="font-medium text-foreground">Beautiful Backgrounds</h3>
                  <p className="text-muted-foreground text-sm">Immerse yourself in calming visuals while you work</p>
                </div>
              </div>
              <div className="flex items-start gap-3.5">
                <div className="h-9 w-9 rounded-md bg-background flex items-center justify-center border border-border mt-0.5 shadow-sm">
                  <Music className="h-4.5 w-4.5 text-primary/70" aria-hidden="true" />
                </div>
                <div>
                  <h3 className="font-medium text-foreground">Chilling Music</h3>
                  <p className="text-muted-foreground text-sm">Focus with ambient sounds that boost concentration</p>
                </div>
              </div>
            </div>

            {/* Testimonial */}
            <div className="bg-background/80 backdrop-blur-sm border border-border rounded-lg p-4 shadow-sm">
              <div className="flex items-start gap-2 mb-2">
                <div className="mt-1">
                  <div className="flex gap-0.5">
                    {[...Array(5)].map((_, i) => (
                      <Sparkles key={i} className="h-3.5 w-3.5 text-primary/70" aria-hidden="true" />
                    ))}
                  </div>
                </div>
                <p className="text-sm text-muted-foreground italic">
                  &ldquo;Pomodoro 365 has transformed my work habits. The beautiful backgrounds and music keep me focused for hours.&rdquo;
                </p>
              </div>
              <p className="text-xs text-muted-foreground font-medium">— Linh Tr., Software Engineer</p>
            </div>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute inset-0 z-0">
          {/* Enhanced gradient overlays */}
          <div className="absolute top-[10%] right-[15%] h-[300px] w-[300px] rounded-full bg-rose-200/10 dark:bg-rose-400/5 blur-3xl"></div>
          <div className="absolute bottom-[15%] left-[10%] h-[250px] w-[250px] rounded-full bg-rose-100/15 dark:bg-rose-300/5 blur-3xl"></div>
          <div className="absolute top-[60%] left-[25%] h-[150px] w-[150px] rounded-full bg-rose-300/5 dark:bg-rose-500/3 blur-3xl"></div>

          {/* Subtle dot pattern */}
          <div className="absolute inset-0 opacity-[0.02]"
               style={{
                 backgroundImage: 'radial-gradient(currentColor 1px, transparent 1px)',
                 backgroundSize: '30px 30px'
               }}>
          </div>
        </div>
      </div>

      {/* Right content - auth form */}
      <div className="md:w-1/2 w-full flex flex-col">
        {/* Mobile logo (only shown on mobile) */}
        <header className="md:hidden w-full py-6 px-6 flex items-center border-b border-border/50">
          <Link href="/" className="flex items-center gap-2">
            <div className="h-9 w-9 rounded-lg bg-background flex items-center justify-center shadow-sm border border-border">
              <Timer className="h-5 w-5 text-primary/80" aria-hidden="true" />
            </div>
            <h1 className="text-lg font-bold">
              Pomodoro <span className="text-primary/80">365</span>
            </h1>
          </Link>
        </header>

        {/* Form area */}
        <div className="flex-1 flex flex-col items-center justify-center px-6 py-8 sm:px-12 relative">
          {/* Enhanced background decoration */}
          <div className="absolute top-1/4 right-0 h-72 w-72 bg-rose-100/15 dark:bg-rose-900/5 rounded-full blur-3xl -z-10"></div>
          <div className="absolute bottom-1/4 left-0 h-56 w-56 bg-rose-50/20 dark:bg-rose-800/5 rounded-full blur-3xl -z-10"></div>
          <div className="absolute top-2/3 right-1/4 h-32 w-32 bg-rose-200/10 dark:bg-rose-700/3 rounded-full blur-3xl -z-10"></div>

          <div className="w-full max-w-sm space-y-6">
            {children}
          </div>

        
        </div>

      </div>
    </div>
  );
}