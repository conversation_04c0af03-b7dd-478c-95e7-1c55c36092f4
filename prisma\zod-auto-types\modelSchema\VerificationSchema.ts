import { z } from 'zod';

/////////////////////////////////////////
// VERIFICATION SCHEMA
/////////////////////////////////////////

export const VerificationSchema = z.object({
  id: z.string().cuid(),
  identifier: z.string(),
  value: z.string(),
  expiresAt: z.coerce.date(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Verification = z.infer<typeof VerificationSchema>

/////////////////////////////////////////
// VERIFICATION PARTIAL SCHEMA
/////////////////////////////////////////

export const VerificationPartialSchema = VerificationSchema.partial()

export type VerificationPartial = z.infer<typeof VerificationPartialSchema>

export default VerificationSchema;
