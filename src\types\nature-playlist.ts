import { UserRole, NatureSoundCategory, MediaSource } from "@prisma/client";

export interface NatureSoundBasic {
  id: string;
  title: string;
  src: string | null;
}

export interface NatureSound extends NatureSoundBasic {
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
  creatorType: UserRole;
  userId: string;
  category: NatureSoundCategory[];
  source: MediaSource | null;
  artist?: string;
}

export interface Video {
  id: string;
  title: string;
  thumbnail: string;
  description?: string | null;
  src: string;
}

export interface NaturePlaylist {
  id: string;
  name: string;
  description: string | null;
  imageUrl: string | null;
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
  isDefault: boolean;
  userId: string;
  creatorType: UserRole;
  videos: Video[];
  natureSounds: NatureSound[];
  natureSoundOrder: string[];
}