import { useQuery } from "@tanstack/react-query";
import { InferResponseType } from "hono";
import client from "@/lib/trpc";

// Get dashboard statistics
export type GetDashboardStats_ResponseType = InferResponseType<
  (typeof client.api.admin)["dashboard"]["$get"],
  200
>;

export type GetDashboardStats_ResponseTypeSuccess = Extract<
  GetDashboardStats_ResponseType,
  { data: object }
>["data"];

export const useGetDashboardStats = () => {
  return useQuery<GetDashboardStats_ResponseTypeSuccess>({
    queryKey: ["admin", "dashboard"],
    queryFn: async () => {
      const response = await client.api.admin["dashboard"].$get();
      if (!response.ok) {
        throw new Error("Failed to fetch dashboard statistics");
      }
      const { data } = await response.json();
      return data;
    },
  });
}; 