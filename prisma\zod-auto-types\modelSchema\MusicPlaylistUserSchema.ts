import { z } from 'zod';
import { UserWithRelationsSchema, UserPartialWithRelationsSchema } from './UserSchema'
import type { UserWithRelations, UserPartialWithRelations } from './UserSchema'
import { MusicWithRelationsSchema, MusicPartialWithRelationsSchema } from './MusicSchema'
import type { MusicWithRelations, MusicPartialWithRelations } from './MusicSchema'
import { NatureSoundWithRelationsSchema, NatureSoundPartialWithRelationsSchema } from './NatureSoundSchema'
import type { NatureSoundWithRelations, NatureSoundPartialWithRelations } from './NatureSoundSchema'
import { VideoWithRelationsSchema, VideoPartialWithRelationsSchema } from './VideoSchema'
import type { VideoWithRelations, VideoPartialWithRelations } from './VideoSchema'

/////////////////////////////////////////
// MUSIC PLAYLIST USER SCHEMA
/////////////////////////////////////////

export const MusicPlaylistUserSchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(1),
  description: z.string().nullish(),
  imageUrl: z.union([z.instanceof(File), z.string().nullable()]).nullish(),
  isPublic: z.boolean(),
  userId: z.string(),
  musicOrder: z.string().array(),
  natureSoundOrder: z.string().array(),
  createdAt: z.union([z.date(), z.string().datetime()]),
  updatedAt: z.union([z.date(), z.string().datetime()]),
})

export type MusicPlaylistUser = z.infer<typeof MusicPlaylistUserSchema>

/////////////////////////////////////////
// MUSIC PLAYLIST USER PARTIAL SCHEMA
/////////////////////////////////////////

export const MusicPlaylistUserPartialSchema = MusicPlaylistUserSchema.partial()

export type MusicPlaylistUserPartial = z.infer<typeof MusicPlaylistUserPartialSchema>

/////////////////////////////////////////
// MUSIC PLAYLIST USER RELATION SCHEMA
/////////////////////////////////////////

export type MusicPlaylistUserRelations = {
  user: UserWithRelations;
  musics: MusicWithRelations[];
  natureSounds: NatureSoundWithRelations[];
  videos: VideoWithRelations[];
};

export type MusicPlaylistUserWithRelations = z.infer<typeof MusicPlaylistUserSchema> & MusicPlaylistUserRelations

export const MusicPlaylistUserWithRelationsSchema: z.ZodType<MusicPlaylistUserWithRelations> = MusicPlaylistUserSchema.merge(z.object({
  user: z.lazy(() => UserWithRelationsSchema),
  musics: z.lazy(() => MusicWithRelationsSchema).array(),
  natureSounds: z.lazy(() => NatureSoundWithRelationsSchema).array(),
  videos: z.lazy(() => VideoWithRelationsSchema).array(),
}))

/////////////////////////////////////////
// MUSIC PLAYLIST USER PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type MusicPlaylistUserPartialRelations = {
  user?: UserPartialWithRelations;
  musics?: MusicPartialWithRelations[];
  natureSounds?: NatureSoundPartialWithRelations[];
  videos?: VideoPartialWithRelations[];
};

export type MusicPlaylistUserPartialWithRelations = z.infer<typeof MusicPlaylistUserPartialSchema> & MusicPlaylistUserPartialRelations

export const MusicPlaylistUserPartialWithRelationsSchema: z.ZodType<MusicPlaylistUserPartialWithRelations> = MusicPlaylistUserPartialSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  musics: z.lazy(() => MusicPartialWithRelationsSchema).array(),
  natureSounds: z.lazy(() => NatureSoundPartialWithRelationsSchema).array(),
  videos: z.lazy(() => VideoPartialWithRelationsSchema).array(),
})).partial()

export type MusicPlaylistUserWithPartialRelations = z.infer<typeof MusicPlaylistUserSchema> & MusicPlaylistUserPartialRelations

export const MusicPlaylistUserWithPartialRelationsSchema: z.ZodType<MusicPlaylistUserWithPartialRelations> = MusicPlaylistUserSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  musics: z.lazy(() => MusicPartialWithRelationsSchema).array(),
  natureSounds: z.lazy(() => NatureSoundPartialWithRelationsSchema).array(),
  videos: z.lazy(() => VideoPartialWithRelationsSchema).array(),
}).partial())

export default MusicPlaylistUserSchema;
