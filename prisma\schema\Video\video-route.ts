import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { createVideoSchema, updateVideoSchema } from "./video-type";
import prisma from "@/lib/prisma";
import { UserVariable } from "../..";
import { UserRole } from "@prisma/client";
import { privateRoutesMiddleware } from "@/server/private/middleware";


const app = new Hono<{ Variables: UserVariable }>()
  // Get all videos
  .get("/", async (c) => {
    // const user = c.get("user");
    const isPublic = c.req.query("isPublic") === "true" ? true : c.req.query("isPublic") === "false" ? false : undefined;
    const isPremium = c.req.query("isPremium") === "true" ? true : c.req.query("isPremium") === "false" ? false : undefined;
    const musicPlaylistId = c.req.query("musicPlaylistId");
    const naturePlaylistId = c.req.query("naturePlaylistId");

    console.log({
      isPublic,
      isPremium,
      musicPlaylistId,
      naturePlaylistId,
    });

    const filters: Record<string, unknown> = {};

    // Add filters based on query parameters
    if (isPublic !== undefined) {
      filters.isPublic = isPublic;
    }

    if (isPremium !== undefined) {
      filters.isPremium = isPremium;
    }

    if (musicPlaylistId) {
      filters.musicPlaylistId = musicPlaylistId;
    }

    if (naturePlaylistId) {
      filters.naturePlaylistId = naturePlaylistId;
    }

    // Videos created by admin are always accessible
    // Videos created by users are accessible if:
    // 1. They're public, or
    // 2. They were created by the requesting user

    console.log({
      filters,
    });

    const videos = await prisma.video.findMany({
      where: {
        ...filters,
      },
      include: {
        musicPlaylist: {
          include: {
            musics: true,
          },
        },
        naturePlaylist: {
          include: {
            natureSounds: true,
          },
        },
      },
      orderBy: {
        order: 'asc'
      }
    });

      // // Transform the videos to add isFavorite flag
      // const transformedVideos = videos.map(video => ({
      //   ...video,
      //   isFavorite: video.favoriteBy.length > 0,
      //   favoriteBy: undefined, // Remove the favoriteBy array
      // }));

    return c.json({ data: videos });
  })

  // Get single video
  .get("/:id", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");

    const video = await prisma.video.findUnique({
      where: { id },
      include: {
        favoriteBy: {
          where: {
            // userId: user.id,
          },
          select: {
            id: true,
          },
        },
        musicPlaylist: {
          include: {
            musics: true,
          },
        },
        naturePlaylist: {
          include: {
            natureSounds: true,
          },
        },
      },
    });

    if (!video) {
      return c.json({ error: "Video not found" }, 404);
    }

    // Check if user can access this video
    const canAccess =
      video.isPublic ||
      video.creatorType === UserRole.ADMIN ||
      video.userId === user.id;

    if (!canAccess) {
      return c.json({ error: "Unauthorized" }, 403);
    }

    // Transform the video to add isFavorite flag
    const transformedVideo = {
      ...video,
      isFavorite: video.favoriteBy.length > 0,
      favoriteBy: undefined, // Remove the favoriteBy array
    };

    return c.json({ data: transformedVideo });
  })

  // Create video - only admin can create
  .post("/", privateRoutesMiddleware, zValidator("form", createVideoSchema), async (c) => {
    const user = c.get("user");

    if (!user) {
      return c.json({ error: "User not authenticated" }, 401);
    }

    // Check if user is admin
    if (user.role !== UserRole.ADMIN) {
      return c.json({ error: "Only admins can create videos" }, 403);
    }

    const { title, src, description, isPublic, isPremium, musicPlaylistId, naturePlaylistId, thumbnail, videoGenre } = c.req.valid("form");

    // Find the highest order value for a video
    const highestOrderVideo = await prisma.video.findFirst({
      orderBy: {
        order: 'desc'
      }
    });

    const newOrder = highestOrderVideo ? highestOrderVideo.order + 1 : 0;

    // Create video in database
    const video = await prisma.video.create({
      data: {
        title,
        src,
        description,
        isPublic: isPublic ?? true,
        isPremium: isPremium ?? false,
        musicPlaylistId: musicPlaylistId || null,
        naturePlaylistId: naturePlaylistId || null,
        thumbnail: thumbnail || "",
        userId: user.id,
        creatorType: user.role as UserRole, // Set creatorType based on user's role
        videoGenre: videoGenre || ["MEDITATION"],
        order: newOrder, // Set the new order value
      },
    });

    return c.json({ data: video });
  })

    // Reorder videos - only admin can reorder
    .patch("/reorder", privateRoutesMiddleware, async (c) => {
      const user = c.get("user");

      // Check if user is admin
      if (user.role !== UserRole.ADMIN) {
        return c.json({ error: "Only admins can reorder videos" }, 403);
      }

      const { videoOrders } = await c.req.json<{
        videoOrders: Array<{ id: string; order: number }>
      }>();
  
      if (!videoOrders || !Array.isArray(videoOrders) || videoOrders.length === 0) {
        return c.json({ error: "Invalid request format" }, 400);
      }
  
      // Get all videos to be reordered
      const videoIds = videoOrders.map(v => v.id);
      const videos = await prisma.video.findMany({
        where: {
          id: { in: videoIds }
        }
      });
  
      if (videos.length !== videoOrders.length) {
        return c.json({ error: "One or more videos not found" }, 404);
      }
  
      // Verify permissions - only admin can reorder any video
      const hasPermission = user.role === UserRole.ADMIN;
  
      if (!hasPermission) {
        return c.json({ error: "Unauthorized to reorder videos" }, 403);
      }
  
      // Perform reordering in a transaction
      const updates = await prisma.$transaction(
        videoOrders.map(({ id, order }) =>
          prisma.video.update({
            where: { id },
            data: { order },
          })
        )
      );
  
      return c.json({ 
        data: { 
          success: true,
          count: updates.length 
        } 
      });
    })

  // Update video - only owner or admin can update admin-created content
  .patch("/:id", privateRoutesMiddleware, zValidator("form", updateVideoSchema), async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");
    const updates = c.req.valid("form");

    // First check if video exists
    const existingVideo = await prisma.video.findUnique({
      where: { id },
    });

    if (!existingVideo) {
      return c.json({ error: "Video not found" }, 404);
    }

    // Security check: Only allow updates if:
    // 1. User owns the video, OR
    // 2. User is admin (can update any video), OR
    // 3. Admin-created content can only be updated by admins
    const canUpdate = 
      existingVideo.userId === user.id ||
      (user.role === UserRole.ADMIN) ||
      (existingVideo.creatorType === UserRole.ADMIN && user.role !== UserRole.ADMIN ? false : true);

    if (!canUpdate || (existingVideo.creatorType === UserRole.ADMIN && user.role !== UserRole.ADMIN)) {
      return c.json({ error: "Unauthorized to update this video" }, 403);
    }

    // Prepare update data with proper typing
    const updateData = {
      ...(updates.title !== undefined && { title: updates.title }),
      ...(updates.src !== undefined && { src: updates.src }),
      ...(updates.description !== undefined && { description: updates.description }),
      ...(updates.isPublic !== undefined && { isPublic: updates.isPublic }),
      ...(updates.isPremium !== undefined && { isPremium: updates.isPremium }),
      ...(updates.musicPlaylistId !== undefined && { musicPlaylistId: updates.musicPlaylistId }),
      ...(updates.naturePlaylistId !== undefined && { naturePlaylistId: updates.naturePlaylistId }),
      ...(updates.thumbnail !== undefined && { thumbnail: updates.thumbnail }),
      ...(updates.videoGenre !== undefined && { videoGenre: updates.videoGenre }),
    };

    // Update the video
    const updatedVideo = await prisma.video.update({
      where: { id },
      data: updateData,
    });

    return c.json({ data: updatedVideo });
  })

  // Delete video - only admin can delete
  .delete("/:id", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");

    // Check if user is admin
    if (user.role !== UserRole.ADMIN) {
      return c.json({ error: "Only admins can delete videos" }, 403);
    }

    // Find the video
    const video = await prisma.video.findUnique({
      where: { id },
    });

    if (!video) {
      return c.json({ error: "Video not found" }, 404);
    }

    // Delete video
    await prisma.video.delete({
      where: { id },
    });

    return c.json({ success: true });
  })

  // Toggle favorite status
  .post("/:id/favorite", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");

    // Check if the video exists
    const video = await prisma.video.findUnique({
      where: { id },
    });

    if (!video) {
      return c.json({ error: "Video not found" }, 404);
    }

    // Check if user has already favorited this video
    const existingFavorite = await prisma.favoriteVideo.findUnique({
      where: {
        userId_videoId: {
          userId: user.id,
          videoId: id,
        },
      },
    });

    let favorite;
    if (existingFavorite) {
      // If already favorited, remove the favorite
      await prisma.favoriteVideo.delete({
        where: {
          id: existingFavorite.id,
        },
      });
      favorite = false;
    } else {
      // If not favorited, add a favorite
      await prisma.favoriteVideo.create({
        data: {
          userId: user.id,
          videoId: id,
        },
      });
      favorite = true;
    }

    return c.json({ data: { isFavorite: favorite } });
  })

  // Get user's favorite videos
  .get("/favorites", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");

    const favoriteVideos = await prisma.favoriteVideo.findMany({
      where: {
        userId: user.id,
      },
      include: {
        video: {
          include: {
            musicPlaylist: {
              select: {
                id: true,
                name: true,
              },
            },
            naturePlaylist: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    // Transform the result to match our API format
    const videos = favoriteVideos.map(fav => ({
      ...fav.video,
      isFavorite: true,
    }));

    return c.json({ data: videos });
  })

  // Update video's music playlist - only admin can update
  .patch("/:id/musicPlaylist", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const videoId = c.req.param("id");
    const { musicPlaylistId } = await c.req.json<{ musicPlaylistId: string | null }>();

    // Check if user is admin
    if (user.role !== UserRole.ADMIN) {
      return c.json({ error: "Only admins can update video playlists" }, 403);
    }

    // Check if video exists
    const video = await prisma.video.findUnique({
      where: { id: videoId },
    });

    if (!video) {
      return c.json({ error: "Video not found" }, 404);
    }

    // If musicPlaylistId is provided, verify the playlist exists
    if (musicPlaylistId) {
      const musicPlaylist = await prisma.musicPlaylist.findUnique({
        where: { id: musicPlaylistId },
      });

      if (!musicPlaylist) {
        return c.json({ error: "Music playlist not found" }, 404);
      }
    }

    // Update video's music playlist
    const updatedVideo = await prisma.video.update({
      where: { id: videoId },
      data: {
        musicPlaylistId: musicPlaylistId,
      },
      include: {
        musicPlaylist: {
          include: {
            musics: true,
          },
        },
        naturePlaylist: {
          include: {
            natureSounds: true,
          },
        },
      },
    });

    return c.json({ data: updatedVideo });
  })
  
  // Update video's nature playlist - only admin can update
  .patch("/:id/naturePlaylist", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const videoId = c.req.param("id");
    const { naturePlaylistId } = await c.req.json<{ naturePlaylistId: string | null }>();

    // Check if user is admin
    if (user.role !== UserRole.ADMIN) {
      return c.json({ error: "Only admins can update video playlists" }, 403);
    }

    // Check if video exists
    const video = await prisma.video.findUnique({
      where: { id: videoId },
    });

    if (!video) {
      return c.json({ error: "Video not found" }, 404);
    }

    // If naturePlaylistId is provided, verify the playlist exists
    if (naturePlaylistId) {
      const naturePlaylist = await prisma.naturePlaylist.findUnique({
        where: { id: naturePlaylistId },
      });

      if (!naturePlaylist) {
        return c.json({ error: "Nature playlist not found" }, 404);
      }
    }

    // Update video's nature playlist
    const updatedVideo = await prisma.video.update({
      where: { id: videoId },
      data: {
        naturePlaylistId: naturePlaylistId,
      },
      include: {
        musicPlaylist: {
          include: {
            musics: true,
          },
        },
        naturePlaylist: {
          include: {
            natureSounds: true,
          },
        },
      },
    });

    return c.json({ data: updatedVideo });
  });

export default app;