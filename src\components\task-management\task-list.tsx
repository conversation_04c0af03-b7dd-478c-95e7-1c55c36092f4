'use client';

import { useState, useC<PERSON>back, useMemo } from 'react';
import { Plus, CheckCircle2, AlertCircle, Target, Zap, Clock, TrendingUp, Circle, Play, Check } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TaskItem } from './task-item';
import { TaskInlineForm } from './task-inline-form';
import { useTaskManagement, type UnifiedTask } from '@/hooks/useTaskManagement';
import { cn } from '@/lib/utils';

interface TaskListProps {
  searchTerm: string;
  showNewTaskForm: boolean;
  onCreateTaskSuccess: () => void;
  onCancelNewTask: () => void;
  onShowNewTaskForm: () => void;
  onTaskFocusStart?: () => void;
}

type TaskType = UnifiedTask;
type TabType = 'pending' | 'completed';

// Animation variants for smooth transitions
const taskAnimations = {
  initial: { 
    opacity: 0, 
    y: -12, 
    scale: 0.98
  },
  animate: { 
    opacity: 1, 
    y: 0, 
    scale: 1,
    transition: {
      type: 'spring',
      stiffness: 400,
      damping: 30,
      duration: 0.3
    }
  },
  exit: { 
    opacity: 0, 
    y: -8, 
    scale: 0.98,
    transition: {
      duration: 0.2,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
};

const addButtonAnimations = {
  hidden: { 
    opacity: 0,
    scale: 0.95
  },
  visible: { 
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.2,
      delay: 0.15, // Shorter delay
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
};

const formAnimations = {
  initial: { 
    opacity: 0,
    scale: 0.98
  },
  animate: { 
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.15,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  },
  exit: { 
    opacity: 0,
    scale: 0.98,
    transition: {
      duration: 0.1,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
};

// Tab Switcher Component
function TabSwitcher({ 
  activeTab, 
  onTabChange, 
  pendingCount, 
  completedCount 
}: { 
  activeTab: TabType; 
  onTabChange: (tab: TabType) => void;
  pendingCount: number;
  completedCount: number;
}) {
  return (
    <div className="flex items-center gap-0.5 p-0.5 bg-muted/30 rounded-md mb-3">
      <button
        onClick={() => onTabChange('pending')}
        className={cn(
          "flex items-center gap-1.5 px-2.5 py-1.5 rounded-md text-xs font-medium transition-all duration-150 flex-1 justify-center relative",
          activeTab === 'pending'
            ? "bg-white text-red-700 shadow-sm"
            : "text-muted-foreground hover:text-red-600 hover:bg-white/60"
        )}
      >
        <Play className={cn(
          "h-3 w-3",
          activeTab === 'pending' ? "text-red-600" : "text-muted-foreground"
        )} />
        <span>Active</span>
        {pendingCount > 0 && (
          <span 
            className={cn(
              "text-[10px] px-1 py-0 h-4 min-w-4 rounded-full flex items-center justify-center font-medium",
              activeTab === 'pending' 
                ? "bg-red-100 text-red-700" 
                : "bg-muted-foreground/20 text-muted-foreground"
            )}
          >
            {pendingCount}
          </span>
        )}
      </button>
      
      <button
        onClick={() => onTabChange('completed')}
        className={cn(
          "flex items-center gap-1.5 px-2.5 py-1.5 rounded-md text-xs font-medium transition-all duration-150 flex-1 justify-center relative",
          activeTab === 'completed'
            ? "bg-white text-green-700 shadow-sm"
            : "text-muted-foreground hover:text-green-600 hover:bg-white/60"
        )}
      >
        <Check className={cn(
          "h-3 w-3",
          activeTab === 'completed' ? "text-green-600" : "text-muted-foreground"
        )} />
        <span>Completed</span>
        {completedCount > 0 && (
          <span 
            className={cn(
              "text-[10px] px-1 py-0 h-4 min-w-4 rounded-full flex items-center justify-center font-medium",
              activeTab === 'completed' 
                ? "bg-green-100 text-green-700" 
                : "bg-muted-foreground/20 text-muted-foreground"
            )}
          >
            {completedCount}
          </span>
        )}
      </button>
    </div>
  );
}

// Simplified Loading Component
function TaskListSkeleton() {
  return (
    <div className="p-4 space-y-3">
      {[...Array(3)].map((_, index) => (
        <div
          key={index}
          className="p-3 rounded-lg border border-border/30 bg-white shadow-sm"
        >
          <div className="flex items-center gap-3">
            <div className="w-4 h-4 rounded-full bg-muted animate-pulse" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-muted rounded animate-pulse" />
              <div className="h-3 bg-muted/70 rounded w-2/3 animate-pulse" />
            </div>
            <div className="w-6 h-6 bg-muted rounded animate-pulse" />
          </div>
        </div>
      ))}
    </div>
  );
}

// Empty State Component for tabs
function EmptyTabState({ 
  tabType, 
  searchTerm, 
  onShowNewTaskForm 
}: { 
  tabType: TabType; 
  searchTerm: string; 
  onShowNewTaskForm: () => void;
}) {
  const isSearching = !!searchTerm;

  if (tabType === 'pending') {
    return (
      <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
        <div className="space-y-6">
          {/* Icon */}
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-primary/10 to-primary/5 rounded-2xl flex items-center justify-center">
            {isSearching ? (
              <AlertCircle className="h-8 w-8 text-muted-foreground/70" />
            ) : (
              <Target className="h-8 w-8 text-primary/70" />
            )}
          </div>

          {/* Content */}
          <div className="space-y-3 max-w-xs">
            <h3 className="text-lg font-semibold text-foreground/90">
              {isSearching ? 'No matching tasks' : 'Ready to focus?'}
            </h3>
            
            <p className="text-sm text-muted-foreground leading-relaxed">
              {isSearching 
                ? `No active tasks found for "${searchTerm}". Try adjusting your search terms.`
                : 'Create your first task and start building productive habits that stick.'
              }
            </p>
          </div>

          {/* Action */}
          {!isSearching && (
            <Button
              onClick={onShowNewTaskForm}
              className="gap-2 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80"
            >
              <Plus className="h-4 w-4" />
              <span>Create Your First Task</span>
            </Button>
          )}

          {/* Features */}
          {!isSearching && (
            <div className="grid grid-cols-3 gap-3 pt-4">
              <div className="flex flex-col items-center gap-1 p-3 bg-yellow-50/50 rounded-lg border border-yellow-200/30">
                <Zap className="h-4 w-4 text-yellow-600" />
                <span className="text-xs text-muted-foreground font-medium">Quick Setup</span>
              </div>
              <div className="flex flex-col items-center gap-1 p-3 bg-blue-50/50 rounded-lg border border-blue-200/30">
                <Clock className="h-4 w-4 text-blue-600" />
                <span className="text-xs text-muted-foreground font-medium">Time Tracking</span>
              </div>
              <div className="flex flex-col items-center gap-1 p-3 bg-green-50/50 rounded-lg border border-green-200/30">
                <TrendingUp className="h-4 w-4 text-green-600" />
                <span className="text-xs text-muted-foreground font-medium">Progress Insights</span>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Completed tab empty state
  return (
    <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
      <div className="space-y-6">
        {/* Icon */}
        <div className="w-16 h-16 mx-auto bg-gradient-to-br from-green-50 to-green-100 rounded-2xl flex items-center justify-center border border-green-200/40">
          {isSearching ? (
            <AlertCircle className="h-8 w-8 text-muted-foreground/70" />
          ) : (
            <CheckCircle2 className="h-8 w-8 text-green-600" />
          )}
        </div>

        {/* Content */}
        <div className="space-y-3 max-w-xs">
          <h3 className="text-lg font-semibold text-foreground/90">
            {isSearching ? 'No matching completed tasks' : 'No completed tasks yet'}
          </h3>
          
          <p className="text-sm text-muted-foreground leading-relaxed">
            {isSearching 
              ? `No completed tasks found for "${searchTerm}".`
              : 'Complete some tasks to see your progress here. Every task completed is a step forward!'
            }
          </p>
        </div>
      </div>
    </div>
  );
}

// Simplified Error State Component
function ErrorState() {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 text-center bg-white">
      <div className="w-14 h-14 mx-auto bg-gradient-to-br from-destructive/10 to-destructive/5 rounded-2xl flex items-center justify-center mb-4">
        <AlertCircle className="h-7 w-7 text-destructive" />
      </div>
      
      <h3 className="text-base font-semibold text-foreground/90 mb-2">
        Unable to load tasks
      </h3>
      
      <p className="text-sm text-muted-foreground mb-4 max-w-xs">
        We're having trouble connecting to our servers. Please check your connection and try again.
      </p>

      <Button 
        variant="outline" 
        size="sm"
        onClick={() => window.location.reload()}
        className="gap-2"
      >
        <span>Try Again</span>
      </Button>
    </div>
  );
}

// Add Task Button Component with simple animations
function AddTaskButton({ onShowNewTaskForm, showNewTaskForm }: { onShowNewTaskForm: () => void; showNewTaskForm: boolean }) {
  return (
    <div className="mb-2">
      <Button
        onClick={onShowNewTaskForm}
        variant="outline"
        className="w-full h-9 gap-2 bg-white hover:bg-gray-50/80 border border-dashed border-gray-300 hover:border-primary/50 transition-all duration-200 group text-sm rounded-lg p-3"
        disabled={showNewTaskForm}
      >
        <div className="flex items-center gap-2 text-muted-foreground group-hover:text-primary transition-colors duration-200">
          <Plus className="h-3.5 w-3.5 transition-transform duration-200 group-hover:rotate-90" />
          <span className="font-medium">Add task</span>
        </div>
      </Button>
    </div>
  );
}

export function TaskList({
  searchTerm,
  showNewTaskForm,
  onCreateTaskSuccess,
  onCancelNewTask,
  onShowNewTaskForm,
  onTaskFocusStart
}: TaskListProps) {
  // Tab state
  const [activeTab, setActiveTab] = useState<TabType>('pending');
  
  // Track which specific task is being operated on
  const [operatingTaskId, setOperatingTaskId] = useState<string | null>(null);
  const [operationType, setOperationType] = useState<'update' | 'delete' | null>(null);
  const [editingTaskId, setEditingTaskId] = useState<string | null>(null);

  // Add state to track recent task creation and control add button visibility
  const [isTaskJustCreated, setIsTaskJustCreated] = useState(false);
  const [showAddButton, setShowAddButton] = useState(true);
  const [isCreatingTask, setIsCreatingTask] = useState(false);

  // Use the hybrid task management hook
  const {
    tasks,
    isLoading,
    error,
    updateTask,
    deleteTask
  } = useTaskManagement({
    filters: {
      searchTerm
    }
  });

  const handleToggleTask = useCallback(async (task: TaskType) => {
    setOperatingTaskId(task.id);
    setOperationType('update');
    
    try {
      await updateTask(task.id, { completed: !task.completed });
    } catch (error) {
      console.error('Failed to toggle task:', error);
    } finally {
      setOperatingTaskId(null);
      setOperationType(null);
    }
  }, [updateTask]);

  const handleDeleteTask = useCallback(async (taskId: string) => {
    setOperatingTaskId(taskId);
    setOperationType('delete');
    
    try {
      await deleteTask(taskId);
    } catch (error) {
      console.error('Failed to delete task:', error);
    } finally {
      setOperatingTaskId(null);
      setOperationType(null);
    }
  }, [deleteTask]);

  const handleEditTask = useCallback((taskId: string) => {
    setEditingTaskId(taskId);
  }, []);

  const handleEditSuccess = useCallback(() => {
    setEditingTaskId(null);
  }, []);

  const handleEditCancel = useCallback(() => {
    setEditingTaskId(null);
  }, []);

  // Handle new task form cancel with add button state reset
  const handleNewTaskCancel = useCallback(() => {
    // Reset add button state in case it was hidden
    setShowAddButton(true);
    setIsTaskJustCreated(false);
    setIsCreatingTask(false);
    onCancelNewTask();
  }, [onCancelNewTask]);

  // Handle task creation start (when form is submitted)
  const handleCreateTaskStart = useCallback(() => {
    setIsCreatingTask(true);
  }, []);

  // Improved task creation success handler with quick transition
  const handleCreateTaskSuccessInternal = useCallback(() => {
    // Mark that a task was just created
    setIsTaskJustCreated(true);
    setIsCreatingTask(false);
    
    // Hide add button immediately to prevent flickering
    setShowAddButton(false);
    
    // Close form immediately since optimistic updates show the task instantly
    onCreateTaskSuccess();
    
    // Show add button again after a shorter delay for responsiveness
    setTimeout(() => {
      setShowAddButton(true);
      setIsTaskJustCreated(false);
    }, 350); // Even shorter delay for instant responsiveness
  }, [onCreateTaskSuccess]);

  // Check if a specific task is loading
  const isTaskLoading = useCallback((taskId: string) => {
    return operatingTaskId === taskId && operationType !== null;
  }, [operatingTaskId, operationType]);

  // Group tasks by completion status with optimistic task handling
  const groupedTasks = useMemo(() => {
    const incomplete = tasks.filter((task: TaskType) => !task.completed);
    const completed = tasks.filter((task: TaskType) => task.completed);
    return { incomplete, completed };
  }, [tasks]);

  // Get current tab tasks
  const currentTabTasks = useMemo(() => {
    return activeTab === 'pending' ? groupedTasks.incomplete : groupedTasks.completed;
  }, [activeTab, groupedTasks]);

  // Improved task rendering with smooth framer-motion animations
  const renderTaskOrEdit = useCallback((task: TaskType) => {
    return (
      <motion.div
        key={task.id}
        layout
        variants={taskAnimations}
        initial={task.isOptimistic ? "initial" : false}
        animate="animate"
        exit="exit"
        className="mb-2"
      >
        {editingTaskId === task.id ? (
          <motion.div
            variants={formAnimations}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            <TaskInlineForm
              editTask={task}
              isEditing={true}
              onSuccess={handleEditSuccess}
              onCancel={handleEditCancel}
              placeholder="Update task title..."
            />
          </motion.div>
        ) : (
          <TaskItem
            task={task}
            onToggle={() => handleToggleTask(task)}
            onDelete={() => handleDeleteTask(task.id)}
            onEdit={() => handleEditTask(task.id)}
            isLoading={isTaskLoading(task.id)}
            onTaskFocusStart={onTaskFocusStart}
          />
        )}
      </motion.div>
    );
  }, [editingTaskId, handleToggleTask, handleDeleteTask, handleEditTask, isTaskLoading, handleEditSuccess, handleEditCancel, onTaskFocusStart]);

  // Only show skeleton for initial loading when there are no tasks at all
  if (isLoading && tasks.length === 0) {
    return <TaskListSkeleton />;
  }

  // Error State
  if (error) {
    return <ErrorState />;
  }

  // Calculate if we have any tasks
  const hasAnyTasks = (groupedTasks.incomplete.length + groupedTasks.completed.length) > 0;

  return (
    <div className="relative bg-white min-h-full flex flex-col">
      {/* Task Content */}
      <div className="flex-1">
        {/* Empty State - only show when no tasks at all and not showing new task form */}
        {!hasAnyTasks && !showNewTaskForm ? (
          <EmptyTabState 
            tabType="pending"
            searchTerm={searchTerm} 
            onShowNewTaskForm={onShowNewTaskForm} 
          />
        ) : (
          <div className="p-4 space-y-1 bg-white">
            {/* Tab Switcher - only show when we have tasks or are creating one */}
            {(hasAnyTasks || showNewTaskForm) && (
              <TabSwitcher
                activeTab={activeTab}
                onTabChange={setActiveTab}
                pendingCount={groupedTasks.incomplete.length}
                completedCount={groupedTasks.completed.length}
              />
            )}

            {/* Tab Content */}
            <div className="min-h-[300px]">
              {/* Current Tab Tasks with AnimatePresence for smooth exits */}
              <AnimatePresence mode="popLayout">
                {currentTabTasks.map((task) =>
                  renderTaskOrEdit(task)
                )}
              </AnimatePresence>

              {/* Add Task Button and Form - Seamless transition without layout shifts */}
              <div className="mb-2">
                <AnimatePresence mode="wait" initial={false}>
                  {activeTab === 'pending' && !showNewTaskForm && !isCreatingTask && hasAnyTasks && showAddButton ? (
                    <motion.div
                      key="add-button"
                      variants={addButtonAnimations}
                      initial="hidden"
                      animate="visible"
                      exit="hidden"
                    >
                      <AddTaskButton 
                        onShowNewTaskForm={onShowNewTaskForm}
                        showNewTaskForm={showNewTaskForm}
                      />
                    </motion.div>
                  ) : activeTab === 'pending' && showNewTaskForm && !isCreatingTask ? (
                    <motion.div
                      key="new-task-form"
                      variants={formAnimations}
                      initial="initial"
                      animate="animate"
                      exit="exit"
                    >
                      <TaskInlineForm
                        onSuccess={handleCreateTaskSuccessInternal}
                        onCancel={handleNewTaskCancel}
                        onSubmitStart={handleCreateTaskStart}
                        placeholder="What would you like to work on?"
                      />
                    </motion.div>
                  ) : null}
                </AnimatePresence>
              </div>

              {/* Empty state for current tab */}
              {currentTabTasks.length === 0 && !showNewTaskForm && hasAnyTasks && (
                <EmptyTabState 
                  tabType={activeTab}
                  searchTerm={searchTerm} 
                  onShowNewTaskForm={onShowNewTaskForm} 
                />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 