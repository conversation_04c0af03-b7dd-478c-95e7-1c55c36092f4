"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Plus,
  Music2,
  Volume2,
  ListMusic,
  Loader2
} from "lucide-react"
import { useGetMusicPlaylistsUser } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { toast } from "sonner"
import { PlaylistFormSheet } from "./playlist-form-sheet"

interface AddToPlaylistDialogProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  itemType: "music" | "nature-sound"
  itemId: string
  itemTitle: string
  onAddToPlaylist: (playlistId: string) => Promise<void>
}

export function AddToPlaylistDialog({
  isOpen,
  onOpenChange,
  itemType,
  itemTitle,
  onAddToPlaylist
}: Omit<AddToPlaylistDialogProps, 'itemId'>) {
  const [addingToPlaylistId, setAddingToPlaylistId] = useState<string | null>(null)
  const [isCreateSheetOpen, setIsCreateSheetOpen] = useState(false)
  
  const { data: playlists, isLoading } = useGetMusicPlaylistsUser()

  const handleAddToPlaylist = async (playlistId: string) => {
    if (addingToPlaylistId) return

    setAddingToPlaylistId(playlistId)
    try {
      await onAddToPlaylist(playlistId)
      toast.success(`${itemType === "music" ? "Music" : "Natural sound"} added to playlist`)
      onOpenChange(false)
    } catch (error) {
      console.error(error)
      toast.error(`Failed to add ${itemType === "music" ? "music" : "natural sound"} to playlist`)
    } finally {
      setAddingToPlaylistId(null)
    }
  }

  const handleCreateFirstPlaylist = () => {
    setIsCreateSheetOpen(true)
    onOpenChange(false)
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/30 dark:to-rose-900/30">
                {itemType === "music" ? (
                  <Music2 className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                ) : (
                  <Volume2 className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                )}
              </div>
              <div className="flex-1">
                <DialogTitle className="text-lg font-semibold">Add to Playlist</DialogTitle>
                <DialogDescription className="mt-1 text-sm">
                  Add "{itemTitle}" to one of your playlists
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="mt-4">
            {isLoading ? (
              <div className="space-y-2">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="flex items-center gap-3 p-3 rounded-lg border">
                    <Skeleton className="h-10 w-10 rounded-lg" />
                    <div className="flex-1 space-y-1">
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-3 w-1/2" />
                    </div>
                    <Skeleton className="h-8 w-16" />
                  </div>
                ))}
              </div>
            ) : !playlists || playlists.length === 0 ? (
              <div className="text-center py-8">
                <div className="p-3 rounded-full bg-muted/50 w-fit mx-auto mb-4">
                  <ListMusic className="h-6 w-6 text-muted-foreground" />
                </div>
                <h3 className="font-medium text-sm mb-2">No playlists yet</h3>
                <p className="text-xs text-muted-foreground mb-4">
                  Create your first playlist to start organizing your {itemType === "music" ? "music" : "natural sounds"}
                </p>
                <Button onClick={handleCreateFirstPlaylist} size="sm" className="gap-2">
                  <Plus className="h-4 w-4" />
                  Create First Playlist
                </Button>
              </div>
            ) : (
              <ScrollArea className="max-h-[300px]">
                <div className="space-y-2">
                  <AnimatePresence>
                    {playlists.map((playlist, index) => (
                      <motion.div
                        key={playlist.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className="flex items-center gap-3 p-3 rounded-lg border border-border/50 hover:border-border hover:bg-muted/20 transition-all duration-200"
                      >
                        <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/30 dark:to-rose-900/30 flex items-center justify-center">
                          <ListMusic className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-sm line-clamp-1">{playlist.name}</h3>
                          {playlist.description && (
                            <p className="text-xs text-muted-foreground line-clamp-1 mt-0.5">
                              {playlist.description}
                            </p>
                          )}
                        </div>
                        <Button
                          onClick={() => handleAddToPlaylist(playlist.id)}
                          disabled={addingToPlaylistId === playlist.id}
                          size="sm"
                          className={cn(
                            "h-8 px-3 text-xs transition-all duration-200",
                            "bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white"
                          )}
                        >
                          {addingToPlaylistId === playlist.id ? (
                            <>
                              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                              Adding...
                            </>
                          ) : (
                            <>
                              <Plus className="h-3 w-3 mr-1" />
                              Add
                            </>
                          )}
                        </Button>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              </ScrollArea>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Create Playlist Sheet */}
      <PlaylistFormSheet 
        isOpen={isCreateSheetOpen} 
        onOpenChange={setIsCreateSheetOpen}
      />
    </>
  )
}
