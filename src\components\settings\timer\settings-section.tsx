'use client';

import { ReactNode } from 'react';
import { Info } from 'lucide-react';
import { motion } from 'framer-motion';
import { NumberControl } from './number-control';
import { PresetButton } from './preset-button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';

interface SettingsSectionProps {
  title: string;
  description: string;
  icon: ReactNode;
  infoText: string;
  value: number;
  onValueChange: (value: number) => void;
  min: number;
  max: number;
  presets: Array<{ value: number; label: string; description: string; isDefault?: boolean }>;
  onPresetClick: (value: number) => void;
  colorScheme: 'blue' | 'emerald' | 'purple' | 'indigo';
  selectedValue: number;
}

const iconClasses = {
  blue: "bg-blue-100/80 dark:bg-blue-900/40 text-blue-600 dark:text-blue-400",
  emerald: "bg-emerald-100/80 dark:bg-emerald-900/40 text-emerald-600 dark:text-emerald-400",
  purple: "bg-purple-100/80 dark:bg-purple-900/40 text-purple-600 dark:text-purple-400",
  indigo: "bg-indigo-100/80 dark:bg-indigo-900/40 text-indigo-600 dark:text-indigo-400"
};

export function SettingsSection({
  title,
  description,
  icon,
  infoText,
  value,
  onValueChange,
  min,
  max,
  presets,
  onPresetClick,
  colorScheme,
  selectedValue
}: SettingsSectionProps) {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center px-0.5">
        <div className="flex items-center gap-2">
          <motion.div 
            className={`flex items-center justify-center w-8 h-8 rounded-full ${iconClasses[colorScheme]}`}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.2 }}
          >
            {icon}
          </motion.div>
          <div>
            <div className="flex items-center gap-1.5">
              <h3 className="text-sm font-medium">{title}</h3>
              <TooltipProvider>
                <Tooltip delayDuration={300}>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-5 w-5 rounded-full">
                      <Info className="h-2.5 w-2.5 text-muted-foreground" />
                      <span className="sr-only">{title} info</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top" align="center" className="max-w-xs text-xs">
                    <p>{infoText}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <p className="text-xs text-muted-foreground">
              {description}
            </p>
          </div>
        </div>

        <NumberControl
          value={value}
          onValueChange={onValueChange}
          min={min}
          max={max}
          color={colorScheme}
          ariaLabel={`${title.toLowerCase()} in minutes`}
        />
      </div>

      <div className="grid grid-cols-3 gap-2">
        {presets.map((preset) => (
          <PresetButton
            key={preset.value}
            label={preset.label}
            description={preset.description}
            isDefault={preset.isDefault}
            isSelected={selectedValue === preset.value}
            onClick={() => onPresetClick(preset.value)}
            colorScheme={colorScheme}
          />
        ))}
      </div>
    </div>
  );
} 