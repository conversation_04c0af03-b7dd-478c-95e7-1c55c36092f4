import { z } from "zod";
import { NaturePlaylistSchema, NaturePlaylistPartialSchema } from "@types";

export const createNaturePlaylistSchema = NaturePlaylistSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
  creatorType: true,
  isDefault: true,
  natureSoundOrder: true,
}).extend({
  imageUrl: z
    .string()
    .transform((value) => {
      // Handle empty strings, "undefined" strings, and null/undefined values
      if (!value || value === "" || value === "undefined" || value === "null") {
        return undefined;
      }
      return value;
    })
    .optional()
    .refine((value) => {
      // Only validate as URL if value exists and is not empty
      if (!value) return true;
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    }, {
      message: "Please enter a valid URL"
    }),
  isPublic: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional().default(false),
  description: z.string().optional(),
  natureSoundIds: z.array(z.string()).optional(),
  videoIds: z.array(z.string()).optional(),
});

export const updateNaturePlaylistSchema = NaturePlaylistPartialSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
  creatorType: true,
  isDefault: true,
}).extend({
  imageUrl: z
    .string()
    .transform((value) => {
      // Handle empty strings, "undefined" strings, and null/undefined values
      if (!value || value === "" || value === "undefined" || value === "null") {
        return undefined;
      }
      return value;
    })
    .optional()
    .refine((value) => {
      // Only validate as URL if value exists and is not empty
      if (!value) return true;
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    }, {
      message: "Please enter a valid URL"
    }),
  isPublic: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional(),
  description: z.string().optional(),
  natureSoundIds: z.preprocess(
    (val) => Array.isArray(val) ? val : typeof val === 'string' ? [val] : val,
    z.array(z.string()).optional()
  ),
  videoIds: z.preprocess(
    (val) => Array.isArray(val) ? val : typeof val === 'string' ? [val] : val,
    z.array(z.string()).optional()
  ),
});

export type CreateNaturePlaylistInput = z.infer<typeof createNaturePlaylistSchema>;
export type UpdateNaturePlaylistInput = z.infer<typeof updateNaturePlaylistSchema>; 