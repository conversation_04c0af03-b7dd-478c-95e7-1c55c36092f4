// Video.prisma
model Video {
    id          String   @id @default(cuid())
    title       String /// @zod.custom.use(z.string().min(1))
    src         String /// @zod.custom.use(z.string().url())
    thumbnail   String /// @zod.custom.use(z.string().url())
    description String?  @db.Text
    isPublic    Boolean  @default(true)
    creatorType UserRole @default(ADMIN) // To distinguish admin vs user created content

    videoGenre VideoGenre[] @default([MEDITATION])

    // User who created this video (optional)
    userId String?
    user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

    // Relationships with playlists
    musicPlaylistId String?
    musicPlaylist   MusicPlaylist? @relation(fields: [musicPlaylistId], references: [id], onDelete: SetNull)

    musicPlaylistsUser MusicPlaylistUser[]

    naturePlaylistId String?
    naturePlaylist   NaturePlaylist? @relation(fields: [naturePlaylistId], references: [id], onDelete: SetNull)

    // User favorites
    favoriteBy FavoriteVideo[]

    order Int @default(0)

    isPremium Boolean @default(false)

    createdAt DateTime @default(now()) /// @zod.custom.use(z.union([z.date(), z.string().datetime()]))
    updatedAt DateTime @updatedAt /// @zod.custom.use(z.union([z.date(), z.string().datetime()]))

    @@index([userId])
    @@index([musicPlaylistId])
    @@index([naturePlaylistId])
}

enum VideoGenre {
    // Mindfulness & Relaxation
    MEDITATION
    AMBIENT

    // Natural Environments
    NATURE
    FOREST
    OCEAN
    RAIN
    SNOW
    WATERFALL
    STREAM
    MOUNTAIN
    GARDEN
    SUNRISE_SUNSET
    NIGHT_SKY

    // Urban & Indoor Settings
    COFFEE

    // Urban & Indoor Settings
    HOUSE

    // Animal & Wildlife
    ANIMALS

    // Travel & Exploration
    TRAVEL

    // Creative & Artistic
    ART
    MINIMAL
    ABSTRACT

    // Study & Focus
    STUDY
    LOFI
}

// Favorite relationships
model FavoriteVideo {
    id        String   @id @default(cuid())
    userId    String
    videoId   String
    user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
    video     Video    @relation(fields: [videoId], references: [id], onDelete: Cascade)
    createdAt DateTime @default(now())

    @@unique([userId, videoId])
    @@index([userId])
    @@index([videoId])
}
