"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export interface MonthPickerProps {
  month: Date | undefined
  onSelect: (month: Date) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function MonthPicker({
  month,
  onSelect,
  placeholder = "Select month",
  className,
  disabled = false,
}: MonthPickerProps) {
  const [currentYear, setCurrentYear] = React.useState(() => new Date().getFullYear())
  
  // Calculate the selected month and year
  const selectedMonth = month ? month.getMonth() : undefined
  const selectedYear = month ? month.getFullYear() : undefined
  
  // Handle month selection
  const handleSelectMonth = (monthIndex: number) => {
    const newDate = new Date(currentYear, monthIndex, 1)
    onSelect(newDate)
  }
  
  // Navigate to previous year
  const prevYear = () => {
    setCurrentYear(currentYear - 1)
  }
  
  // Navigate to next year
  const nextYear = () => {
    setCurrentYear(currentYear + 1)
  }
  
  // Get month names
  const monthNames = [
    "January", "February", "March", "April", 
    "May", "June", "July", "August", 
    "September", "October", "November", "December"
  ]
  
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !month && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {month ? format(month, "MMMM yyyy") : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[280px] p-0" align="start">
        <div className="p-3">
          <div className="flex items-center justify-between mb-4">
            <Button
              variant="outline"
              size="icon"
              className="h-7 w-7"
              onClick={prevYear}
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only">Previous Year</span>
            </Button>
            <div className="font-medium">{currentYear}</div>
            <Button
              variant="outline"
              size="icon"
              className="h-7 w-7"
              onClick={nextYear}
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">Next Year</span>
            </Button>
          </div>
          <div className="grid grid-cols-3 gap-2">
            {monthNames.map((name, i) => {
              const isSelected = selectedYear === currentYear && selectedMonth === i
              return (
                <Button
                  key={i}
                  variant={isSelected ? "default" : "outline"}
                  size="sm"
                  className={cn(
                    "h-9",
                    isSelected && "bg-primary text-primary-foreground"
                  )}
                  onClick={() => handleSelectMonth(i)}
                >
                  {name.substring(0, 3)}
                </Button>
              )
            })}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
} 