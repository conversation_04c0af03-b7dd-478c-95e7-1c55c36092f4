import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FileVideo2 } from "lucide-react";

export default function NotFound() {
  return (
    <div className="container mx-auto py-12">
      <div className="flex flex-col items-center justify-center space-y-4 text-center">
        <div className="rounded-full bg-muted p-4">
          <FileVideo2 className="h-12 w-12 text-muted-foreground" />
        </div>
        <h2 className="text-2xl font-bold">Video Not Found</h2>
        <p className="text-muted-foreground">
          The video you&apos;re looking for doesn&apos;t exist or has been removed.
        </p>
        <Button asChild>
          <Link href="/admin/videos">Back to Videos</Link>
        </Button>
      </div>
    </div>
  );
} 