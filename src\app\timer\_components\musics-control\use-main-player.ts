import { useCallback, useEffect, useRef, useState } from 'react';
import { MusicControlProps, PlaybackMode } from './types';

// Type guard to check if playlist has musics
function hasMusicPlaylist(playlist: MusicControlProps['playlist']): playlist is {
  id: string;
  name: string;
  musics: {
    id: string;
    title: string;
    src?: string | null;
    createdAt?: string;
    updatedAt?: string;
    isPublic?: boolean;
    creatorType?: string;
    userId?: string;
    genres?: string[];
  }[];
  [key: string]: any;
} {
  return playlist !== null && 
         typeof playlist === 'object' && 
         'musics' in playlist && 
         Array.isArray(playlist.musics) && 
         playlist.musics.length > 0;
}

export function useMainPlayer(playlist: MusicControlProps['playlist']) {
  // Main music player state
  const [currentMusicIndex, setCurrentMusicIndex] = useState(0);
  const [isMainPlaying, setIsMainPlaying] = useState(false);
  const [mainVolume, setMainVolume] = useState(70);
  const [isMainMuted, setIsMainMuted] = useState(false);
  const [playbackMode, setPlaybackMode] = useState<PlaybackMode>(PlaybackMode.LOOP_ALL);
  const mainAudioRef = useRef<HTMLAudioElement | null>(null);
  const isFirstRenderRef = useRef(true);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  // Define function references to avoid circular dependencies
  const playMainTrackRef = useRef<((index: number) => void) | null>(null);
  const checkAndHandleLastTrackRef = useRef<(() => boolean) | null>(null);
  const handleMainTrackEndedRef = useRef<(() => void) | null>(null);
  const handleMainTrackEndedWithIndexRef = useRef<((index: number) => void) | null>(null);

  // Update audio.loop property when playback mode changes
  useEffect(() => {
    if (mainAudioRef.current) {
      // Update loop property based on playback mode
      mainAudioRef.current.loop = playbackMode === PlaybackMode.LOOP_ONE;
      
      // Store the current playback mode on the audio element
      mainAudioRef.current.dataset.playbackMode = playbackMode;
    }
  }, [playbackMode]);

  // Create a safer version of handleMainTrackEnded that takes the index as a parameter
  const handleMainTrackEndedWithIndex = useCallback((index: number) => {
    if (!hasMusicPlaylist(playlist)) {
      return;
    }

    // Handle different playback modes
    let nextIndex: number;
    
    switch (playbackMode) {
      case PlaybackMode.LOOP_ONE:
        // For LOOP_ONE, we should replay the same track
        nextIndex = index;
        
        // When using loop=true, this function shouldn't be called for LOOP_ONE mode
        // But if somehow it is called, make sure we keep playing the same track
        if (mainAudioRef.current) {
          // Ensure loop is set to true
          mainAudioRef.current.loop = true;
          
          // If we're already at the current index, simply restart the track
          if (index === currentMusicIndex) {
            mainAudioRef.current.currentTime = 0;
            mainAudioRef.current.play();
            return;
          }
        }
        break;
      
      case PlaybackMode.SHUFFLE:
        // Play a random track (excluding the current one if possible)
        if (playlist.musics.length > 1) {
          let randomIndex;
          do {
            randomIndex = Math.floor(Math.random() * playlist.musics.length);
          } while (randomIndex === index && playlist.musics.length > 1);
          nextIndex = randomIndex;
        } else {
          // If there's only one track, play it again
          nextIndex = 0;
        }
        break;
      
      case PlaybackMode.LOOP_ALL:
      default:
        // Explicitly check if we're at the last track using the passed index
        const isLastTrack = index === playlist.musics.length - 1;
        nextIndex = isLastTrack ? 0 : (index + 1) % playlist.musics.length;
        break;
    }

    // Use playMainTrackRef to avoid circular dependency
    if (playMainTrackRef.current) {
      playMainTrackRef.current(nextIndex);
    }
  }, [playlist, playbackMode, mainAudioRef, currentMusicIndex]);

  // Store the function in the ref
  useEffect(() => {
    handleMainTrackEndedWithIndexRef.current = handleMainTrackEndedWithIndex;
  }, [handleMainTrackEndedWithIndex]);

  // Enhance the handleMainTrackEnded function to use the current state
  const handleMainTrackEnded = useCallback(() => {
    // Use the version that takes the index explicitly
    if (handleMainTrackEndedWithIndexRef.current) {
      handleMainTrackEndedWithIndexRef.current(currentMusicIndex);
    }
  }, [currentMusicIndex]);

  // Store the function in the ref
  useEffect(() => {
    handleMainTrackEndedRef.current = handleMainTrackEnded;
  }, [handleMainTrackEnded]);

  // Add a function to explicitly check if we're at the end of the playlist with index
  const checkAndHandleLastTrackWithIndex = useCallback((index: number): boolean => {
    if (!hasMusicPlaylist(playlist) || !mainAudioRef.current) {
      return false;
    }

    const isLastTrack = index === (playlist.musics?.length ?? 0) - 1;

    if (isLastTrack) {
      const audio = mainAudioRef.current;

      // Check if we're close to the end of the track
      if (audio.duration > 0 &&
          audio.currentTime > 0 &&
          audio.duration - audio.currentTime < 1) {

        // Force play the first track in the playlist using the ref
        if (playMainTrackRef.current) {
          playMainTrackRef.current(0);
          return true;
        }
      }
    }

    return false;
  }, [playlist, mainAudioRef]);

  // Original function as a wrapper for the one with index
  const checkAndHandleLastTrack = useCallback((): boolean => {
    return checkAndHandleLastTrackWithIndex(currentMusicIndex);
  }, [currentMusicIndex, checkAndHandleLastTrackWithIndex]);

  // Store the function in the ref
  useEffect(() => {
    checkAndHandleLastTrackRef.current = checkAndHandleLastTrack;
  }, [checkAndHandleLastTrack]);

  // Handle main track playback
  const playMainTrack = useCallback((index: number): void => {
    if (!hasMusicPlaylist(playlist)) {
      return;
    }

    // Safety check - if index is out of bounds
    if (index >= playlist.musics.length) {
      index = 0;
    }
    if (index < 0) {
      index = playlist.musics.length - 1;
    }

    // Update current index - CRITICAL: This must happen BEFORE creating new audio to ensure correct state
    setCurrentMusicIndex(index);
    
    // Reset current time and duration when starting a new track
    setCurrentTime(0);
    setDuration(0);

    // Clean up existing audio
    if (mainAudioRef.current) {
      // Clear any existing loop check interval
      if (mainAudioRef.current.dataset.loopCheckInterval) {
        clearInterval(Number(mainAudioRef.current.dataset.loopCheckInterval));
      }

      mainAudioRef.current.pause();
      mainAudioRef.current.onended = null;
      mainAudioRef.current.ontimeupdate = null;

      // Remove any direct event listeners
      if (handleMainTrackEndedRef.current) {
        mainAudioRef.current.removeEventListener('ended', handleMainTrackEndedRef.current);
      }

      mainAudioRef.current.src = '';
      mainAudioRef.current = null;
    }

    // Create new audio element
    const track = playlist.musics[index];
    if (!track.src) {
      return;
    }

    const audio = new Audio(track.src);
    audio.volume = isMainMuted ? 0 : mainVolume / 100;

    // Store the current index with the audio element to ensure correct state
    audio.dataset.trackIndex = String(index);
    
    // Store the current playback mode with the audio element
    audio.dataset.playbackMode = playbackMode;

    // Critical fix for LOOP_ONE mode
    audio.loop = playbackMode === PlaybackMode.LOOP_ONE;

    // Add a direct event listener for the ended event - only if not in LOOP_ONE mode
    if (playbackMode !== PlaybackMode.LOOP_ONE) {
      audio.addEventListener('ended', () => {
        // Get current playback mode from the dataset, as it might have changed
        const currentMode = audio.dataset.playbackMode as PlaybackMode | undefined;
        
        // If mode has changed to LOOP_ONE, don't handle the ended event
        if (currentMode === PlaybackMode.LOOP_ONE) {
          return;
        }
        
        // Ensure we're using the current index, not the one from closure
        const currentIdx = Number(audio.dataset.trackIndex || index);
        if (handleMainTrackEndedWithIndexRef.current) {
          handleMainTrackEndedWithIndexRef.current(currentIdx);
        }
      });
    }

    // Add timeupdate listener to check if we're near the end of the track
    // (only needed for non-LOOP_ONE modes)
    audio.ontimeupdate = () => {
      // Get the current playback mode from the dataset
      const currentMode = audio.dataset.playbackMode as PlaybackMode | undefined;
      
      // Always update current time and duration for progress bar
      setCurrentTime(audio.currentTime);
      if (!isNaN(audio.duration) && audio.duration > 0) {
        setDuration(audio.duration);
      }
      
      // Skip track ending logic if we're in LOOP_ONE mode
      if (currentMode === PlaybackMode.LOOP_ONE) {
        return;
      }
      
      // Get the track index from the dataset
      const trackIndex = Number(audio.dataset.trackIndex || index);

      // Check if we're the last track in the playlist
      if (trackIndex === (playlist.musics?.length ?? 0) - 1) {
        // Periodically check if we need to handle track ending with our explicit function
        if (audio.duration - audio.currentTime < 3) {
          // Use the memoized function to ensure consistent behavior
          if (checkAndHandleLastTrackRef.current) {
            checkAndHandleLastTrackRef.current();
          }
        }
      }

      // If we're within 0.5 seconds of the end and the track is not paused
      if (audio.duration > 0 && audio.currentTime > 0 &&
          audio.duration - audio.currentTime < 0.5 && !audio.paused) {

        // Get the track index from the dataset
        const trackIndex = Number(audio.dataset.trackIndex || index);

        // Special handling for the last track
        if (trackIndex === (playlist.musics?.length ?? 0) - 1) {
          // Try our explicit track ending handler with the memoized function
          if (checkAndHandleLastTrackRef.current) {
            const handled = checkAndHandleLastTrackRef.current();
            if (handled) {
              // If the explicit handler took care of it, we're done
              return;
            }
          }
        }

        // Prevent multiple calls by removing the listener and creating a new one
        // that only handles time updates
        audio.ontimeupdate = () => {
          setCurrentTime(audio.currentTime);
          if (!isNaN(audio.duration) && audio.duration > 0) {
            setDuration(audio.duration);
          }
        };
        
        // Handle track ending with the current track index
        if (handleMainTrackEndedWithIndexRef.current) {
          handleMainTrackEndedWithIndexRef.current(trackIndex);
        }
      }
    };

    // Add loadedmetadata event to properly set duration and current time
    audio.onloadedmetadata = () => {
      // Set duration and current time when metadata is loaded
      if (!isNaN(audio.duration) && audio.duration > 0) {
        setDuration(audio.duration);
      }
      setCurrentTime(audio.currentTime);
    };

    // Add additional event listeners for better time tracking
    audio.ondurationchange = () => {
      if (!isNaN(audio.duration) && audio.duration > 0) {
        setDuration(audio.duration);
      }
    };

    audio.onplay = () => {
      // Update time info when playback starts
      setCurrentTime(audio.currentTime);
      if (!isNaN(audio.duration) && audio.duration > 0) {
        setDuration(audio.duration);
      }
    };

    mainAudioRef.current = audio;

    // Add a small delay before playing to avoid race conditions
    setTimeout(() => {
      // Check if the component is still mounted and the audio element still exists
      if (mainAudioRef.current === audio) {
        // Play the track
        audio.play()
          .then(() => {
            if (mainAudioRef.current === audio) {
              setIsMainPlaying(true);
            }
          })
          .catch(() => {
            if (mainAudioRef.current === audio) {
              setIsMainPlaying(false);
            }
          });
      }
    }, 50);

    // Add a function to force loop check
    const checkLoop = () => {
      // Get current playback mode from the dataset
      const currentMode = audio.dataset.playbackMode as PlaybackMode | undefined;
      
      // If we're at the end of the audio and at the last track
      if (audio.currentTime > 0 &&
          audio.duration > 0 &&
          audio.currentTime >= audio.duration - 0.1) {

        // Get the track index from the dataset
        const trackIndex = Number(audio.dataset.trackIndex || index);

        // Handle according to playback mode
        if (currentMode === PlaybackMode.LOOP_ONE) {
          // For loop one, replay the same track by resetting current time
          if (audio) {
            audio.currentTime = 0;
            audio.play().catch(() => {});
            return true;
          }
        } else if (currentMode === PlaybackMode.SHUFFLE) {
          // For shuffle, pick a random track
          if (playMainTrackRef.current && hasMusicPlaylist(playlist)) {
            const randomIndex = Math.floor(Math.random() * playlist.musics.length);
            playMainTrackRef.current(randomIndex);
            return true;
          }
        } else if (trackIndex === (playlist.musics?.length ?? 0) - 1) {
          // For loop all and we're at the last track, play the first track
          if (playMainTrackRef.current) {
            playMainTrackRef.current(0);
            return true;
          }
        }
      }
      return false;
    };

    // Additional safety check - force check loop every 500ms for this audio element
    const loopCheckInterval = setInterval(() => {
      if (!audio || audio !== mainAudioRef.current) {
        // Clean up if this isn't the current audio anymore
        clearInterval(loopCheckInterval);
        return;
      }
      
      // Get current playback mode from the dataset
      const currentMode = audio.dataset.playbackMode as PlaybackMode | undefined;
      
      // Update the loop property based on the current mode
      audio.loop = currentMode === PlaybackMode.LOOP_ONE;
      
      // Only run loop check for non-LOOP_ONE modes
      if (currentMode !== PlaybackMode.LOOP_ONE) {
        checkLoop();
      }
    }, 500);

    // Store the interval ID on the audio element for cleanup
    audio.dataset.loopCheckInterval = String(loopCheckInterval);
  }, [
    playlist,
    mainAudioRef,
    isMainMuted,
    mainVolume,
    setCurrentMusicIndex,
    setIsMainPlaying,
    playbackMode,
  ]);

  // Store the playMainTrack function in the ref
  useEffect(() => {
    playMainTrackRef.current = playMainTrack;
  }, [playMainTrack]);

  // Add a useEffect to periodically check if we're at the end of the last track
  useEffect(() => {
    if (!isMainPlaying || !mainAudioRef.current || !hasMusicPlaylist(playlist)) {
      return;
    }

    // We don't need to set up any interval for LOOP_ONE mode
    // as the native audio.loop property will handle it
    if (playbackMode === PlaybackMode.LOOP_ONE) {
      return;
    }

    let shouldSetupInterval = false;
    
    // Determine if we need to set up an interval based on playback mode and track position
    if (playbackMode === PlaybackMode.LOOP_ALL) {
      // For loop all, only set up if we're on the last track
      shouldSetupInterval = currentMusicIndex === playlist.musics.length - 1;
    } else if (playbackMode === PlaybackMode.SHUFFLE) {
      // For shuffle, always set up the interval
      shouldSetupInterval = true;
    }
    
    if (!shouldSetupInterval) {
      return;
    }

    // Check every second if we're near the end of the track
    const intervalId = setInterval(() => {
      const audio = mainAudioRef.current;
      if (!audio) return;

      // Safety check - if the playback mode has changed to LOOP_ONE, clear the interval
      if (audio.dataset.playbackMode === PlaybackMode.LOOP_ONE) {
        clearInterval(intervalId);
        return;
      }

      // If we're within 0.5 seconds of the end
      if (audio.duration > 0 && audio.currentTime > 0 && audio.duration - audio.currentTime < 0.5) {
        clearInterval(intervalId); // Clear this interval

        // Handle according to playback mode
        if (playbackMode === PlaybackMode.SHUFFLE) {
          // For shuffle, pick a random track
          if (playMainTrackRef.current && hasMusicPlaylist(playlist)) {
            const randomIndex = Math.floor(Math.random() * playlist.musics.length);
            playMainTrackRef.current(randomIndex);
          }
        } else {
          // For loop all, play the first track if we're at the last
          if (playMainTrackRef.current) {
            playMainTrackRef.current(0);
          }
        }
      }
    }, 1000);

    // Clean up interval on unmount or when changing tracks
    return () => {
      clearInterval(intervalId);
    };
  }, [currentMusicIndex, isMainPlaying, playlist, playbackMode]);

  // Toggle play/pause for main music
  const toggleMainPlay = useCallback(() => {
    if (!mainAudioRef.current && hasMusicPlaylist(playlist)) {
      if (playMainTrackRef.current) {
        playMainTrackRef.current(currentMusicIndex);
      }
      return;
    }

    if (isMainPlaying && mainAudioRef.current) {
      mainAudioRef.current.pause();
      setIsMainPlaying(false);
    } else if (mainAudioRef.current) {
      mainAudioRef.current.play()
        .then(() => {
          setIsMainPlaying(true);
        })
        .catch(() => {});
    }
  }, [currentMusicIndex, isMainPlaying, playlist]);

  // Skip to next track
  const skipToNext = useCallback(() => {
    if (!hasMusicPlaylist(playlist)) {
      return;
    }

    let nextIndex: number;

    switch (playbackMode) {
      case PlaybackMode.LOOP_ONE:
        // For manual skips in LOOP_ONE mode, we should go to the next track
        // but keep the LOOP_ONE mode active for that track
        nextIndex = (currentMusicIndex + 1) % playlist.musics.length;
        break;
      case PlaybackMode.SHUFFLE:
        // For shuffle, pick a random track (avoiding current if possible)
        if (playlist.musics.length > 1) {
          let randomIndex;
          do {
            randomIndex = Math.floor(Math.random() * playlist.musics.length);
          } while (randomIndex === currentMusicIndex && playlist.musics.length > 1);
          nextIndex = randomIndex;
        } else {
          nextIndex = 0;
        }
        break;
      case PlaybackMode.LOOP_ALL:
      default:
        // For loop all, go to the next track or back to first if at end
        nextIndex = (currentMusicIndex + 1) % playlist.musics.length;
        break;
    }

    if (playMainTrackRef.current) {
      playMainTrackRef.current(nextIndex);
    }
  }, [currentMusicIndex, playlist, playbackMode]);

  // Skip to previous track
  const skipToPrevious = useCallback(() => {
    if (!hasMusicPlaylist(playlist)) return;

    let prevIndex: number;

    switch (playbackMode) {
      case PlaybackMode.LOOP_ONE:
        // For manual skips in LOOP_ONE mode, we should go to the previous track
        // but keep the LOOP_ONE mode active for that track
        prevIndex = currentMusicIndex === 0 ? playlist.musics.length - 1 : currentMusicIndex - 1;
        break;
      case PlaybackMode.SHUFFLE:
        // For shuffle, pick a random track (avoiding current if possible)
        if (playlist.musics.length > 1) {
          let randomIndex;
          do {
            randomIndex = Math.floor(Math.random() * playlist.musics.length);
          } while (randomIndex === currentMusicIndex && playlist.musics.length > 1);
          prevIndex = randomIndex;
        } else {
          prevIndex = 0;
        }
        break;
      case PlaybackMode.LOOP_ALL:
      default:
        // For loop all, go to the previous track or to the last if at beginning
        prevIndex = currentMusicIndex === 0 ? playlist.musics.length - 1 : currentMusicIndex - 1;
        break;
    }

    if (playMainTrackRef.current) {
      playMainTrackRef.current(prevIndex);
    }
  }, [currentMusicIndex, playlist, playbackMode]);

  // Toggle mute for main track
  const toggleMainMute = useCallback(() => {
    const newMuted = !isMainMuted;
    setIsMainMuted(newMuted);

    if (mainAudioRef.current) {
      mainAudioRef.current.volume = newMuted ? 0 : mainVolume / 100;
    }
  }, [isMainMuted, mainVolume]);

  // Handle main volume changes
  const handleMainVolumeChange = useCallback((value: number[]) => {
    const newVolume = value[0];
    setMainVolume(newVolume);

    if (mainAudioRef.current && !isMainMuted) {
      mainAudioRef.current.volume = newVolume / 100;
    }
  }, [isMainMuted]);

  // Toggle playback mode
  const togglePlaybackMode = useCallback(() => {
    setPlaybackMode(prevMode => {
      let newMode: PlaybackMode;
      
      switch(prevMode) {
        case PlaybackMode.LOOP_ALL:
          newMode = PlaybackMode.LOOP_ONE;
          break;
        case PlaybackMode.LOOP_ONE:
          newMode = PlaybackMode.SHUFFLE;
          break;
        case PlaybackMode.SHUFFLE:
        default:
          newMode = PlaybackMode.LOOP_ALL;
      }

      // Immediately apply the loop property based on the new mode
      if (mainAudioRef.current) {
        // Set loop property
        mainAudioRef.current.loop = newMode === PlaybackMode.LOOP_ONE;
        
        // Store the current playback mode on the audio element
        mainAudioRef.current.dataset.playbackMode = newMode;
      }
      
      return newMode;
    });
  }, [mainAudioRef]);

  // Update current time and duration whenever the audio is playing
  // Note: Time tracking is now handled directly in playMainTrack function
  // This useEffect is kept only as a backup for edge cases
  useEffect(() => {
    if (!mainAudioRef.current) return;

    const audio = mainAudioRef.current;
    
    // Only add backup listeners if they don't already exist
    if (!audio.dataset.hasTimeListeners) {
      const updateTimeInfo = () => {
        setCurrentTime(audio.currentTime);
        if (!isNaN(audio.duration) && audio.duration > 0) {
          setDuration(audio.duration);
        }
      };

      // Mark that this audio element has time listeners
      audio.dataset.hasTimeListeners = 'true';
      
      // Add backup event listeners
      audio.addEventListener('loadedmetadata', updateTimeInfo);
      audio.addEventListener('durationchange', updateTimeInfo);
      audio.addEventListener('play', updateTimeInfo);
      
      return () => {
        audio.removeEventListener('loadedmetadata', updateTimeInfo);
        audio.removeEventListener('durationchange', updateTimeInfo);
        audio.removeEventListener('play', updateTimeInfo);
      };
    }
  }, [isMainPlaying, currentMusicIndex, mainAudioRef]);

  // Handle seek functionality
  const handleSeek = useCallback((time: number) => {
    if (!mainAudioRef.current) return;
    
    mainAudioRef.current.currentTime = time;
    setCurrentTime(time);
  }, []);

  return {
    currentMusicIndex,
    setCurrentMusicIndex,
    isMainPlaying,
    setIsMainPlaying,
    mainVolume,
    setMainVolume,
    isMainMuted,
    setIsMainMuted,
    playbackMode,
    setPlaybackMode,
    togglePlaybackMode,
    mainAudioRef,
    isFirstRenderRef,
    playMainTrack,
    toggleMainPlay,
    skipToNext,
    skipToPrevious,
    toggleMainMute,
    handleMainVolumeChange,
    currentTime,
    duration,
    handleSeek,
  };
} 