'use client';

import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Film } from 'lucide-react';

interface FallbackThumbnailProps {
  title: string;
}

export function FallbackThumbnail({ title }: FallbackThumbnailProps) {
  // Modern bright color palette
  const colorPalette = [
    '#FF5757', // bright red
    '#FF914D', // bright orange
    '#FFDE59', // bright yellow
    '#A7FF83', // bright green
    '#38B6FF', // bright blue
    '#8C52FF', // bright purple
    '#FF66C4', // bright pink
    '#00D1C1', // bright teal
  ];
  
  // Generate a color from the palette based on the title
  const getColorFromTitle = (title: string): string => {
    let hash = 0;
    for (let i = 0; i < title.length; i++) {
      hash = title.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    const index = Math.abs(hash) % colorPalette.length;
    return colorPalette[index];
  };

  const backgroundColor = getColorFromTitle(title);
  
  // Generate a gradient with the background color
  const gradient = `linear-gradient(135deg, ${backgroundColor}, ${backgroundColor}dd, ${backgroundColor}aa)`;

  // Get first letter of each word to create an abbreviation
  const getAbbreviation = (text: string): string => {
    return text
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 3);
  };

  return (
    <AspectRatio ratio={16 / 9} className="overflow-hidden rounded-t-lg">
      <div 
        className="w-full h-full flex flex-col items-center justify-center p-4 text-center relative"
        style={{ background: gradient }}
      >
        {/* Abstract background elements */}
        <div className="absolute top-0 left-0 w-32 h-32 rounded-full opacity-20 bg-white transform -translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute bottom-0 right-0 w-40 h-40 rounded-full opacity-20 bg-white transform translate-x-1/3 translate-y-1/3"></div>
        
        {/* Title abbreviation in a large display */}
        <div className="mb-3 flex items-center gap-2 relative z-10">
          <span className="text-white text-4xl font-bold tracking-tight drop-shadow-md">
            {getAbbreviation(title)}
          </span>
          <Film className="h-6 w-6 text-white/80" />
        </div>
        
        {/* Title with text shadow for better readability */}
        <p className="font-medium text-sm text-white/90 relative z-10 drop-shadow-md line-clamp-2 max-w-[80%]">
          {title}
        </p>
      </div>
    </AspectRatio>
  );
} 