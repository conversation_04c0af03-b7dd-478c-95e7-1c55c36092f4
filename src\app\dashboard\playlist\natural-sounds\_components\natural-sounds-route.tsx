"use client"

import { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Search,
  Plus,
  Filter,
  X,
  Play,
  Pause,
  Waves,
  Clock,
  Heart,
  Headphones
} from "lucide-react"
import { useGetNatureSounds } from "@schemas/Natural/nature-sound-query"
import { useAddNatureSoundsToMusicPlaylistUser } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { useAudioStore } from "@/lib/audio-store"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { AddToNaturePlaylistDialog } from "./add-to-nature-playlist-dialog"

interface NatureSound {
  id: string
  title: string
  src: string | null
  category: string[]
  isPublic: boolean
}

// Map category enum values to display names
const categoryDisplayNames: Record<string, string> = {
  RAIN: "Rain",
  FOREST: "Forest",
  OCEAN: "Ocean",
  THUNDER: "Thunder",
  WIND: "Wind",
  FIRE: "Fire",
  BIRDS: "Birds",
  WATERFALL: "Waterfall",
  STREAM: "Stream",
  WAVES: "Waves",
  NIGHT: "Night",
  INSECTS: "Insects",
  RIVER: "River",
  STORM: "Storm",
  LAKE: "Lake",
  WHITE_NOISE: "White Noise",
  AMBIENT: "Ambient",
  CITY: "City",
  PEOPLE: "People"
}

export function NaturalSoundsRoute() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedSoundId, setSelectedSoundId] = useState<string | null>(null)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)

  const { data: natureSounds, isLoading } = useGetNatureSounds({ isPublic: true })
  const addNatureSoundsToPlaylist = useAddNatureSoundsToMusicPlaylistUser()

  // Global audio player state
  const {
    globalPlayer,
    setGlobalPlayerTrack,
    setGlobalPlayerPlaying
  } = useAudioStore()

  // Get unique categories for filtering
  const availableCategories = useMemo(() => {
    if (!natureSounds) return []
    const categorySet = new Set<string>()
    natureSounds.forEach(sound => {
      sound.category?.forEach(cat => categorySet.add(cat))
    })
    return Array.from(categorySet).sort()
  }, [natureSounds])

  // Filter nature sounds based on search and category filters
  const filteredSounds = useMemo(() => {
    if (!natureSounds) return []

    return natureSounds.filter(sound => {
      const matchesSearch = sound.title.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesCategory = selectedCategories.length === 0 ||
        sound.category?.some(cat => selectedCategories.includes(cat))

      return matchesSearch && matchesCategory
    })
  }, [natureSounds, searchQuery, selectedCategories])

  // Enhanced stats with more insights
  const stats = useMemo(() => {
    if (!natureSounds) return { total: 0, categories: 0, totalDuration: 0, avgDuration: 0 }

    const total = natureSounds.length
    const uniqueCategories = new Set<string>()

    natureSounds.forEach(sound => {
      sound.category?.forEach(cat => uniqueCategories.add(cat))
    })

    return {
      total,
      categories: uniqueCategories.size,
      totalDuration: Math.floor(total * 4.2), // Estimated duration in minutes (nature sounds tend to be longer)
      avgDuration: total > 0 ? Math.floor((total * 4.2) / total) : 0
    }
  }, [natureSounds])

  const handleCategoryToggle = (category: string) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
  }

  const clearFilters = () => {
    setSearchQuery("")
    setSelectedCategories([])
  }

  const handleAddToPlaylist = (soundId: string) => {
    setSelectedSoundId(soundId)
    setIsAddDialogOpen(true)
  }

  const handleAddSoundToPlaylist = async (playlistId: string) => {
    if (!selectedSoundId) return

    await addNatureSoundsToPlaylist.mutateAsync({
      musicPlaylistUserId: playlistId,
      natureSoundIds: [selectedSoundId]
    })
  }

  const handlePlaySound = (sound: NatureSound) => {
    const track = {
      id: sound.id,
      title: sound.title,
      src: sound.src || `https://www.soundjay.com/misc/sounds/bell-ringing-05.wav`,
      type: 'nature-sound' as const,
      category: sound.category
    }

    if (globalPlayer.currentTrack?.id === sound.id) {
      // If already playing this track, toggle play/pause
      setGlobalPlayerPlaying(!globalPlayer.isPlaying)
    } else {
      // Play new track - set track first, then ensure it starts playing
      setGlobalPlayerTrack(track)
      setGlobalPlayerPlaying(true)
    }
  }

  const selectedSound = selectedSoundId ? natureSounds?.find(s => s.id === selectedSoundId) : null

  // Compact Loading State
  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Compact Header skeleton */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Skeleton className="h-10 w-10 rounded-xl" />
              <div className="space-y-2">
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-64" />
              </div>
            </div>
          </div>

          {/* Compact stats skeleton */}
          <div className="flex gap-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 rounded" />
                <div className="space-y-1">
                  <Skeleton className="h-5 w-8" />
                  <Skeleton className="h-3 w-12" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Search and filters skeleton */}
        <div className="space-y-4">
          <Skeleton className="h-10 w-full" />
          <div className="flex flex-wrap gap-2">
            {Array.from({ length: 6 }).map((_, i) => (
              <Skeleton key={i} className="h-6 w-20" />
            ))}
          </div>
        </div>

        {/* List skeleton */}
        <div className="space-y-3">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="flex items-center gap-4 p-4 rounded-lg border border-border/50 bg-muted/10">
              <Skeleton className="h-12 w-12 rounded-lg" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <div className="flex gap-2">
                  <Skeleton className="h-3 w-16" />
                  <Skeleton className="h-3 w-20" />
                </div>
              </div>
              <Skeleton className="h-8 w-24" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 pb-32">
        {/* Compact Header with Inline Stats */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-xl bg-gradient-to-br from-orange-500/10 to-rose-500/10 border border-orange-200/20 dark:border-orange-700/20">
                <Waves className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 bg-clip-text text-transparent">
                  Natural Sounds
                </h1>
                <p className="text-sm text-muted-foreground">
                  Discover and add natural sounds to create the perfect ambient environment
                </p>
              </div>
            </div>
          </div>

          {/* Compact Inline Stats - Only show when sounds exist */}
          {natureSounds && natureSounds.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="flex items-center gap-6 py-2 px-4 rounded-lg bg-muted/30 border border-border/50"
            >
              <div className="flex items-center gap-2">
                <div className="p-1.5 rounded-md bg-orange-500/10">
                  <Waves className="h-3.5 w-3.5 text-orange-600 dark:text-orange-400" />
                </div>
                <div className="text-sm">
                  <span className="font-semibold text-orange-700 dark:text-orange-300">{stats.total}</span>
                  <span className="text-muted-foreground ml-1">sounds</span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <div className="p-1.5 rounded-md bg-rose-500/10">
                  <Filter className="h-3.5 w-3.5 text-rose-600 dark:text-rose-400" />
                </div>
                <div className="text-sm">
                  <span className="font-semibold text-rose-700 dark:text-rose-300">{stats.categories}</span>
                  <span className="text-muted-foreground ml-1">categories</span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <div className="p-1.5 rounded-md bg-red-500/10">
                  <Clock className="h-3.5 w-3.5 text-red-600 dark:text-red-400" />
                </div>
                <div className="text-sm">
                  <span className="font-semibold text-red-700 dark:text-red-300">{(stats.totalDuration / 60).toFixed(1)}h</span>
                  <span className="text-muted-foreground ml-1">duration</span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <div className="p-1.5 rounded-md bg-emerald-500/10">
                  <Headphones className="h-3.5 w-3.5 text-emerald-600 dark:text-emerald-400" />
                </div>
                <div className="text-sm">
                  <span className="font-semibold text-emerald-700 dark:text-emerald-300">{stats.avgDuration}</span>
                  <span className="text-muted-foreground ml-1">avg min</span>
                </div>
              </div>
            </motion.div>
          )}
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search natural sounds..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {(searchQuery || selectedCategories.length > 0) && (
            <Button
              variant="outline"
              onClick={clearFilters}
              className="gap-2 shrink-0 hover:bg-orange-50 hover:text-orange-700 hover:border-orange-200 dark:hover:bg-orange-950/30 dark:hover:text-orange-300 dark:hover:border-orange-800"
            >
              <X className="h-4 w-4" />
              Clear Filters
            </Button>
          )}
        </div>

        {/* Category Filters */}
        {availableCategories.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Categories</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {availableCategories.map(category => (
                <Badge
                  key={category}
                  variant={selectedCategories.includes(category) ? "default" : "outline"}
                  className={cn(
                    "cursor-pointer transition-all duration-200 hover:scale-105",
                    selectedCategories.includes(category)
                      ? "bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 text-white border-transparent hover:from-orange-600 hover:via-red-600 hover:to-rose-700"
                      : "hover:bg-orange-50 hover:text-orange-700 hover:border-orange-200 dark:hover:bg-orange-950/30 dark:hover:text-orange-300 dark:hover:border-orange-800"
                  )}
                  onClick={() => handleCategoryToggle(category)}
                >
                  {categoryDisplayNames[category] || category}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Enhanced Natural Sounds Display */}
        <AnimatePresence mode="wait">
        {filteredSounds.length === 0 ? (
          <motion.div
            key="empty"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            className="flex flex-col items-center justify-center py-20 text-center"
          >
            {/* Enhanced Icon with Animation */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.1, type: "spring", stiffness: 200, damping: 15 }}
              className="relative mb-8"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-orange-400/20 to-rose-400/20 rounded-full blur-xl scale-150" />
              <div className="relative p-6 rounded-2xl bg-gradient-to-br from-orange-50 via-red-50 to-rose-50 dark:from-orange-950/50 dark:via-red-950/30 dark:to-rose-950/50 border border-orange-200/50 dark:border-orange-800/50 shadow-lg">
                <motion.div
                  animate={{
                    rotate: [0, -10, 10, -5, 5, 0],
                    scale: [1, 1.05, 1]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatDelay: 3,
                    ease: "easeInOut"
                  }}
                >
                  <Waves className="h-16 w-16 text-orange-500 dark:text-orange-400" />
                </motion.div>
              </div>
            </motion.div>

            {/* Enhanced Content */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="space-y-6 max-w-lg"
            >
              <div className="space-y-3">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-orange-600 via-red-600 to-rose-600 bg-clip-text text-transparent">
                  {searchQuery || selectedCategories.length > 0
                    ? "No sounds match your search"
                    : "Discover nature's symphony"
                  }
                </h3>
                <p className="text-base text-muted-foreground leading-relaxed px-4">
                  {searchQuery || selectedCategories.length > 0
                    ? "Try adjusting your search terms or filters to find the perfect ambient sounds for your focus sessions."
                    : "Explore our collection of natural sounds to create the perfect ambient environment for productivity and relaxation."
                  }
                </p>
              </div>

              {/* Enhanced Features List */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5, duration: 0.5 }}
                className="grid grid-cols-1 sm:grid-cols-3 gap-4 py-4"
              >
                <div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-muted/30">
                  <div className="p-2 rounded-full bg-orange-500/10">
                    <Waves className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground">Nature Sounds</span>
                </div>
                <div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-muted/30">
                  <div className="p-2 rounded-full bg-rose-500/10">
                    <Headphones className="h-4 w-4 text-rose-600 dark:text-rose-400" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground">Ambient Quality</span>
                </div>
                <div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-muted/30">
                  <div className="p-2 rounded-full bg-red-500/10">
                    <Heart className="h-4 w-4 text-red-600 dark:text-red-400" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground">Relaxing Vibes</span>
                </div>
              </motion.div>

              {/* Clear Filters Button */}
              {(searchQuery || selectedCategories.length > 0) && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.7, duration: 0.3 }}
                  className="flex justify-center pt-2"
                >
                  <Button
                    onClick={clearFilters}
                    size="lg"
                    className="bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 hover:from-orange-600 hover:via-red-600 hover:to-rose-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 group px-8"
                  >
                    <X className="mr-2 h-5 w-5 group-hover:rotate-90 transition-transform duration-200" />
                    Clear All Filters
                  </Button>
                </motion.div>
              )}
            </motion.div>
          </motion.div>
        ) : (
          <motion.div
            key="sounds"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="space-y-3"
          >
            {filteredSounds.map((sound, index) => {
              const isCurrentlyPlaying = globalPlayer.currentTrack?.id === sound.id && globalPlayer.isPlaying
              const isCurrentTrack = globalPlayer.currentTrack?.id === sound.id

              return (
                <motion.div
                  key={sound.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    delay: index * 0.03,
                    duration: 0.2,
                    ease: "easeOut"
                  }}
                  layout
                  className={cn(
                    "group flex items-center gap-4 p-4 rounded-lg border transition-all duration-200",
                    "border-border/30 hover:border-border hover:bg-muted/20 hover:shadow-md",
                    isCurrentTrack && "bg-orange-50/50 dark:bg-orange-950/20 border-orange-200 dark:border-orange-800 ring-1 ring-orange-200/50 dark:ring-orange-800/50"
                  )}
                >
                  {/* Sound Icon / Play Button */}
                  <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/30 dark:to-rose-900/30 flex items-center justify-center shrink-0 relative group-hover:from-orange-200 group-hover:to-rose-200 dark:group-hover:from-orange-800/40 dark:group-hover:to-rose-800/40 transition-colors duration-200">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation()
                        handlePlaySound(sound)
                      }}
                      className="w-12 h-12 rounded-lg hover:bg-orange-200 dark:hover:bg-orange-800 absolute inset-0 transition-colors duration-150"
                    >
                      {isCurrentlyPlaying ? (
                        <Pause className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                      ) : (
                        <Play className="h-5 w-5 text-orange-600 dark:text-orange-400 ml-0.5" />
                      )}
                    </Button>
                  </div>

                  {/* Sound Info */}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium line-clamp-1 mb-1.5 text-sm leading-tight">{sound.title}</h3>
                    <div className="flex items-center gap-2 flex-wrap">
                      {sound.category?.slice(0, 3).map(category => (
                        <Badge key={category} variant="secondary" className="text-xs px-1.5 py-0.5">
                          {categoryDisplayNames[category] || category}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Add Button */}
                  <Button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleAddToPlaylist(sound.id)
                    }}
                    size="sm"
                    className={cn(
                      "h-8 px-3 text-xs transition-all duration-200 gap-2 shadow-lg",
                      "bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 hover:from-orange-600 hover:via-red-600 hover:to-rose-700 text-white",
                      "hover:shadow-xl hover:scale-105"
                    )}
                  >
                    <Plus className="h-3 w-3" />
                    Add to Playlist
                  </Button>
                </motion.div>
              )
            })}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Add to Nature Playlist Dialog */}
      <AddToNaturePlaylistDialog
        isOpen={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        soundId={selectedSoundId || ""}
        soundTitle={selectedSound?.title || ""}
        onAddToPlaylist={handleAddSoundToPlaylist}
      />
    </div>
  )
}
