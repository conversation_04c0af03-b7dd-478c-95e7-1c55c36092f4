import { z } from 'zod';
import { JsonValueSchema } from '../inputTypeSchemas/JsonValueSchema'
import { SubscriptionStatusSchema } from '../inputTypeSchemas/SubscriptionStatusSchema'
import { SubscriptionTypeSchema } from '../inputTypeSchemas/SubscriptionTypeSchema'
import { SubscriptionIntervalSchema } from '../inputTypeSchemas/SubscriptionIntervalSchema'
import type { JsonValueType } from '../inputTypeSchemas/JsonValueSchema';
import { UserWithRelationsSchema, UserPartialWithRelationsSchema } from './UserSchema'
import type { UserWithRelations, UserPartialWithRelations } from './UserSchema'
import { PaymentWithRelationsSchema, PaymentPartialWithRelationsSchema } from './PaymentSchema'
import type { PaymentWithRelations, PaymentPartialWithRelations } from './PaymentSchema'

/////////////////////////////////////////
// SUBSCRIPTION SCHEMA
/////////////////////////////////////////

export const SubscriptionSchema = z.object({
  status: SubscriptionStatusSchema,
  subscriptionType: SubscriptionTypeSchema,
  interval: SubscriptionIntervalSchema.nullish(),
  id: z.string().cuid(),
  userId: z.string(),
  planId: z.string(),
  currentPeriodStart: z.coerce.date(),
  currentPeriodEnd: z.coerce.date(),
  cancelAtPeriodEnd: z.boolean(),
  canceledAt: z.coerce.date().nullish(),
  externalId: z.string().nullish(),
  checkoutId: z.string().nullish(),
  productId: z.string().nullish(),
  priceId: z.string().nullish(),
  price: z.number().nullish(),
  currency: z.string(),
  lastPaymentDate: z.coerce.date().nullish(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  polarMetadata: JsonValueSchema.nullable(),
})

export type Subscription = z.infer<typeof SubscriptionSchema>

/////////////////////////////////////////
// SUBSCRIPTION PARTIAL SCHEMA
/////////////////////////////////////////

export const SubscriptionPartialSchema = SubscriptionSchema.partial()

export type SubscriptionPartial = z.infer<typeof SubscriptionPartialSchema>

/////////////////////////////////////////
// SUBSCRIPTION RELATION SCHEMA
/////////////////////////////////////////

export type SubscriptionRelations = {
  user: UserWithRelations;
  payments: PaymentWithRelations[];
};

export type SubscriptionWithRelations = Omit<z.infer<typeof SubscriptionSchema>, "polarMetadata"> & {
  polarMetadata?: JsonValueType | null;
} & SubscriptionRelations

export const SubscriptionWithRelationsSchema: z.ZodType<SubscriptionWithRelations> = SubscriptionSchema.merge(z.object({
  user: z.lazy(() => UserWithRelationsSchema),
  payments: z.lazy(() => PaymentWithRelationsSchema).array(),
}))

/////////////////////////////////////////
// SUBSCRIPTION PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type SubscriptionPartialRelations = {
  user?: UserPartialWithRelations;
  payments?: PaymentPartialWithRelations[];
};

export type SubscriptionPartialWithRelations = Omit<z.infer<typeof SubscriptionPartialSchema>, "polarMetadata"> & {
  polarMetadata?: JsonValueType | null;
} & SubscriptionPartialRelations

export const SubscriptionPartialWithRelationsSchema: z.ZodType<SubscriptionPartialWithRelations> = SubscriptionPartialSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  payments: z.lazy(() => PaymentPartialWithRelationsSchema).array(),
})).partial()

export type SubscriptionWithPartialRelations = Omit<z.infer<typeof SubscriptionSchema>, "polarMetadata"> & {
  polarMetadata?: JsonValueType | null;
} & SubscriptionPartialRelations

export const SubscriptionWithPartialRelationsSchema: z.ZodType<SubscriptionWithPartialRelations> = SubscriptionSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  payments: z.lazy(() => PaymentPartialWithRelationsSchema).array(),
}).partial())

export default SubscriptionSchema;
