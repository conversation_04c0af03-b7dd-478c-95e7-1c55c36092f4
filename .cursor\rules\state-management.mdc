---
description: 
globs: 
alwaysApply: false
---
# State Management

## Zustand Store
The application uses Zustand for state management. The main store is defined in [src/lib/store.ts](mdc:src/lib/store.ts).

## Key State Elements
- Timer settings (pomodoro, short break, long break durations)
- Current timer phase
- Video selection and playback
- Timer position and fullscreen state
- Session tracking

## State Structure
The store provides:
- **State Properties**: Video selection, timer settings, phase, time remaining
- **Actions**: Start/pause timer, reset, skip breaks, move to next phase
- **Timer Logic**: Phase transitions, session counting

## Hooks Integration
Custom hooks in [src/hooks](mdc:src/hooks) provide React components with access to store functionality with additional UI-specific logic.
