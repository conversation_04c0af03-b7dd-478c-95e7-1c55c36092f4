import { cn } from "@/lib/utils";
import { Maximize2 } from "lucide-react";

interface ResizeHandleProps {
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  onMouseDown: (e: React.MouseEvent) => void;
  onTouchStart: (e: React.TouchEvent) => void;
  isHovered: boolean;
  isResizing: boolean;
  showControls: boolean;
}

export function ResizeHandle({
  onMouseEnter,
  onMouseLeave,
  onMouseDown,
  onTouchStart,
  isHovered,
  isResizing,
  showControls,
}: ResizeHandleProps) {
  // Get styles based on state
  const getStyles = () => {
    // Base styles
    const baseStyle = {
      opacity: isHovered || showControls ? 0.95 : 0.7,
      transform: isHovered ? 'scale(1.05)' : 'scale(1)',
    };
    
    // Add transition unless actively resizing
    if (!isResizing) {
      return {
        ...baseStyle,
        transition: 'all 0.3s cubic-bezier(0.16, 1, 0.3, 1)',
      };
    }
    
    return baseStyle;
  };

  return (
    <div 
      className={cn(
        "absolute bottom-0.5 right-0.5 w-5 h-5 z-30 flex items-center justify-center",
        "cursor-nwse-resize rounded-md",
        "hover:bg-white/10 active:bg-white/15",
        "overflow-hidden",
        isHovered 
          ? "bg-white/5 backdrop-blur-sm shadow-sm border border-white/10" 
          : "bg-transparent border-none"
      )}
      style={getStyles()}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onMouseDown={onMouseDown}
      onTouchStart={onTouchStart}
      title="Resize timer (maintains proportion)"
      aria-label="Resize timer"
      role="button"
      tabIndex={0}
      data-resize-handle="bottom-right"
    >
      <Maximize2 
        className={cn(
          "h-3 w-3 transform transition-transform duration-300 ease-out",
          isHovered ? "scale-110 text-white" : "text-white/70"
        )}
        strokeWidth={1.5} 
      />
    </div>
  );
} 