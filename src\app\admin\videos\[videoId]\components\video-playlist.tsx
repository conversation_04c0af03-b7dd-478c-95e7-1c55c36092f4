"use client";

import { useAudioStore } from "@/lib/audio-store";
import { Music, PlayCircle, PauseCircle } from "lucide-react";
import { useState } from "react";
import { cn } from "@/lib/utils";

import { GetVideo_ResponseTypeSuccess } from "@schemas/Video/video-query";
import { MusicGenre, NatureSoundCategory, MediaSource, UserRole } from "@prisma/client";

// Define types for Music and NatureSound based on the actual database schema
interface MusicTrack {
  id: string;
  title: string;
  src: string | null;
  source: MediaSource | null;
  rating: number | null;
  isPublic: boolean;
  isCopyright: boolean;
  creatorType: UserRole;
  userId: string;
  genres: MusicGenre[];
  createdAt: string;
  updatedAt: string;
}

interface NatureSoundTrack {
  id: string;
  title: string;
  src: string | null;
  source: MediaSource | null;
  isPublic: boolean;
  creatorType: UserRole;
  userId: string;
  category: NatureSoundCategory[];
  createdAt: string;
  updatedAt: string;
}

// Type guard to check if a track is a music track
function isMusicTrack(track: MusicTrack | NatureSoundTrack): track is MusicTrack {
  return 'genres' in track;
}

interface VideoPlaylistProps {
  video: GetVideo_ResponseTypeSuccess;
}

export function VideoPlaylist({ video }: VideoPlaylistProps) {
  const [currentTrackId, setCurrentTrackId] = useState<string | null>(null);
  const { setSelectedAudiosByMusicTrackId } = useAudioStore();

  const handlePlayTrack = (trackId: string) => {
    if (currentTrackId === trackId) {
      // If the same track, toggle it off
      setCurrentTrackId(null);
    } else {
      // Otherwise, play the new track
      setCurrentTrackId(trackId);
      setSelectedAudiosByMusicTrackId(trackId);
    }
  };

  // Combine musics and nature sounds into a single array
  const allTracks = [
    ...(video.musicPlaylist?.musics || []),
    ...(video.naturePlaylist?.natureSounds || [])
  ] as (MusicTrack | NatureSoundTrack)[];

  if ((!video.musicPlaylist && !video.naturePlaylist) || allTracks.length === 0) {
    return (
      <div className="text-center p-4 text-muted-foreground">
        No tracks available in this playlist
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {allTracks.map((track) => {
        // Determine if track is a music track (has genres) or nature sound
        const isMusic = isMusicTrack(track);

        return (
          <div
            key={track.id}
            className={cn(
              "flex gap-3 group hover:bg-accent p-2 rounded-md transition-colors cursor-pointer",
              currentTrackId === track.id && "bg-accent/80"
            )}
            onClick={() => handlePlayTrack(track.id)}
          >
            <div className="relative h-14 w-14 rounded-md overflow-hidden flex-shrink-0 bg-muted flex items-center justify-center">
              {track.id === currentTrackId ? (
                <div className="absolute inset-0 bg-primary/10 flex items-center justify-center">
                  <PauseCircle className="h-6 w-6 text-primary" />
                </div>
              ) : (
                <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity">
                  <PlayCircle className="h-6 w-6 text-white" />
                </div>
              )}
              <Music className="h-6 w-6 text-muted-foreground/70" />
            </div>

            <div className="flex flex-col justify-center overflow-hidden">
              <h3 className={cn(
                "font-medium text-sm line-clamp-1 group-hover:text-primary transition-colors",
                currentTrackId === track.id && "text-primary"
              )}>
                {track.title}
              </h3>
              {isMusic ? (
                <p className="text-xs text-muted-foreground mt-1">
                  {track.genres.length > 0
                    ? track.genres.join(', ')
                    : 'No genre'}
                  {track.rating && ` • ${track.rating}/5 ⭐`}
                </p>
              ) : (
                <p className="text-xs text-muted-foreground mt-1">
                  Nature Sound {track.category.length > 0 && `• ${track.category.join(', ')}`}
                </p>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}