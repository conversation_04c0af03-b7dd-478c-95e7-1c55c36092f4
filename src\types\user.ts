export interface User {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string | null;
  createdAt: string;
  updatedAt: string;
  role: "ADMIN" | "USER";
  banned: boolean;
  banReason?: string | null;
  banExpires?: string | null;
  premium: boolean;
  subscriptionType?: "FREE" | "PREMIUM" | "PREMIUM_PLUS";
}

export interface Session {
  id: string;
  token: string;
  expiresAt: string;
  createdAt: string;
  updatedAt: string;
  ipAddress?: string;
  userAgent?: string;
  userId: string;
  impersonatedBy?: string | null;
}

export interface UserSession {
  user: User;
  session: Session;
}

export interface UserPreferences {
  theme: "light" | "dark" | "system";
  notifications: {
    email: boolean;
    push: boolean;
    pomodoroReminders: boolean;
    breakReminders: boolean;
  };
  pomodoro: {
    autoStartBreaks: boolean;
    autoStartPomodoros: boolean;
    soundEnabled: boolean;
    volume: number;
  };
  privacy: {
    profileVisible: boolean;
    statsVisible: boolean;
  };
  ui: {
    dismissedSignInPrompt: boolean;
  };
}

export interface UserStats {
  totalPomodoroSessions: number;
  totalFocusTime: number; // in seconds
  currentStreak: number;
  longestStreak: number;
  tasksCompleted: number;
  joinedDate: string;
  lastActiveDate: string;
}

export interface UserState {
  // Authentication state
  user: User | null;
  session: Session | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // User preferences
  preferences: UserPreferences;

  // User statistics
  stats: UserStats;

  // Actions
  setUser: (user: User | null) => void;
  setSession: (session: Session | null) => void;
  setUserSession: (userSession: UserSession | null) => void;
  updateUser: (updates: Partial<User>) => void;
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  updateStats: (stats: Partial<UserStats>) => void;
  clearUser: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // Computed getters
  isAdmin: () => boolean;
  isPremium: () => boolean;
  isSessionValid: () => boolean;
  getDisplayName: () => string;
  getAvatarUrl: () => string | null;
}
