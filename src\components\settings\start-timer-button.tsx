'use client';

import Link from 'next/link';
import { Play, ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface StartTimerButtonProps {
  isDisabled: boolean;
  className?: string;
}

export function StartTimerButton({ isDisabled, className }: StartTimerButtonProps) {
  const buttonVariants = {
    initial: {
      scale: 1,
      boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
    },
    hover: {
      scale: 1.02,
      y: -2,
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    },
    tap: {
      scale: 0.98,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    },
    disabled: {
      scale: 1,
      opacity: 0.6,
      cursor: "not-allowed"
    }
  };

  const iconVariants = {
    initial: { x: 0 },
    hover: {
      x: 2,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    }
  };

  const arrowVariants = {
    initial: { x: -4, opacity: 0 },
    hover: {
      x: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    }
  };

  const ButtonContent = (
    <motion.div
      className={cn(
        "relative rounded-lg overflow-hidden group h-14 w-full",
        "bg-gradient-to-r from-primary to-primary/90",
        "border border-primary/20 dark:border-primary/30",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        isDisabled ? "pointer-events-none" : "cursor-pointer"
      )}
      variants={buttonVariants}
      initial="initial"
      whileHover={!isDisabled ? "hover" : undefined}
      whileTap={!isDisabled ? "tap" : undefined}
      animate={isDisabled ? "disabled" : "initial"}
    >
      {/* Subtle gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-white/10 via-white/5 to-transparent opacity-80" />

      {/* Animated background shimmer */}
      {!isDisabled && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
          initial={{ x: "-100%" }}
          animate={{ x: "100%" }}
          transition={{
            duration: 3,
            repeat: Infinity,
            repeatDelay: 2,
            ease: "easeInOut"
          }}
        />
      )}

      {/* Button Content */}
      <div className="relative h-full flex items-center justify-center px-6 gap-3">
        {/* Icon container */}
        <motion.div
          className="flex items-center justify-center w-9 h-9 rounded-full bg-white/15 backdrop-blur-sm border border-white/20"
          variants={iconVariants}
        >
          <Play className="h-4 w-4 text-primary-foreground" />
        </motion.div>

        {/* Text content */}
        <div className="flex flex-col justify-center items-center text-center">
          <div className="flex items-center gap-2">
            <span className="text-base font-semibold text-primary-foreground tracking-wide">
              Start Timer
            </span>
            <motion.div variants={arrowVariants}>
              <ArrowRight className="h-4 w-4 text-primary-foreground/80" />
            </motion.div>
          </div>
          <span className="text-primary-foreground/80 text-xs font-medium">
            Begin your focus session
          </span>
        </div>
      </div>


    </motion.div>
  );

  return (
    <div className={cn("w-full max-w-[450px] mx-auto", className)}>
      {isDisabled ? (
        ButtonContent
      ) : (
        <Link href="/timer" className="block" tabIndex={-1}>
          {ButtonContent}
        </Link>
      )}
    </div>
  );
}