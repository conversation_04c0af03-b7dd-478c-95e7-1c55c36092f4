'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { X, Search, CheckCircle2, Clock, AlertCircle, LogIn, UserPlus, Sparkles, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

import { useUserStore } from '@/store/userStore';
import { TaskList } from './task-list';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { useTaskManagement } from '@/hooks/useTaskManagement';

interface TaskSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onTaskFocusStart?: () => void;
}

// Authentication UI Component
function AuthenticationPrompt({ onSignIn, onSignUp }: { onSignIn: () => void; onSignUp: () => void }) {
  return (
    <div className="flex-1 flex items-center justify-center p-8">
      <div className="text-center max-w-sm space-y-6">
        {/* Hero Icon */}
        <div className="relative">
          <div className="w-20 h-20 mx-auto bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl flex items-center justify-center relative overflow-hidden">
            <CheckCircle2 className="h-10 w-10 text-primary z-10" />
            <div className="absolute inset-0 bg-gradient-to-br from-primary/30 to-transparent rounded-2xl" />
          </div>
          <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-br from-orange-400 to-orange-500 rounded-full flex items-center justify-center">
            <Sparkles className="h-3 w-3 text-white" />
          </div>
        </div>

        {/* Content */}
        <div className="space-y-3">
          <h3 className="text-xl font-semibold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            Unlock Your Productivity
          </h3>
          <p className="text-sm text-muted-foreground leading-relaxed">
            Create, organize, and track your tasks with our powerful task management system. 
            <span className="block mt-1 font-medium text-foreground/80">Sign in to get started!</span>
          </p>
        </div>

        {/* Features */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="flex items-center gap-2 p-2 rounded-lg bg-muted/50">
            <CheckCircle2 className="h-3 w-3 text-green-500" />
            <span className="text-muted-foreground">Task Tracking</span>
          </div>
          <div className="flex items-center gap-2 p-2 rounded-lg bg-muted/50">
            <Clock className="h-3 w-3 text-blue-500" />
            <span className="text-muted-foreground">Pomodoro Timer</span>
          </div>
          <div className="flex items-center gap-2 p-2 rounded-lg bg-muted/50">
            <Shield className="h-3 w-3 text-purple-500" />
            <span className="text-muted-foreground">Secure Sync</span>
          </div>
          <div className="flex items-center gap-2 p-2 rounded-lg bg-muted/50">
            <Sparkles className="h-3 w-3 text-orange-500" />
            <span className="text-muted-foreground">AI Insights</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3 pt-2">
          <Button
            onClick={onSignIn}
            className="w-full h-11 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80 shadow-lg hover:shadow-xl transition-all duration-200 font-medium cursor-pointer"
          >
            <LogIn className="h-4 w-4 mr-2" />
            Sign In
          </Button>
          
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-border/50" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground font-medium">or</span>
            </div>
          </div>
          
          <Button
            onClick={onSignUp}
            variant="outline"
            className="w-full h-11 border-2 hover:bg-muted/50 transition-all duration-200 font-medium cursor-pointer"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Create Account
          </Button>
        </div>

        {/* Footer */}
        <p className="text-xs text-muted-foreground/70 pt-2">
          Join thousands of users boosting their productivity
        </p>
      </div>
    </div>
  );
}

export function TaskSheet({ isOpen, onClose, onTaskFocusStart }: TaskSheetProps) {
  const [showNewTaskForm, setShowNewTaskForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const router = useRouter();

  // Get authentication state from user store
  const { isAuthenticated, user } = useUserStore();

  // Use the hybrid task management hook
  const { taskStats } = useTaskManagement({
    filters: {
      searchTerm
    }
  });

  // Count tasks by status - with real-time updates from optimistic tasks
  const taskCounts = useMemo(() => {
    return {
      total: taskStats.totalTasks,
      completed: taskStats.completedTasks,
      pending: taskStats.pendingTasks
    };
  }, [taskStats]);

  const handleCreateTaskSuccess = useCallback(() => {
    setShowNewTaskForm(false);
  }, []);

  // Handle sign-in action
  const handleSignIn = useCallback(() => {
    router.push('/auth/sign-in');
  }, [router]);

  // Handle sign-up action
  const handleSignUp = useCallback(() => {
    router.push('/auth/sign-up');
  }, [router]);

  // Handle escape key to close and body scroll
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      if (!isOpen) {
        document.body.style.overflow = 'unset';
      }
    };
  }, [isOpen, onClose]);

  return (
    <>
      {/* Backdrop */}
      <div
        className={cn(
          "fixed inset-0 bg-black/10 backdrop-blur-sm z-[60] transition-opacity duration-300 ease-out",
          isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        )}
        onClick={onClose}
      />

      {/* Task Sheet */}
      <div className={cn(
        "fixed left-0 top-0 bottom-0 w-full sm:w-96 bg-white border-r border-border/30 shadow-2xl z-[70] flex flex-col transition-transform duration-300 ease-out",
        isOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        {/* Header */}
        <div className="bg-white border-b border-border/30">
          {/* Top Header Bar */}
          <div className="flex items-center justify-between p-4 pb-3">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <CheckCircle2 className="h-5 w-5 text-primary" />
                </div>
                <div className="flex flex-col">
                  <h2 className="text-lg font-semibold leading-tight">Tasks</h2>
                  {isAuthenticated && user ? (
                    <span className="text-xs text-muted-foreground leading-tight">
                      Welcome back, {user.name?.split(' ')[0] || 'User'}! 👋
                    </span>
                  ) : (
                    <span className="text-xs text-muted-foreground leading-tight">
                      Organize your productivity
                    </span>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {/* Task Counter Badge */}
              <Badge variant="secondary" className="text-xs px-2 py-1 font-medium">
                {taskCounts.total} {taskCounts.total === 1 ? 'task' : 'tasks'}
              </Badge>
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 rounded-full hover:bg-muted transition-all duration-200"
                    onClick={onClose}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <span>Close tasks panel</span>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>

          {/* Search Controls */}
          <div className="p-4 pt-0 bg-gradient-to-b from-white to-gray-50/30">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tasks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9 h-9 bg-white border-border/40 focus:ring-2 focus:ring-primary/20 transition-all duration-200 shadow-sm"
              />
            </div>

            {/* Sign-in prompt for unauthenticated users */}
            {/* {!isAuthenticated && (
              <div className="flex items-center justify-center mt-4">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={handleSignIn}
                      variant="outline"
                      size="sm"
                      className="h-9 gap-2 text-xs hover:bg-gray-50 transition-colors bg-white border-border/40 shadow-sm"
                    >
                      <LogIn className="h-3.5 w-3.5" />
                      <span>Sign In to Sync</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="z-[80]">
                    <span>Sign in to sync your tasks across devices</span>
                  </TooltipContent>
                </Tooltip>
              </div>
            )} */}
          </div>
        </div>

        {/* Task List */}
        <ScrollArea className="flex-1 task-sheet-scroll bg-white">
          <div className="bg-white"> 
            <TaskList
              searchTerm={searchTerm}
              showNewTaskForm={showNewTaskForm}
              onCreateTaskSuccess={handleCreateTaskSuccess}
              onCancelNewTask={() => setShowNewTaskForm(false)}
              onShowNewTaskForm={() => setShowNewTaskForm(true)}
              onTaskFocusStart={onTaskFocusStart}
            />
          </div>
        </ScrollArea>

        {/* Footer */}
        <div className="border-t border-border/30 bg-white">
          {/* Local storage notice for unauthenticated users */}
          {!isAuthenticated && taskCounts.total > 0 && (
            <div className="p-4 border-b border-border/30">
              <div className="bg-blue-50/50 border border-blue-200/40 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-4 w-4 text-muted-foreground mt-0.5 shrink-0" />
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-foreground/90">
                      Tasks saved locally
                    </p>
                    <p className="text-xs text-muted-foreground leading-relaxed">
                      Your tasks are stored in your browser. <button
                        onClick={handleSignIn}
                        className="text-primary hover:underline font-medium"
                      >
                        Sign in
                      </button> to sync across devices and keep them safe.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <div className="p-4">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>
                {taskCounts.pending} pending, {taskCounts.completed} completed
              </span>
              <div className="flex items-center gap-2">
                <span>{taskCounts.total} total</span>
                {!isAuthenticated && (
                  <span className="text-orange-500">• Local</span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
} 