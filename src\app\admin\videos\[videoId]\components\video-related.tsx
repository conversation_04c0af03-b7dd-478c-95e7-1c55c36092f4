"use client";

import { useGetVideos } from "@schemas/Video/video-query";
import { Skeleton } from "@/components/ui/skeleton";
import Image from "next/image";
import Link from "next/link";
import { Crown } from "lucide-react";
import { Video } from "../../components/types";

interface VideoRelatedProps {
  currentVideoId: string;
  playlistId?: string | null;
}

export function VideoRelated({ currentVideoId, playlistId }: VideoRelatedProps) {
  // Get related videos - if playlistId exists, get videos from same playlist
  // Otherwise get public videos
  const filters = playlistId 
    ? { isPublic: true, playlistId } 
    : { isPublic: true };
  
  const { data: videos, isLoading, error } = useGetVideos(filters);

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="flex gap-2">
            <Skeleton className="h-20 w-36 rounded-md" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-24" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error || !videos) {
    return (
      <div className="text-center p-4 text-muted-foreground">
        Could not load related videos
      </div>
    );
  }

  // Filter out current video and limit to 5
  const relatedVideos = videos
    .filter((video: Video) => video.id !== currentVideoId)
    .slice(0, 5);

  if (relatedVideos.length === 0) {
    return (
      <div className="text-center p-4 text-muted-foreground">
        No related videos found
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {relatedVideos.map((video: Video) => (
        <Link 
          key={video.id} 
          href={`/admin/videos/${video.id}`}
          className="flex gap-3 group hover:bg-accent p-2 rounded-md transition-colors"
        >
          <div className="relative h-20 w-36 rounded-md overflow-hidden flex-shrink-0">
            <Image
              src={video.thumbnail}
              alt={video.title}
              fill
              className="object-cover"
              sizes="144px"
            />
            {video.isPremium && (
              <div className="absolute top-1 right-1 bg-amber-500 text-white text-xs px-1 py-0.5 rounded flex items-center">
                <Crown className="h-3 w-3 mr-0.5" />
                <span className="text-[10px]">Premium</span>
              </div>
            )}
          </div>
          
          <div className="flex flex-col justify-center overflow-hidden">
            <h3 className="font-medium text-sm line-clamp-2 group-hover:text-primary transition-colors">
              {video.title}
            </h3>
            {video.playlist && (
              <p className="text-xs text-muted-foreground mt-1">
                {video.playlist.name}
              </p>
            )}
          </div>
        </Link>
      ))}
      
      {playlistId && relatedVideos.length > 0 && (
        <Link 
          href={`/admin/music-playlists/${playlistId}`}
          className="block w-full text-center text-sm text-muted-foreground hover:text-primary py-2"
        >
          View all in playlist
        </Link>
      )}
    </div>
  );
} 