'use client';

import { cn } from '@/lib/utils';
import { TimerColorPreset } from '@/lib/pomodoro-store';
import { getTimerTextColor, getTimerTextColorValue, getTimerTextShadow, getPhaseLabelColor } from './common/constants';
import type { FontSize } from './use-timer-resize';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface TimerMainDisplayProps {
  formattedTime: string;
  phaseLabel: string;
  isRunning: boolean;
  currentPhase: string;
  pomodoroCount: number;
  timerColor: TimerColorPreset;
  showControls: boolean;
  timerSize: {
    width?: number;
    timeScale?: number;
  } | null;
  fontSize: FontSize | null;
  timerSettings: {
    longBreakInterval?: number;
    sessionsBeforeLongBreak?: number;
  };
  onSwitchPhase: (direction: 'prev' | 'next') => void;
  currentTask: { id: string; title: string } | null;
}

export function TimerMainDisplay({
  formattedTime,
  phaseLabel,
  isRunning,
  currentPhase,
  pomodoroCount,
  timerColor,
  showControls,
  timerSize,
  fontSize,
  timerSettings,
  onSwitchPhase,
  currentTask
}: TimerMainDisplayProps) {
  // Helper function for phase color
  const getPhaseColor = () => {
    switch(currentPhase) {
      case 'pomodoro': return 'bg-blue-500';
      case 'shortBreak': return 'bg-emerald-500';
      case 'longBreak': return 'bg-purple-500';
      default: return 'bg-blue-500';
    }
  };

  // Function to get the session label
  const getSessionLabel = () => {
    if (currentPhase === 'shortBreak') {
      return `Short Break (${pomodoroCount}/${timerSettings.longBreakInterval || timerSettings.sessionsBeforeLongBreak})`;
    } else if (currentPhase === 'longBreak') {
      return 'Long Break';
    } else {
      // For focus sessions, show task title if available, otherwise show session count
      return currentTask 
        ? currentTask.title 
        : `Focus ${pomodoroCount}`;
    }
  };

  return (
    <div className="relative w-full">
      {/* Header Container - Holds both labels in the same space */}
      <div className={cn(
        "relative",
        showControls ? "h-7 mb-1" : isRunning ? "h-5 mb-0.5" : "h-5 mb-0.5"
      )}
      style={{
        height: timerSize?.timeScale && timerSize.timeScale > 1.3
          ? showControls
            ? `${Math.max(1.75, Math.min(2.5, 1.75 + (timerSize.timeScale - 1.3) * 0.8))}rem`
            : `${Math.max(1.25, Math.min(1.8, 1.25 + (timerSize.timeScale - 1.3) * 0.6))}rem`
          : undefined,
        marginBottom: timerSize?.timeScale && timerSize.timeScale > 1.3
          ? `${Math.max(0.25, Math.min(0.75, 0.25 + (timerSize.timeScale - 1.3) * 0.5))}rem`
          : undefined
      }}>
        {/* Phase Label with Switcher - Only visible on hover */}
        <div
          className={cn(
            "absolute inset-0 font-medium text-center",
            "flex items-center justify-center gap-1.5",
            "timer-control-fade",
            showControls ? "show" : "hide pointer-events-none",
            getPhaseLabelColor(timerColor) // Apply phase label color
          )}
          style={{
            textShadow: '0 2px 4px rgba(0,0,0,0.4), 0 1px 2px rgba(0,0,0,0.3)', // Enhanced shadow for glass backgrounds
            fontSize: timerSize?.width
              ? `${Math.max(0.5, Math.min(0.7, 0.62 * Math.pow(timerSize.width / 320, 0.25)))}rem`
              : 'clamp(0.55rem, 1vw, 0.7rem)',
            letterSpacing: '0.04em',
            textTransform: 'uppercase',
            zIndex: 11,
            animationDelay: '30ms'
          }}
        >
          {/* Left chevron for phase switching */}
          {showControls && (
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-5 w-5 mr-1 flex items-center justify-center p-0 transition-all duration-250 ease-out cursor-pointer",
                "rounded-full bg-black/30 hover:bg-black/40 border-white/10",
                "text-white focus:ring-1 focus:ring-white/30 focus:ring-offset-0 shadow-sm",
                "opacity-100 hover:scale-105 active:scale-95"
              )}
              onClick={() => onSwitchPhase('prev')}
              title="Previous phase"
              aria-label="Switch to previous phase"
            >
              <ChevronLeft className="h-3 w-3 text-white" />
            </Button>
          )}

          {/* Phase indicator */}
          <div className={cn(
            "w-2 h-2 rounded-full transition-properties ease-out ring-2 ring-white/30",
            getPhaseColor()
          )} />
          <span className={getPhaseLabelColor(timerColor)}>{phaseLabel}</span>

          {/* Right chevron for phase switching */}
          {showControls && (
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-5 w-5 ml-1 flex items-center justify-center p-0 transition-all duration-250 ease-out cursor-pointer",
                "rounded-full bg-black/30 hover:bg-black/40 border-white/10",
                "text-white focus:ring-1 focus:ring-white/30 focus:ring-offset-0 shadow-sm",
                "opacity-100 hover:scale-105 active:scale-95"
              )}
              onClick={() => onSwitchPhase('next')}
              title="Next phase"
              aria-label="Switch to next phase"
            >
              <ChevronRight className="h-3 w-3 text-white" />
            </Button>
          )}
        </div>

        {/* Session Counter - Only visible when not hovering */}
        <div
          className={cn(
            "absolute inset-0 text-center",
            "flex items-center justify-center",
            "timer-control-fade",
            !showControls ? "show" : "hide pointer-events-none",
            getPhaseLabelColor(timerColor) // Apply phase label color for session info
          )}
          style={{
            letterSpacing: '0.02em',
            fontSize: timerSize?.width
              ? `${Math.max(0.5, Math.min(0.68, 0.62 * Math.pow(timerSize.width / 320, 0.25)))}rem`
              : '0.62rem',
            zIndex: 10,
            animationDelay: '30ms'
          }}
        >
          <div className="flex items-center justify-center gap-1.5">
            <span className={cn(
              "px-2 py-0.5 rounded-md transition-properties ease-out font-medium",
              // Remove hardcoded background colors for cleaner appearance
              "bg-transparent"
            )}>
              {getSessionLabel()}
            </span>
          </div>
        </div>
      </div>

      <div
        className={cn(
          "font-bold text-center transition-properties ease-out",
          "font-mono", // Ensure monospace for consistent rendering
          getTimerTextColor(timerColor) // Apply timer text color
        )}
        style={{
          textShadow: getTimerTextShadow(timerColor),
          color: getTimerTextColorValue(timerColor),
          fontSize: fontSize?.time || 'clamp(2.2rem, 4.5vw, 3.2rem)',
          lineHeight: 1.1,
          transform: timerSize?.timeScale ? `scale3d(${timerSize.timeScale}, ${timerSize.timeScale}, 1)` : undefined,
          transformOrigin: 'center center',
          display: 'block',
          padding: timerSize?.timeScale && timerSize.timeScale > 1.3
            ? showControls
              ? `${Math.max(0.15, Math.min(0.25, 0.15 + (timerSize.timeScale - 1.3) * 0.12))}em 0`
              : `${Math.max(0.1, Math.min(0.18, 0.1 + (timerSize.timeScale - 1.3) * 0.08))}em 0`
            : showControls ? '0.12em 0' : '0.08em 0',
          letterSpacing: '0.02em',
          fontVariantNumeric: 'tabular-nums',
          width: 'min-content',
          minWidth: '5.5ch',
          margin: '0 auto',
          alignSelf: showControls ? 'center' : 'flex-end',
          marginTop: timerSize?.timeScale && timerSize.timeScale > 1.3
            ? `${Math.max(0.5, Math.min(1.2, 0.5 + (timerSize.timeScale - 1.3) * 0.8))}rem`
            : showControls ? '0.4rem' : '0.2rem',
          marginBottom: timerSize?.timeScale && timerSize.timeScale > 1.3
            ? `${Math.max(0.3, Math.min(0.9, 0.3 + (timerSize.timeScale - 1.3) * 0.6))}rem`
            : showControls ? '0.1rem' : '0.05rem',
          // Add performance optimizations for text rendering
          WebkitFontSmoothing: 'antialiased',
          WebkitBackfaceVisibility: 'hidden' as const,
          backfaceVisibility: 'hidden' as const,
          contain: 'paint' as const
        }}
        aria-live="polite"
      >
        {formattedTime}
      </div>
    </div>
  );
}