import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { InferRequestType, InferResponseType } from "hono";
import client from "@/lib/trpc";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

// Get all music playlists (Admin)
export type GetAdminMusicPlaylists_ResponseType = InferResponseType<
  (typeof client.api.admin.musicPlaylists)["$get"],
  200
>;

export type GetAdminMusicPlaylists_ResponseTypeSuccess = Extract<
  GetAdminMusicPlaylists_ResponseType,
  { data: object }
>["data"];

export const useGetAdminMusicPlaylists = (filters?: {
  isPublic?: boolean;
  isDefault?: boolean;
}) => {
  return useQuery({
    queryKey: ["admin", "musicPlaylists", filters],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      
      if (filters?.isPublic !== undefined) {
        queryParams.append("isPublic", String(filters.isPublic));
      }
      
      if (filters?.isDefault !== undefined) {
        queryParams.append("isDefault", String(filters.isDefault));
      }
      
      const queryString = queryParams.toString();
      
      let response;
      if (queryString) {
        response = await client.api.admin.musicPlaylists.$get({
          query: { 
            isPublic: filters?.isPublic !== undefined ? String(filters.isPublic) : undefined,
            isDefault: filters?.isDefault !== undefined ? String(filters.isDefault) : undefined
          }
        });
      } else {
        response = await client.api.admin.musicPlaylists.$get();
      }
      
      if (!response.ok) {
        throw new Error("Failed to fetch admin music playlists");
      }
      const { data } = await response.json();
      return data;
    },
  });
};

// Get single music playlist (Admin)
type GetAdminMusicPlaylist_ResponseType = InferResponseType<
  (typeof client.api.admin.musicPlaylists)[":id"]["$get"],
  200
>;

export type GetAdminMusicPlaylist_ResponseTypeSuccess = Extract<
  GetAdminMusicPlaylist_ResponseType,
  { data: object }
>["data"];

export const useGetAdminMusicPlaylist = (id?: string) => {
  return useQuery({
    queryKey: ["admin", "musicPlaylists", { id }],
    queryFn: async () => {
      if (!id) throw new Error("No music playlist ID provided");

      const response = await client.api.admin.musicPlaylists[":id"]["$get"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch admin music playlist");
      }

      const { data } = await response.json();
      return data;
    },
    enabled: !!id,
  });
};

// Create music playlist (Admin)
interface CreateAdminMusicPlaylistSuccessResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

type CreateAdminMusicPlaylistRequest = InferRequestType<
  (typeof client.api.admin.musicPlaylists)["$post"]
>;

export const useCreateAdminMusicPlaylist = () => {
  const router = useRouter();
  const queryClient = useQueryClient();

  const mutation = useMutation<
    CreateAdminMusicPlaylistSuccessResponse,
    Error,
    CreateAdminMusicPlaylistRequest
  >({
    mutationFn: async ({ form }) => {
      const response = await client.api.admin.musicPlaylists.$post({ form });

      if (!response.ok) {
        throw new Error("Failed to create admin music playlist");
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Music playlist created successfully");
      router.refresh();
      queryClient.invalidateQueries({ queryKey: ["admin", "musicPlaylists"] });
    },
    onError: (error) => {
      toast.error(`Failed to create music playlist: ${error.message}`);
    },
  });

  return mutation;
};

// Update music playlist (Admin)
type UpdateAdminMusicPlaylist_ResponseType = InferResponseType<
  (typeof client.api.admin.musicPlaylists)[":id"]["$patch"],
  200
>;

export type UpdateAdminMusicPlaylist_ResponseTypeSuccess = Extract<
  UpdateAdminMusicPlaylist_ResponseType,
  { data: object }
>["data"];

type UpdateAdminMusicPlaylistRequest = InferRequestType<
  (typeof client.api.admin.musicPlaylists)[":id"]["$patch"]
>;

export const useUpdateAdminMusicPlaylist = () => {
  const queryClient = useQueryClient();
  
  return useMutation<
    UpdateAdminMusicPlaylist_ResponseType,
    Error,
    UpdateAdminMusicPlaylistRequest
  >({
    mutationFn: async (variables) => {
      const { form, param } = variables;

      if (!param?.id) {
        throw new Error("No music playlist ID provided");
      }

      const response = await client.api.admin.musicPlaylists[":id"]["$patch"]({
        form,
        param: { id: param.id },
      });

      if (!response.ok) {
        throw new Error(`Failed to update admin music playlist. Status: ${response.status}`);
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Music playlist updated successfully");
      const musicPlaylistId = data?.id;
      if (musicPlaylistId) {
        queryClient.invalidateQueries({ queryKey: ["admin", "musicPlaylists", { id: musicPlaylistId }] });
      }
      queryClient.invalidateQueries({ queryKey: ["admin", "musicPlaylists"] });
    },
    onError: (error) => {
      toast.error(`Failed to update music playlist: ${error.message}`);
    },
  });
};

// Delete music playlist (Admin)
type DeleteAdminMusicPlaylist_ResponseType = InferResponseType<
  (typeof client.api.admin.musicPlaylists)[":id"]["$delete"],
  200
>;

export const useDeleteAdminMusicPlaylist = () => {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation<
    DeleteAdminMusicPlaylist_ResponseType,
    Error,
    { id: string }
  >({
    mutationFn: async ({ id }) => {
      if (!id) {
        throw new Error("No music playlist ID provided");
      }

      const response = await client.api.admin.musicPlaylists[":id"]["$delete"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to delete admin music playlist");
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Music playlist deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["admin", "musicPlaylists"] });
      router.push("/admin/music-playlists");
    },
    onError: (error) => {
      toast.error(`Failed to delete music playlist: ${error.message}`);
    },
  });
};

// Add Music to Music Playlist (Admin)
interface AdminMusicResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface AddMusicToAdminMusicPlaylistRequest {
  musicPlaylistId: string;
  musicIds: string[];
}

export const useAddMusicToAdminMusicPlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    AdminMusicResponse,
    Error,
    AddMusicToAdminMusicPlaylistRequest
  >({
    mutationFn: async ({ musicPlaylistId, musicIds }) => {
      if (!musicPlaylistId) {
        throw new Error("No music playlist ID provided");
      }

      const response = await client.api.admin.musicPlaylists[":id"].music.$post({
        param: { id: musicPlaylistId },
        json: { musicIds }
      });

      if (!response.ok) {
        throw new Error(`Failed to add music to admin music playlist: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Music added to music playlist successfully");
      queryClient.invalidateQueries({ queryKey: ["admin", "musicPlaylists", { id: variables.musicPlaylistId }] });
    },
    onError: (error) => {
      toast.error(`Failed to add music to music playlist: ${error.message}`);
    },
  });
};

// Remove Music from Music Playlist (Admin)
interface RemoveAdminMusicResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface RemoveMusicFromAdminMusicPlaylistRequest {
  musicPlaylistId: string;
  musicId: string;
}

export const useRemoveMusicFromAdminMusicPlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    RemoveAdminMusicResponse,
    Error,
    RemoveMusicFromAdminMusicPlaylistRequest
  >({
    mutationFn: async ({ musicPlaylistId, musicId }) => {
      if (!musicPlaylistId || !musicId) {
        throw new Error("Both music playlist ID and music ID are required");
      }

      const response = await client.api.admin.musicPlaylists[":id"].music[":musicId"]["$delete"]({
        param: { id: musicPlaylistId, musicId },
      });

      if (!response.ok) {
        throw new Error(`Failed to remove music from admin music playlist: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Music removed from music playlist");
      queryClient.invalidateQueries({ queryKey: ["admin", "musicPlaylists", { id: variables.musicPlaylistId }] });
      queryClient.invalidateQueries({ queryKey: ["admin", "musicPlaylists"] });
    },
    onError: (error) => {
      toast.error(`Failed to remove music from music playlist: ${error.message}`);
    },
  });
};

// Reorder Music in Music Playlist (Admin)
interface ReorderAdminMusicResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface ReorderAdminMusicRequest {
  musicPlaylistId: string;
  musicOrder: string[];
}

export const useReorderMusicInAdminMusicPlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    ReorderAdminMusicResponse,
    Error,
    ReorderAdminMusicRequest
  >({
    mutationFn: async ({ musicPlaylistId, musicOrder }) => {
      if (!musicPlaylistId) {
        throw new Error("No music playlist ID provided");
      }

      const response = await client.api.admin.musicPlaylists[":id"].music.reorder.$patch({
        param: { id: musicPlaylistId },
        json: { musicOrder }
      });

      if (!response.ok) {
        throw new Error(`Failed to reorder music in admin music playlist: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Music order updated successfully");
      queryClient.invalidateQueries({ queryKey: ["admin", "musicPlaylists", { id: variables.musicPlaylistId }] });
    },
    onError: (error) => {
      toast.error(`Failed to reorder music: ${error.message}`);
    },
  });
};

// Add Videos to Music Playlist (Admin)
interface AdminMusicVideoResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface AddVideosToAdminMusicPlaylistRequest {
  musicPlaylistId: string;
  videoIds: string[];
}

export const useAddVideosToAdminMusicPlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    AdminMusicVideoResponse,
    Error,
    AddVideosToAdminMusicPlaylistRequest
  >({
    mutationFn: async ({ musicPlaylistId, videoIds }) => {
      if (!musicPlaylistId) {
        throw new Error("No music playlist ID provided");
      }

      const response = await client.api.admin.musicPlaylists[":id"].videos.$post({
        param: { id: musicPlaylistId },
        json: { videoIds }
      });

      if (!response.ok) {
        throw new Error(`Failed to add videos to admin music playlist: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Videos added to music playlist successfully");
      queryClient.invalidateQueries({ queryKey: ["admin", "musicPlaylists", { id: variables.musicPlaylistId }] });
    },
    onError: (error) => {
      toast.error(`Failed to add videos to music playlist: ${error.message}`);
    },
  });
};

// Remove Video from Music Playlist (Admin)
interface RemoveAdminMusicVideoResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface RemoveVideoFromAdminMusicPlaylistRequest {
  musicPlaylistId: string;
  videoId: string;
}

export const useRemoveVideoFromAdminMusicPlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    RemoveAdminMusicVideoResponse,
    Error,
    RemoveVideoFromAdminMusicPlaylistRequest
  >({
    mutationFn: async ({ musicPlaylistId, videoId }) => {
      if (!musicPlaylistId || !videoId) {
        throw new Error("Both music playlist ID and video ID are required");
      }

      const response = await client.api.admin.musicPlaylists[":id"].videos[":videoId"]["$delete"]({
        param: { id: musicPlaylistId, videoId },
      });

      if (!response.ok) {
        throw new Error(`Failed to remove video from admin music playlist: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Video removed from music playlist");
      queryClient.invalidateQueries({ queryKey: ["admin", "musicPlaylists", { id: variables.musicPlaylistId }] });
    },
    onError: (error) => {
      toast.error(`Failed to remove video from music playlist: ${error.message}`);
    },
  });
};

 