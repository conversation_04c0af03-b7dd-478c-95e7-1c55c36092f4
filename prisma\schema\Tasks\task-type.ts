import { z } from "zod";
import { TaskSchema, TaskPartialSchema } from "@types";

export const createTaskSchema = TaskSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
}).extend({
  completed: z.union([
    z.boolean(),
    z.string().transform(val => val === "true"),
  ]).optional().default(false),
});

export const updateTaskSchema = TaskPartialSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
}).extend({
  completed: z.union([
    z.boolean(),
    z.string().transform(val => val === "true"),
  ]).optional(),
});

export type CreateTaskInput = z.infer<typeof createTaskSchema>;
export type UpdateTaskInput = z.infer<typeof updateTaskSchema>; 