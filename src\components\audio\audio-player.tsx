'use client';

import { useEffect, useRef, useState } from 'react';
import { useAudioStore, type Audio } from '@/lib/audio-store';
import { Volume2, VolumeX, ListMusic, Volume1, Volume, PauseCircle, PlayCircle, Music, SkipForward, SkipBack } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { AudioGrid } from '@/components/audio/audio-grid';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';

// Replace with a simpler playing indicator
function PlayingIndicator() {
  return (
    <div className="absolute inset-0 bg-primary/5 rounded-md pointer-events-none border border-primary/10" />
  );
}

interface AudioPlayerProps {
  className?: string;
}

export function AudioPlayer({ className }: AudioPlayerProps) {
  const { selectedAudios, useVideoDefaultAudio, volume, isMuted, setVolume, toggleMute, categories } = useAudioStore();
  const [isAudioDialogOpen, setIsAudioDialogOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const expandTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentAudioIndex, setCurrentAudioIndex] = useState(0);
  const currentAudioIndexRef = useRef(0); // Synchronous tracking for event handlers
  const [autoplayFailed, setAutoplayFailed] = useState(false);
  const currentAudioRef = useRef<HTMLAudioElement | null>(null);
  const [currentAudioTitle, setCurrentAudioTitle] = useState("");
  const autoPlayAttemptedRef = useRef(false);

  // Group selected audios by category
  const selectedAudiosByCategory = selectedAudios.reduce<Record<string, number>>((acc, audio) => {
    acc[audio.categoryId] = (acc[audio.categoryId] || 0) + 1;
    return acc;
  }, {});

  // Initialize current audio when selected audios change
  useEffect(() => {
    if (typeof window === 'undefined' || useVideoDefaultAudio || selectedAudios.length === 0) {
      setCurrentAudioTitle("");
      return;
    }

    // Clean up previous audio element
    if (currentAudioRef.current) {
      currentAudioRef.current.pause();
      currentAudioRef.current.src = '';
      currentAudioRef.current = null;
    }

    // Reset the current index to 0 when selection changes
    setCurrentAudioIndex(0);
    currentAudioIndexRef.current = 0;
    
    // Create new audio element for the first track
    if (selectedAudios.length > 0) {
      const audio = new Audio(selectedAudios[0].src);
      audio.volume = isMuted ? 0 : volume / 100;
      
      // Set up the ended event to advance to the next track
      audio.onended = handleTrackEnded;
      
      currentAudioRef.current = audio;
      setCurrentAudioTitle(selectedAudios[0].title);
      
      // Auto-play on initial load with a slight delay
      if (!autoPlayAttemptedRef.current) {
        setTimeout(() => {
          if (currentAudioRef.current) {
            autoPlayAttemptedRef.current = true;
            currentAudioRef.current.play()
              .then(() => {
                setIsPlaying(true);
                setIsPaused(false);
                setAutoplayFailed(false);
              })
              .catch(err => {
                console.error("Failed to auto-play audio:", err);
                setAutoplayFailed(true);
              });
          }
        }, 300);
      }
    }

    // Clean up function
    return () => {
      if (currentAudioRef.current) {
        currentAudioRef.current.pause();
        currentAudioRef.current.onended = null;
        currentAudioRef.current.src = '';
        currentAudioRef.current = null;
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedAudios, useVideoDefaultAudio]);

  // Simple function to play the next track when current one ends
  const handleTrackEnded = () => {
    console.log(`Track ended. Current index: ${currentAudioIndexRef.current}, Total tracks: ${selectedAudios.length}`);
    
    // Calculate next track index (with looping)
    const nextIndex = (currentAudioIndexRef.current + 1) % selectedAudios.length;
    console.log(`Attempting to play next track at index: ${nextIndex}`);
    
    // Play the next track
    playTrack(nextIndex);
  };

  // Function to play a specific track by index
  const playTrack = (index: number) => {
    // Safety check - if selectedAudios changes and index is out of bounds
    if (selectedAudios.length === 0) return;
    if (index >= selectedAudios.length) index = 0;
    
    // Update current index both in state and ref
    setCurrentAudioIndex(index);
    currentAudioIndexRef.current = index;
    
    // Get the audio track
    const track = selectedAudios[index];
    setCurrentAudioTitle(track.title);
    
    // Clean up existing audio
    if (currentAudioRef.current) {
      currentAudioRef.current.pause();
      currentAudioRef.current.onended = null;
      currentAudioRef.current.src = '';
      currentAudioRef.current = null;
    }
    
    // Create new audio element
    const audio = new Audio(track.src);
    audio.volume = isMuted ? 0 : volume / 100;
    audio.onended = handleTrackEnded;
    currentAudioRef.current = audio;
    console.log(`Starting playback for track: ${track.title} at index: ${index}`);
    
    // Play the track
    audio.play()
      .then(() => {
        console.log(`Successfully playing track: ${track.title}`);
        setIsPlaying(true);
        setIsPaused(false);
        setAutoplayFailed(false);
      })
      .catch(err => {
        console.error(`Failed to play track: ${track.title}`, err);
        setAutoplayFailed(true);
        setIsPlaying(false);
        setIsPaused(true);
      });
  };

  // Update volume when it changes
  useEffect(() => {
    if (currentAudioRef.current) {
      currentAudioRef.current.volume = isMuted ? 0 : volume / 100;
    }
  }, [volume, isMuted]);

  // Handle user interaction to enable autoplay
  useEffect(() => {
    const handleUserInteraction = () => {
      if (autoplayFailed && currentAudioRef.current && !isPlaying) {
        currentAudioRef.current.play()
          .then(() => {
            setIsPlaying(true);
            setIsPaused(false);
            setAutoplayFailed(false);
          })
          .catch(err => {
            console.error("Failed to play audio after user interaction:", err);
          });
      }
    };

    // Add event listeners for user interaction
    document.addEventListener('click', handleUserInteraction);
    document.addEventListener('keydown', handleUserInteraction);
    document.addEventListener('touchstart', handleUserInteraction);

    return () => {
      document.removeEventListener('click', handleUserInteraction);
      document.removeEventListener('keydown', handleUserInteraction);
      document.removeEventListener('touchstart', handleUserInteraction);
    };
  }, [autoplayFailed, isPlaying]);

  // Toggle play/pause
  const togglePlay = () => {
    if (!currentAudioRef.current && selectedAudios.length > 0) {
      playTrack(currentAudioIndex);
      return;
    }

    if (isPlaying && currentAudioRef.current) {
      currentAudioRef.current.pause();
      setIsPaused(true);
      setIsPlaying(false);
    } else if (currentAudioRef.current) {
      currentAudioRef.current.play()
        .then(() => {
          setAutoplayFailed(false);
          setIsPaused(false);
          setIsPlaying(true);
        })
        .catch(err => {
          console.error("Failed to play audio:", err);
          setAutoplayFailed(true);
        });
    }
  };

  // Skip to next track
  const skipToNext = () => {
    const nextIndex = (currentAudioIndexRef.current + 1) % selectedAudios.length;
    playTrack(nextIndex);
  };

  // Skip to previous track
  const skipToPrevious = () => {
    const prevIndex = currentAudioIndexRef.current === 0 ? selectedAudios.length - 1 : currentAudioIndexRef.current - 1;
    playTrack(prevIndex);
  };

  // Handle volume changes
  const handleVolumeChange = (value: number[]) => {
    const newVolume = value[0];
    setVolume(newVolume);
    
    // Auto-collapse after a delay
    scheduleCollapseVolumeSlider();
  };
  
  // Schedule collapse of volume slider
  const scheduleCollapseVolumeSlider = () => {
    if (expandTimeoutRef.current) {
      clearTimeout(expandTimeoutRef.current);
    }
    
    expandTimeoutRef.current = setTimeout(() => {
      setIsExpanded(false);
      expandTimeoutRef.current = null;
    }, 3000);
  };
  
  // Expand volume slider
  const expandVolumeSlider = () => {
    setIsExpanded(true);
    scheduleCollapseVolumeSlider();
  };
  
  // Get volume icon based on volume level
  const getVolumeIcon = () => {
    if (isMuted) return <VolumeX className="h-4 w-4" />;
    if (volume === 0) return <Volume className="h-4 w-4" />;
    if (volume < 50) return <Volume1 className="h-4 w-4" />;
    return <Volume2 className="h-4 w-4" />;
  };

  // Get category name by id
  const getCategoryName = (categoryId: string): string => {
    return categories.find(c => c.id === categoryId)?.name || 'Uncategorized';
  };

  // If no audio selected or using video default audio, don't show player
  if (useVideoDefaultAudio || selectedAudios.length === 0) {
    return null;
  }

  return (
    <div className={cn(
      "fixed top-0 left-0 right-0 z-20 bg-background/50 backdrop-blur-md border-b border-border/20",
      className
    )}>
      <div className="flex items-center gap-2 bg-background/80 backdrop-blur-sm px-3 py-1.5 rounded-md shadow-sm relative group">
        {/* Playing indicator when audio is active */}
        {!isPaused && selectedAudios.length > 0 && <PlayingIndicator />}
        
        {/* Previous track button */}
        <TooltipProvider delayDuration={300}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={skipToPrevious}
                className="text-foreground h-8 w-8 relative z-10"
                aria-label="Previous Track"
              >
                <SkipBack className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top">
              Previous Track
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        
        {/* Play/Pause button */}
        <TooltipProvider delayDuration={300}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={togglePlay}
                className="text-foreground h-8 w-8 relative z-10"
                aria-label={isPlaying ? "Pause" : "Play"}
              >
                {isPlaying ? (
                  <PauseCircle className="h-5 w-5" />
                ) : (
                  <PlayCircle className="h-5 w-5" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top">
              {isPlaying ? "Pause" : "Play"}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        
        {/* Next track button */}
        <TooltipProvider delayDuration={300}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={skipToNext}
                className="text-foreground h-8 w-8 relative z-10"
                aria-label="Next Track"
              >
                <SkipForward className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top">
              Next Track
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        
        {autoplayFailed && (
          <div className="absolute -top-8 left-0 right-0 text-center">
            <Badge variant="destructive" className="text-xs">
              Click or interact with page to start audio
            </Badge>
          </div>
        )}
        
        {/* Volume controls */}
        <div 
          className={cn(
            "flex items-center gap-2 transition-all duration-200 ease-in-out relative z-10",
            isExpanded ? "w-32" : "w-auto"
          )}
          onMouseEnter={expandVolumeSlider}
        >
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => {
                    toggleMute();
                    expandVolumeSlider();
                  }}
                  className="text-foreground h-8 w-8"
                  aria-label={isMuted ? "Unmute" : "Mute"}
                >
                  {getVolumeIcon()}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">
                {isMuted ? "Unmute" : "Mute"}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {isExpanded && (
            <Slider
              value={[volume]}
              min={0}
              max={100}
              step={1}
              onValueChange={handleVolumeChange}
              className="flex-1"
              aria-label="Volume control"
            />
          )}
        </div>

        {/* Audio track selector */}
        <TooltipProvider delayDuration={300}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => setIsAudioDialogOpen(true)}
                className="text-foreground h-8 w-8 relative z-10"
                aria-label="Manage audio tracks"
              >
                <ListMusic className="h-4 w-4" />
                {selectedAudios.length > 0 && (
                  <Badge variant="secondary" className="absolute -top-1 -right-1 h-4 min-w-4 px-1 flex items-center justify-center text-[10px] leading-none">
                    {selectedAudios.length}
                  </Badge>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top">
              Manage audio tracks
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        
        {/* Current track information */}
        {currentAudioTitle && (
          <div className="ml-2 text-xs font-medium max-w-28 overflow-hidden overflow-ellipsis whitespace-nowrap">
            {currentAudioTitle}
          </div>
        )}
        
        {/* Now playing indicator label */}
        <div className="absolute right-3 top-[-20px] opacity-0 group-hover:opacity-100 transition-opacity">
          <Badge variant="outline" className="text-xs bg-background/80 backdrop-blur-sm">
            {isPlaying ? 
              `Playing ${currentAudioIndex + 1}/${selectedAudios.length}` : 
              "Ready to Play"}
          </Badge>
        </div>
      </div>

      {/* Audio selection dialog */}
      <Dialog open={isAudioDialogOpen} onOpenChange={setIsAudioDialogOpen}>
        <DialogContent className="sm:max-w-md md:max-w-lg lg:max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Music className="h-5 w-5" />
              <span>Audio Tracks</span>
            </DialogTitle>
            <DialogDescription>
              Select background sounds for your focus session. Tracks will play sequentially.
            </DialogDescription>
          </DialogHeader>

          {selectedAudios.length > 0 && (
            <div className="mb-4 -mt-1">
              <h3 className="text-sm font-medium mb-2">Currently Selected:</h3>
              <div className="flex flex-wrap gap-2">
                {Object.entries(selectedAudiosByCategory).map(([categoryId, count]) => (
                  <Badge key={categoryId} variant="outline" className="flex items-center gap-1.5">
                    <span>{getCategoryName(categoryId)}</span>
                    <Badge variant="secondary" className="text-[10px] h-4 min-w-4 flex items-center justify-center">
                      {count}
                    </Badge>
                  </Badge>
                ))}
              </div>
            </div>
          )}
          
          <ScrollArea className="max-h-[65vh]">
            <div className="py-2">
              <AudioGrid />
            </div>
          </ScrollArea>
          
          <DialogFooter>
            <Button variant="default" onClick={() => setIsAudioDialogOpen(false)}>
              Done
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 