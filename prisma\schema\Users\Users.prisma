model User {
    id            String    @id @default(cuid())
    name          String
    email         String    @unique
    emailVerified Boolean   @default(false)
    image         String?
    premium       Boolean   @default(false)
    role          User<PERSON><PERSON>  @default(USER)
    banned        Boolean   @default(false)
    banReason     String?
    banExpires    BigInt? /// Changed from Int to BigInt for Unix timestamp
    createdAt     DateTime  @default(now())
    updatedAt     DateTime  @updatedAt
    sessions      Session[]
    accounts      Account[]

    // New payment-related relationships
    subscriptions Subscription[]
    payments      Payment[]

    subscriptionType SubscriptionType @default(FREE)

    // User owned content
    videos          Video[]
    musicPlaylists  MusicPlaylist[]
    musicPlaylistsUser MusicPlaylistUser[]
    naturePlaylists NaturePlaylist[]
    musics          Music[]
    natureSounds    NatureSound[]
    // User favorites
    favoriteVideos  FavoriteVideo[]

    // Pomodoro related
    pomodoroSessions PomodoroSession[]
    tasks            Task[]

    @@index([email])
}

enum UserRole {
    USER
    ADMIN
}

enum SubscriptionType {
    FREE
    PREMIUM
    PREMIUM_PLUS
}

model Session {
    id             String   @id @default(cuid())
    userId         String
    token          String
    expiresAt      DateTime
    ipAddress      String?
    userAgent      String?
    impersonatedBy String?
    createdAt      DateTime @default(now())
    updatedAt      DateTime @updatedAt

    user User @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@index([userId])
    @@index([token])
}

model Account {
    id                    String    @id @default(cuid())
    userId                String
    accountId             String
    providerId            String
    accessToken           String?   @db.Text
    refreshToken          String?   @db.Text
    accessTokenExpiresAt  DateTime?
    refreshTokenExpiresAt DateTime?
    scope                 String?   @db.Text
    idToken               String?   @db.Text
    password              String?
    createdAt             DateTime  @default(now())
    updatedAt             DateTime  @updatedAt

    user User @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@unique([providerId, accountId])
    @@index([userId])
    @@index([accountId])
}

model Verification {
    id         String   @id @default(cuid())
    identifier String
    value      String
    expiresAt  DateTime
    createdAt  DateTime @default(now())
    updatedAt  DateTime @updatedAt

    @@index([identifier])
    @@index([value])
}
