'use client';

import { create } from 'zustand';

export type SoundType = 'bell' | 'check' | 'elevator' | 'funny' | 'level' | 'ping' | 'positive' | 'simple';

export interface NotificationSettings {
  soundType: SoundType;
  volume: number;
  repeat: number;
  enabled: boolean;
}

interface NotificationState {
  settings: NotificationSettings;
  preloadedAudio: HTMLAudioElement | null;

  // Actions
  setSoundType: (type: SoundType) => void;
  setVolume: (volume: number) => void;
  setRepeat: (count: number) => void;
  toggleEnabled: () => void;
  setEnabled: (enabled: boolean) => void;

  // Sound management
  playTestSound: () => void;
  preloadSound: () => void;
  getPreloadedSound: () => HTMLAudioElement | null;
}

// Load settings from localStorage if available
const loadSettings = (): NotificationSettings => {
  if (typeof window !== 'undefined') {
    try {
      const saved = localStorage.getItem('notificationSettings');
      if (saved) {
        return JSON.parse(saved);
      }
    } catch (e) {
      console.error('Failed to load notification settings:', e);
    }
  }

  return {
    soundType: 'bell',
    volume: 44, // Default volume (0-100)
    repeat: 1,
    enabled: true
  };
};

// Save settings to localStorage
const saveSettings = (settings: NotificationSettings): void => {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem('notificationSettings', JSON.stringify(settings));
    } catch (e) {
      console.error('Failed to save notification settings:', e);
    }
  }
};

// Map sound types to their file paths
export const soundMap: Record<SoundType, string> = {
  bell: '/notifications/bell.mp3',
  check: '/notifications/check.mp3',
  elevator: '/notifications/elavator.mp3', // Note: keeping original filename spelling
  funny: '/notifications/funny.mp3',
  level: '/notifications/level.mp3',
  ping: '/notifications/ping.mp3',
  positive: '/notifications/positive.mp3',
  simple: '/notifications/simple.mp3'
};

// Create the store
export const useNotificationStore = create<NotificationState>((set, get) => ({
  settings: loadSettings(),
  preloadedAudio: null,

  setSoundType: (type) => {
    set(state => {
      const newSettings = {
        ...state.settings,
        soundType: type
      };
      saveSettings(newSettings);
      return { settings: newSettings };
    });

    // Preload the new sound type
    get().preloadSound();
  },

  setVolume: (volume) => {
    set(state => {
      const newSettings = {
        ...state.settings,
        volume: Math.max(0, Math.min(100, volume))
      };
      saveSettings(newSettings);

      // Update volume on preloaded audio if exists
      const { preloadedAudio } = get();
      if (preloadedAudio) {
        preloadedAudio.volume = newSettings.volume / 100;
      }

      return { settings: newSettings };
    });
  },

  setRepeat: (count) => {
    set(state => {
      const newSettings = {
        ...state.settings,
        repeat: Math.max(1, Math.min(10, count))
      };
      saveSettings(newSettings);
      return { settings: newSettings };
    });
  },

  toggleEnabled: () => {
    set(state => {
      const newSettings = {
        ...state.settings,
        enabled: !state.settings.enabled
      };
      saveSettings(newSettings);
      return { settings: newSettings };
    });
  },

  setEnabled: (enabled) => {
    set(state => {
      const newSettings = {
        ...state.settings,
        enabled
      };
      saveSettings(newSettings);
      return { settings: newSettings };
    });
  },

  playTestSound: () => {
    const { settings } = get();

    if (typeof window !== 'undefined') {
      const { soundType, volume, repeat } = settings;
      const soundPath = soundMap[soundType];

      // Function to play sound once
      const playOnce = (index: number) => {
        setTimeout(() => {
          const audio = new Audio(soundPath);
          audio.volume = volume / 100;
          audio.play().catch(error => {
            console.error('Error playing notification sound:', error);
          });
        }, index * 1000); // Play with a 1-second delay between repeats
      };

      // Play the sound the specified number of times
      for (let i = 0; i < repeat; i++) {
        playOnce(i);
      }
    }
  },

  preloadSound: () => {
    const { settings } = get();

    if (typeof window !== 'undefined' && settings.enabled) {
      const soundPath = soundMap[settings.soundType];

      try {
        // Create new audio element and preload it
        const audio = new Audio(soundPath);
        audio.volume = settings.volume / 100;
        audio.preload = 'auto';

        // Load the audio
        audio.load();

        // Store the preloaded audio
        set({ preloadedAudio: audio });

        console.log(`Preloaded sound: ${settings.soundType}`);
      } catch (error) {
        console.error('Error preloading notification sound:', error);
        set({ preloadedAudio: null });
      }
    }
  },

  getPreloadedSound: () => {
    return get().preloadedAudio;
  }
}));