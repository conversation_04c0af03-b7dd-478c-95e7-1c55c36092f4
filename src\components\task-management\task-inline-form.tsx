'use client';

import { useState, useRef, useEffect } from 'react';
import { X, Check, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useTaskManagement, type UnifiedTask } from '@/hooks/useTaskManagement';
import { cn } from '@/lib/utils';

type TaskType = UnifiedTask;

interface TaskInlineFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  onSubmitStart?: () => void;
  placeholder?: string;
  className?: string;
  editTask?: TaskType;
  isEditing?: boolean;
}

export function TaskInlineForm({ 
  onSuccess, 
  onCancel,
  onSubmitStart,
  placeholder = "Enter task title...", 
  className,
  editTask,
  isEditing = false
}: TaskInlineFormProps) {
  const [title, setTitle] = useState(editTask?.title || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const { createTask, updateTask, isCreating, isUpdating } = useTaskManagement();

  // Auto focus when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
      // Select all text if editing
      if (isEditing) {
        inputRef.current.select();
      }
    }
  }, [isEditing]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const trimmedTitle = title.trim();
    if (!trimmedTitle || isSubmitting) {
      return;
    }

    // Notify parent that submission has started
    onSubmitStart?.();
    setIsSubmitting(true);

    try {
      if (isEditing && editTask) {
        await updateTask(editTask.id, { title: trimmedTitle });
        // Reset form and close immediately for edit
        setTitle('');
        onSuccess?.();
      } else {
        // For new task creation with optimistic updates
        await createTask({
          title: trimmedTitle,
          completed: false
        });
        
        // Clear form and close immediately since the task appears optimistically
        setTitle('');
        onSuccess?.();
      }
    } catch (error) {
      console.error(`Failed to ${isEditing ? 'update' : 'create'} task:`, error);
      // Don't call onSuccess on error, keep form open
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setTitle(editTask?.title || '');
    onCancel?.();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleCancel();
    }
  };

  // Use local submitting state for immediate feedback
  const isLoading = isSubmitting;
  const canSubmit = title.trim().length > 0 && !isLoading;

  return (
    <div
      className={cn(
        "relative rounded-lg border bg-white shadow-sm overflow-hidden transition-all duration-200",
        "focus-within:ring-1 focus-within:ring-primary/30 focus-within:border-primary/50",
        className
      )}
    >
      <form onSubmit={handleSubmit} className="p-3 space-y-2">
        {/* Input */}
        <div className="relative">
          <Input
            ref={inputRef}
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="text-sm border-0 shadow-none focus-visible:ring-0 focus-visible:ring-offset-0 p-0 h-auto bg-transparent"
            disabled={isLoading}
            maxLength={255}
          />
          
          {/* Character count */}
          {title.length > 200 && (
            <div className="absolute -top-5 right-0 text-xs text-muted-foreground">
              {title.length}/255
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end gap-1">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="h-7 w-7 p-0 hover:bg-gray-100"
            onClick={handleCancel}
            disabled={isLoading}
          >
            <X className="h-3 w-3" />
          </Button>
          
          <Button
            type="submit"
            size="sm"
            className={cn(
              "h-7 gap-1.5 px-3 transition-all duration-200",
              canSubmit 
                ? "bg-primary hover:bg-primary/90 text-primary-foreground" 
                : "bg-muted text-muted-foreground cursor-not-allowed"
            )}
            disabled={!canSubmit}
          >
            {isLoading ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <Check className="h-3 w-3" />
            )}
            <span className="text-xs font-medium">
              {isLoading 
                ? (isEditing ? 'Saving...' : 'Creating...') 
                : (isEditing ? 'Save' : 'Create')
              }
            </span>
          </Button>
        </div>
      </form>
    </div>
  );
} 