"use client"

import { useTaskStore } from "@/store/taskStore"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, <PERSON>atterChart, CartesianGrid, XAxis, YAxis, ZAxis, Tooltip, ResponsiveContainer, Cell } from "recharts"
import { useState } from "react"

interface ScatterDataPoint {
  id: string
  title: string
  x: number
  y: number
  z: number
  index: number
}

interface CustomTooltipProps {
  active?: boolean
  payload?: Array<{
    payload: ScatterDataPoint
  }>
}

export function TaskPomodoroRelationship() {
  const { tasks } = useTaskStore()
  const [activeIndex, setActiveIndex] = useState<number | null>(null)

  // Filter completed tasks with pomodoro data
  const completedTasks = tasks.filter(
    (task) => task.status === "completed" && task.pomodoroCount > 0 && task.pomodoroEstimate > 0,
  )

  // Prepare data for scatter plot
  const scatterData = completedTasks.map((task, index) => ({
    id: task.id,
    title: task.title,
    x: task.pomodoroEstimate, // Estimated pomodoros
    y: task.pomodoroCount, // Actual pomodoros
    z: 10, // Size of dot
    index,
  }))

  // Count tasks that were under/over estimated
  const underestimated = completedTasks.filter((task) => task.pomodoroCount > task.pomodoroEstimate).length
  const overestimated = completedTasks.filter((task) => task.pomodoroCount < task.pomodoroEstimate).length
  const perfectlyEstimated = completedTasks.filter((task) => task.pomodoroCount === task.pomodoroEstimate).length

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      const estimationAccuracy = Math.round((data.y / data.x) * 100)

      let accuracyText = "Perfectly estimated"
      let accuracyColor = "text-emerald-500"

      if (data.y > data.x) {
        accuracyText = "Underestimated"
        accuracyColor = "text-primary"
      } else if (data.y < data.x) {
        accuracyText = "Overestimated"
        accuracyColor = "text-amber-500"
      }

      return (
        <div className="rounded-md border border-border bg-card p-3 shadow-md">
          <p className="mb-1 font-medium truncate max-w-[200px]">{data.title}</p>
          <p className="text-muted-foreground">
            <span className="font-medium">Estimated: </span>
            <span>{data.x} pomodoros</span>
          </p>
          <p className="text-muted-foreground">
            <span className="font-medium">Actual: </span>
            <span>{data.y} pomodoros</span>
          </p>
          <p className={`mt-1 font-medium ${accuracyColor}`}>
            {accuracyText} ({estimationAccuracy}%)
          </p>
        </div>
      )
    }
    return null
  }

  // Get color based on estimation accuracy
  const getDotColor = (x: number, y: number) => {
    if (y === x) return "#3A6CF4" // Perfect
    if (y > x) return "#5576F5" // Underestimated
    return "#F48B3A" // Overestimated
  }

  return (
    <Card className="border-border bg-card/50 shadow-lg">
      <CardHeader>
        <CardTitle>Estimation Accuracy</CardTitle>
        <CardDescription>Estimated vs. actual pomodoros</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-4 grid grid-cols-3 gap-2 rounded-md bg-accent/30 p-3">
          <div className="text-center">
            <p className="text-xs text-muted-foreground">Underestimated</p>
            <p className="text-lg font-bold text-primary">{underestimated}</p>
            <p className="text-xs text-muted-foreground/70">tasks</p>
          </div>
          <div className="text-center">
            <p className="text-xs text-muted-foreground">Perfect</p>
            <p className="text-lg font-bold text-emerald-500">{perfectlyEstimated}</p>
            <p className="text-xs text-muted-foreground/70">tasks</p>
          </div>
          <div className="text-center">
            <p className="text-xs text-muted-foreground">Overestimated</p>
            <p className="text-lg font-bold text-amber-500">{overestimated}</p>
            <p className="text-xs text-muted-foreground/70">tasks</p>
          </div>
        </div>

        <div className="h-[300px] w-full">
          {scatterData.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                <XAxis
                  type="number"
                  dataKey="x"
                  name="Estimated"
                  stroke="currentColor"
                  className="text-muted-foreground"
                  tickLine={false}
                  axisLine={false}
                  label={{
                    value: "Estimated Pomodoros",
                    position: "bottom",
                    offset: 0,
                    fill: "currentColor",
                    className: "text-muted-foreground",
                    fontSize: 12,
                  }}
                />
                <YAxis
                  type="number"
                  dataKey="y"
                  name="Actual"
                  stroke="currentColor"
                  className="text-muted-foreground"
                  tickLine={false}
                  axisLine={false}
                  label={{
                    value: "Actual Pomodoros",
                    angle: -90,
                    position: "left",
                    fill: "currentColor",
                    className: "text-muted-foreground",
                    fontSize: 12,
                  }}
                />
                <ZAxis type="number" dataKey="z" range={[60, 60]} />
                <Tooltip content={<CustomTooltip />} cursor={{ strokeDasharray: "3 3" }} />
                <Scatter
                  name="Tasks"
                  data={scatterData}
                  fill="#8884d8"
                  onMouseOver={(data: ScatterDataPoint) => setActiveIndex(data.index)}
                  onMouseLeave={() => setActiveIndex(null)}
                >
                  {scatterData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={getDotColor(entry.x, entry.y)}
                      fillOpacity={activeIndex === index ? 1 : 0.7}
                      stroke={activeIndex === index ? "white" : "none"}
                      strokeWidth={activeIndex === index ? 1 : 0}
                    />
                  ))}
                </Scatter>
                {/* Reference line for perfect estimation */}
                <line
                  x1={0}
                  y1={0}
                  x2={Math.max(...scatterData.map((d) => Math.max(d.x, d.y))) + 1}
                  y2={Math.max(...scatterData.map((d) => Math.max(d.x, d.y))) + 1}
                  stroke="#D1D5DB"
                  strokeDasharray="3 3"
                  strokeWidth={1}
                />
              </ScatterChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex h-full items-center justify-center">
              <p className="text-muted-foreground">Not enough completed tasks with pomodoro data</p>
            </div>
          )}
        </div>

        <div className="mt-4 flex items-center justify-center gap-6">
          <div className="flex items-center">
            <div className="mr-2 h-3 w-3 rounded-full" style={{ backgroundColor: "#5576F5" }}></div>
            <span className="text-xs text-muted-foreground">Underestimated</span>
          </div>
          <div className="flex items-center">
            <div className="mr-2 h-3 w-3 rounded-full" style={{ backgroundColor: "#3A6CF4" }}></div>
            <span className="text-xs text-muted-foreground">Perfect</span>
          </div>
          <div className="flex items-center">
            <div className="mr-2 h-3 w-3 rounded-full" style={{ backgroundColor: "#F48B3A" }}></div>
            <span className="text-xs text-muted-foreground">Overestimated</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
