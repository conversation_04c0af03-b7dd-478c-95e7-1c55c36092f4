'use client';

import { HeaderSettingsSummary } from '@/components/settings/header-settings-summary';
import { usePomodoroStore, Video } from '@/lib/pomodoro-store';
import { VideoPreview } from '@/app/(main)/_components/video-preview';
import { useState, useEffect } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { CountdownDisplay } from './countdown-display';
import { motion } from 'framer-motion';
import { Sparkles } from 'lucide-react';
import { ErrorBoundary } from '@/components/error-boundary';

interface HeroSectionProps {
  videos: Video[];
}

const HeroSectionComponent = ({ videos }: HeroSectionProps) => {
  const { selectedVideo, timerSettings, setVideos } = usePomodoroStore();
  const [isMounted, setIsMounted] = useState(false);

  // Update the store when videos are loaded
  useEffect(() => {
    try {
      if (videos && videos.length > 0) {
        setVideos(videos);
        // console.log('videos', videos);
        // Choose between remembered audios or video's music track
      }
    } catch (error) {
      console.error('Error setting videos in store:', error);
    }
  }, [videos, setVideos]);

  // Use a mount effect to prevent hydration issues
  useEffect(() => {
    try {
      setIsMounted(true);
    } catch (error) {
      console.error('Error setting mounted state:', error);
    }
  }, []);

  // Extract a default video to show before user selection
  const defaultVideo = videos && videos.length > 0 ? videos[0] : null;
  const videoToDisplay = isMounted ? selectedVideo || defaultVideo : null;

  return (
    <section className="w-full py-4 md:py-12 bg-gradient-to-b from-primary/5 via-background/100 to-background border-b border-muted/30 dark:from-primary/10 dark:via-slate-950/100 dark:to-slate-950">
      <div className="container mx-auto px-2 md:px-4 max-w-7xl">
        <div className="mb-3 md:mb-6 flex flex-col md:flex-row items-center gap-2 md:gap-4">
          <div className="w-full md:w-3/5 relative px-1 md:px-0">
            {/* Decorative background glow */}
            <div className="absolute -inset-3 bg-gradient-to-r from-orange-500/10 via-red-500/5 to-rose-500/10 rounded-xl blur-xl opacity-60 dark:opacity-40" />

            {/* Main header content */}
            <div className="relative">
              <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center justify-center md:justify-start gap-2.5 mb-2"
              >

                <h1
                  className="text-4xl md:text-4xl font-bold tracking-tight relative"
                  style={{
                    fontFamily: 'var(--font-geist-sans)',
                    letterSpacing: '-0.025em',
                  }}
                >
                  <span className="relative inline-block">
                    <span
                      className="bg-clip-text text-transparent bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 dark:from-slate-100 dark:via-slate-200 dark:to-slate-300 font-semibold"
                      style={{
                        textShadow: '0 0 20px rgba(251, 146, 60, 0.15)',
                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                      }}
                    >
                      Pomodoro
                    </span>
                  </span>{' '}
                  <span className="relative inline-block">
                    <span
                      className="bg-clip-text text-transparent bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 font-extrabold"
                      style={{
                        textShadow: '0 0 25px rgba(251, 146, 60, 0.3)',
                        filter: 'drop-shadow(0 2px 6px rgba(251, 146, 60, 0.2))'
                      }}
                    >
                      365
                    </span>
                    {/* Subtle sparkle effect */}
                    <motion.div
                      className="absolute -top-1 -right-1"
                      animate={{
                        opacity: [0, 0.8, 0],
                        scale: [0.8, 1.1, 0.8],
                        rotate: [0, 180, 360]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <Sparkles className="h-3.5 w-3.5 text-orange-400/80" />
                    </motion.div>
                  </span>
                </h1>
              </motion.div>

              {/* Decorative underline */}
              <motion.div
                animate={{ width: "100%" }}
                className="h-0.5 mb-2 max-w-sm mx-auto md:mx-0 bg-gradient-to-r from-transparent via-orange-500/60 to-transparent md:from-orange-500/60 md:via-red-500/40 md:to-transparent"
              />

              <motion.p
                animate={{ opacity: 1, y: 0 }}
                className="text-slate-600 dark:text-slate-300 max-w-xl text-sm md:text-base leading-snug"
                style={{ fontFamily: 'var(--font-geist-sans)' }}
              >
                Boost your focus with stunning backgrounds and captivating music.
              </motion.p>
            </div>
          </div>

          <div className="hidden w-full md:w-2/5 md:flex justify-center md:justify-end mt-1">
            <CountdownDisplay
              initialMinutes={timerSettings.pomodoroMinutes}
            />
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-3 md:gap-8 items-stretch">
          {/* Left Column - Timer Settings */}
          <div className="w-full md:w-[45%] flex flex-col px-1 md:px-0">
              <HeaderSettingsSummary
                pomodoroMinutes={timerSettings.pomodoroMinutes}
                shortBreakMinutes={timerSettings.shortBreakMinutes}
                longBreakMinutes={timerSettings.longBreakMinutes}
                sessionsBeforeLongBreak={timerSettings.sessionsBeforeLongBreak}
                className="h-full"
              />
          </div>

          {/* Right Column - Video Preview */}
          <div className="w-full md:w-[55%] min-h-[200px] md:min-h-[300px] relative px-1 md:px-0">
            {/* Fixed size container to prevent layout shift */}
            <div className="w-full h-full aspect-video rounded-lg overflow-hidden">
              {isMounted && videoToDisplay ? (
                <VideoPreview video={videoToDisplay} />
              ) : isMounted && !videoToDisplay ? (
                <div className="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-lg">
                  <p className="text-slate-600 dark:text-slate-300 text-sm px-4 text-center">
                    {videos && videos.length > 0
                      ? 'Select a video to get started'
                      : 'No videos available. Check back soon!'}
                  </p>
                </div>
              ) : (
                <Skeleton className="w-full h-full aspect-video rounded-lg" />
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

// Wrap with error boundary for iOS safety
export const HeroSection = ({ videos }: HeroSectionProps) => {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('HeroSection error:', {
          error: error.message,
          componentStack: errorInfo.componentStack,
          videosCount: videos?.length || 0,
          userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'unknown'
        });
      }}
    >
      <HeroSectionComponent videos={videos} />
    </ErrorBoundary>
  );
};