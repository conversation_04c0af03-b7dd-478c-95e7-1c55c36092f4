'use client';

import { Volume2, VolumeX, <PERSON>, Bell } from 'lucide-react';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Badge } from "@/components/ui/badge";
// import { AudioTabProps } from './types';

export function AudioTab() {
  // Common section class for consistent spacing and styling
  const sectionClass = "space-y-1 px-4 mb-4";
  const headerClass = "flex items-center justify-between mb-1";
  const titleClass = "text-xs font-medium flex items-center gap-1.5";
  const descriptionClass = "text-xs text-muted-foreground leading-tight";

  // This is a placeholder component - in a real implementation, these would be connected to state
  return (
    <div className="space-y-4">
      {/* Sound On/Off Switch */}
      <div className={sectionClass}>
        <div className={headerClass}>
          <h3 className={titleClass}>
            <Volume2 className="h-3.5 w-3.5 text-emerald-500" />
            <span>Enable Sound</span>
          </h3>
          <Switch 
            checked={true}
            className="data-[state=checked]:bg-emerald-600"
          />
        </div>
        <p className={descriptionClass}>
          Play sound notifications when timer completes
        </p>
      </div>
      
      {/* Volume Control */}
      <div className={sectionClass}>
        <div className={headerClass}>
          <h3 className={titleClass}>
            <Music className="h-3.5 w-3.5 text-emerald-500" />
            <span>Volume</span>
          </h3>
          <Badge variant="outline" className="h-5 px-1.5 text-[10px] font-normal">
            80%
          </Badge>
        </div>
        <div className="flex items-center gap-3 mt-1">
          <VolumeX className="h-3.5 w-3.5 text-muted-foreground" />
          <Slider
            value={[80]}
            min={0}
            max={100}
            step={5}
            className="flex-1"
          />
          <Volume2 className="h-3.5 w-3.5 text-muted-foreground" />
        </div>
      </div>
      
      {/* Coming Soon Message */}
      <div className="px-4 mt-8">
        <div className="p-4 rounded-lg bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-800/30 flex flex-col items-center gap-2">
          <Bell className="h-8 w-8 text-emerald-500 opacity-70" />
          <div className="text-center">
            <h4 className="text-xs font-medium text-emerald-700 dark:text-emerald-300">Sound Customization</h4>
            <p className="text-xs text-emerald-600/70 dark:text-emerald-400/70 mt-1">
              Custom sound selection coming soon
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
