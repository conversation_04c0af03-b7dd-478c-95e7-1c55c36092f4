// MusicPlaylistUser.prisma - For user-created playlists
model MusicPlaylistUser {
    id          String  @id @default(cuid())
    name        String /// @zod.custom.use(z.string().min(1))
    description String? @db.Text
    imageUrl    String? /// @zod.custom.use(z.union([z.instanceof(File), z.string().nullable()]))
    isPublic    Boolean @default(false)

    // User who created this playlist
    userId String
    user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

    // Relationship with music tracks (many-to-many)
    musics Music[]

    // Relationship with natural sounds (many-to-many)
    natureSounds NatureSound[]

    // Relationship with videos
    videos Video[]

    // Store the order of tracks in the playlist
    musicOrder       String[]
    // Store the order of natural sounds in the playlist
    natureSoundOrder String[]

    createdAt DateTime @default(now()) /// @zod.custom.use(z.union([z.date(), z.string().datetime()]))
    updatedAt DateTime @updatedAt /// @zod.custom.use(z.union([z.date(), z.string().datetime()]))

    @@index([userId])
    @@index([isPublic])
}
