'use client';

import { useEffect, useState } from 'react';
import { runIOSCompatibilityTests, logCompatibilityReport, type CompatibilityReport } from '@/lib/ios-compatibility-test';
import { getDeviceInfo } from '@/lib/ios-safe-video';

/**
 * iOS Compatibility Checker Component
 * Runs compatibility tests on iOS devices and logs results
 */
export const IOSCompatibilityChecker = () => {
  const [hasRun, setHasRun] = useState(false);

  useEffect(() => {
    // Only run once and only on client side
    if (hasRun || typeof window === 'undefined') return;

    const deviceInfo = getDeviceInfo();
    
    // Run tests immediately on iOS devices, or after a delay on other devices
    const shouldRunTests = deviceInfo.isIOS || process.env.NODE_ENV === 'development';
    
    if (shouldRunTests) {
      const runTests = async () => {
        try {
          console.log('🔍 Running iOS compatibility tests...');
          const report = await runIOSCompatibilityTests();
          logCompatibilityReport(report);
          
          // Send report to analytics if available
          if (typeof window !== 'undefined' && (window as any).gtag) {
            (window as any).gtag('event', 'ios_compatibility_check', {
              event_category: 'Performance',
              event_label: deviceInfo.isIOS ? 'iOS' : 'Other',
              value: report.overallScore,
              custom_map: {
                device_info: JSON.stringify(deviceInfo),
                failed_tests: report.tests.filter(t => !t.passed).map(t => t.test).join(','),
                recommendations_count: report.recommendations.length
              }
            });
          }
          
          setHasRun(true);
        } catch (error) {
          console.error('Error running iOS compatibility tests:', error);
          setHasRun(true);
        }
      };

      // Run tests after a short delay to not block initial render
      const delay = deviceInfo.isIOS ? 1000 : 3000;
      setTimeout(runTests, delay);
    } else {
      setHasRun(true);
    }
  }, [hasRun]);

  // This component doesn't render anything visible
  return null;
};
