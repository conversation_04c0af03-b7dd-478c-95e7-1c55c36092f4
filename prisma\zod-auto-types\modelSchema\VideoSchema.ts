import { z } from 'zod';
import { UserRoleSchema } from '../inputTypeSchemas/UserRoleSchema'
import { VideoGenreSchema } from '../inputTypeSchemas/VideoGenreSchema'
import { UserWithRelationsSchema, UserPartialWithRelationsSchema } from './UserSchema'
import type { UserWithRelations, UserPartialWithRelations } from './UserSchema'
import { MusicPlaylistWithRelationsSchema, MusicPlaylistPartialWithRelationsSchema } from './MusicPlaylistSchema'
import type { MusicPlaylistWithRelations, MusicPlaylistPartialWithRelations } from './MusicPlaylistSchema'
import { MusicPlaylistUserWithRelationsSchema, MusicPlaylistUserPartialWithRelationsSchema } from './MusicPlaylistUserSchema'
import type { MusicPlaylistUserWithRelations, MusicPlaylistUserPartialWithRelations } from './MusicPlaylistUserSchema'
import { NaturePlaylistWithRelationsSchema, NaturePlaylistPartialWithRelationsSchema } from './NaturePlaylistSchema'
import type { NaturePlaylistWithRelations, NaturePlaylistPartialWithRelations } from './NaturePlaylistSchema'
import { FavoriteVideoWithRelationsSchema, FavoriteVideoPartialWithRelationsSchema } from './FavoriteVideoSchema'
import type { FavoriteVideoWithRelations, FavoriteVideoPartialWithRelations } from './FavoriteVideoSchema'

/////////////////////////////////////////
// VIDEO SCHEMA
/////////////////////////////////////////

export const VideoSchema = z.object({
  creatorType: UserRoleSchema,
  videoGenre: VideoGenreSchema.array(),
  id: z.string().cuid(),
  title: z.string().min(1),
  src: z.string().url(),
  thumbnail: z.string().url(),
  description: z.string().nullish(),
  isPublic: z.boolean(),
  userId: z.string().nullish(),
  musicPlaylistId: z.string().nullish(),
  naturePlaylistId: z.string().nullish(),
  order: z.number().int(),
  isPremium: z.boolean(),
  createdAt: z.union([z.date(), z.string().datetime()]),
  updatedAt: z.union([z.date(), z.string().datetime()]),
})

export type Video = z.infer<typeof VideoSchema>

/////////////////////////////////////////
// VIDEO PARTIAL SCHEMA
/////////////////////////////////////////

export const VideoPartialSchema = VideoSchema.partial()

export type VideoPartial = z.infer<typeof VideoPartialSchema>

/////////////////////////////////////////
// VIDEO RELATION SCHEMA
/////////////////////////////////////////

export type VideoRelations = {
  user?: UserWithRelations | null;
  musicPlaylist?: MusicPlaylistWithRelations | null;
  musicPlaylistsUser: MusicPlaylistUserWithRelations[];
  naturePlaylist?: NaturePlaylistWithRelations | null;
  favoriteBy: FavoriteVideoWithRelations[];
};

export type VideoWithRelations = z.infer<typeof VideoSchema> & VideoRelations

export const VideoWithRelationsSchema: z.ZodType<VideoWithRelations> = VideoSchema.merge(z.object({
  user: z.lazy(() => UserWithRelationsSchema).nullish(),
  musicPlaylist: z.lazy(() => MusicPlaylistWithRelationsSchema).nullish(),
  musicPlaylistsUser: z.lazy(() => MusicPlaylistUserWithRelationsSchema).array(),
  naturePlaylist: z.lazy(() => NaturePlaylistWithRelationsSchema).nullish(),
  favoriteBy: z.lazy(() => FavoriteVideoWithRelationsSchema).array(),
}))

/////////////////////////////////////////
// VIDEO PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type VideoPartialRelations = {
  user?: UserPartialWithRelations | null;
  musicPlaylist?: MusicPlaylistPartialWithRelations | null;
  musicPlaylistsUser?: MusicPlaylistUserPartialWithRelations[];
  naturePlaylist?: NaturePlaylistPartialWithRelations | null;
  favoriteBy?: FavoriteVideoPartialWithRelations[];
};

export type VideoPartialWithRelations = z.infer<typeof VideoPartialSchema> & VideoPartialRelations

export const VideoPartialWithRelationsSchema: z.ZodType<VideoPartialWithRelations> = VideoPartialSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema).nullish(),
  musicPlaylist: z.lazy(() => MusicPlaylistPartialWithRelationsSchema).nullish(),
  musicPlaylistsUser: z.lazy(() => MusicPlaylistUserPartialWithRelationsSchema).array(),
  naturePlaylist: z.lazy(() => NaturePlaylistPartialWithRelationsSchema).nullish(),
  favoriteBy: z.lazy(() => FavoriteVideoPartialWithRelationsSchema).array(),
})).partial()

export type VideoWithPartialRelations = z.infer<typeof VideoSchema> & VideoPartialRelations

export const VideoWithPartialRelationsSchema: z.ZodType<VideoWithPartialRelations> = VideoSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema).nullish(),
  musicPlaylist: z.lazy(() => MusicPlaylistPartialWithRelationsSchema).nullish(),
  musicPlaylistsUser: z.lazy(() => MusicPlaylistUserPartialWithRelationsSchema).array(),
  naturePlaylist: z.lazy(() => NaturePlaylistPartialWithRelationsSchema).nullish(),
  favoriteBy: z.lazy(() => FavoriteVideoPartialWithRelationsSchema).array(),
}).partial())

export default VideoSchema;
