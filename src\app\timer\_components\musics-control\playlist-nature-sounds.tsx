import React, { useState, useEffect } from 'react';
import { NatureSoundPlayer } from './types';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { PlayCircle, PauseCircle, Leaf } from 'lucide-react';
import { getNatureSoundVolumeIcon } from './utils';
import { cn } from '@/lib/utils';

interface PlaylistNatureSoundsProps {
  players: NatureSoundPlayer[];
  playingCount: number;
  onToggleSound: (id: string) => void;
  onVolumeChange: (id: string, value: number[]) => void;
  onToggleMute: (id: string) => void;
  className?: string;
}

export function PlaylistNatureSounds({
  players,
  playingCount,
  onToggleSound,
  onVolumeChange,
  onToggleMute,
  className
}: PlaylistNatureSoundsProps) {
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile/tablet devices
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  if (players.length === 0) {
    return null;
  }

  return (
    <div className={cn(
      "border-t border-border/30",
      isMobile ? "mt-2 pt-3" : "mt-1 pt-2",
      className
    )}>
      {/* Header */}
      <div className={cn(
        "flex items-center gap-2 mb-2",
        isMobile ? "px-1" : "px-0.5"
      )}>
        <Leaf size={isMobile ? 18 : 16} className="text-emerald-500 flex-shrink-0" />
        <span className={cn(
          "font-medium",
          isMobile ? "text-base" : "text-sm"
        )}>
          Nature Sounds
        </span>
        {playingCount > 0 && (
          <span className="font-medium text-xs text-emerald-600">
            ({playingCount})
          </span>
        )}
      </div>

      {/* Nature Sounds List */}
      <div className={cn(
        "space-y-1 max-h-48 overflow-y-auto touch-pan-y",
        isMobile ? "mt-1" : "mt-0.5"
      )} style={{ WebkitOverflowScrolling: 'touch' }}>
        {players.map(player => {
          const VolumeIcon = getNatureSoundVolumeIcon(player);

          return (
            <div
              key={player.id}
              className={cn(
                "flex items-center justify-between gap-2 rounded-lg",
                "transition-colors duration-200",
                isMobile ? "py-1.5 px-1 bg-muted/20" : "py-0.5 px-0.5"
              )}
            >
              {/* Left side: Play button and title */}
              <div className="flex items-center gap-2 min-w-0">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onToggleSound(player.id)}
                  className={cn(
                    "rounded-full shrink-0 transition-all duration-200 border",
                    "transition-transform duration-200 hover:scale-105 active:scale-95",
                    "shadow-sm touch-manipulation",
                    isMobile ? "h-5 w-5 p-0" : "h-5 w-5 p-0",
                    player.isPlaying
                      ? "text-emerald-600 border-emerald-200 bg-emerald-50 hover:bg-emerald-100"
                      : "text-muted-foreground border-muted-foreground/20 bg-background hover:bg-muted/50"
                  )}
                  aria-label={player.isPlaying ? `Pause ${player.title}` : `Play ${player.title}`}
                >
                  {player.isPlaying ? (
                    <PauseCircle size={isMobile ? 12 : 10} />
                  ) : (
                    <PlayCircle size={isMobile ? 12 : 10} />
                  )}
                </Button>

                <span className={cn(
                  "font-medium truncate text-xs",
                  isMobile ? "flex-shrink-0 w-32" : "w-28 flex-shrink-0"
                )}>
                  {player.title}
                </span>
              </div>

              {/* Right side: Volume controls */}
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onToggleMute(player.id)}
                  className={cn(
                    "rounded-full shrink-0 transition-colors duration-200",
                    "focus:outline-none focus:ring-2 focus:ring-emerald-500/20",
                    "touch-manipulation text-emerald-600 hover:text-emerald-700",
                    isMobile ? "h-6 w-6 p-0" : "h-4 w-4 p-0",
                    player.isMuted && "text-emerald-400/50"
                  )}
                  aria-label={player.isMuted ? `Unmute ${player.title}` : `Mute ${player.title}`}
                >
                  <VolumeIcon size={isMobile ? 12 : 9} />
                </Button>

                <div className={cn(
                  "min-w-0",
                  isMobile ? "w-24" : "w-20"
                )}>
                  <Slider
                    value={[player.volume]}
                    min={0}
                    max={100}
                    step={1}
                    onValueChange={(value) => onVolumeChange(player.id, value)}
                    className={cn(
                      "w-full touch-manipulation",
                      player.isMuted && "opacity-40",
                      "[&_[data-slot=slider-range]]:bg-emerald-500",
                      "[&_[data-slot=slider-track]]:bg-muted",
                      "[&_[data-slot=slider-thumb]]:bg-emerald-500 [&_[data-slot=slider-thumb]]:border-emerald-500",
                      isMobile ? [
                        "[&_[data-slot=slider-track]]:h-[4px]",
                        "[&_[data-slot=slider-thumb]]:h-3 [&_[data-slot=slider-thumb]]:w-3"
                      ] : [
                        "[&_[data-slot=slider-track]]:h-[3px]",
                        "[&_[data-slot=slider-thumb]]:h-2.5 [&_[data-slot=slider-thumb]]:w-2.5"
                      ]
                    )}
                    aria-label={`${player.title} volume`}
                  />
                </div>

                <span className={cn(
                  "text-right text-muted-foreground font-mono text-xs shrink-0",
                  isMobile ? "w-6" : "w-5"
                )}>
                  {player.volume}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
