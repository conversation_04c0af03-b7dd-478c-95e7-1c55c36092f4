# Pomodoro Stats Architecture

## Overview
This document outlines the distinction between quick stats (popover) and comprehensive stats (dashboard) to ensure optimal performance and data consistency.

## Architecture

### 1. Quick Stats Route (`/quick-stats`)
**Purpose**: Lightweight stats for the timer popover
**Endpoint**: `GET /api/pomodoro/quick-stats`
**Use Case**: Quick glance at today's progress

#### Data Returned:
```typescript
interface PomodoroQuickStatsResponse {
  todayFocusTime: number; // in minutes
  todayCompletedSessions: number;
  todaySessions: Array<{
    id: string;
    title: string;
    duration: number; // in minutes
    completed: boolean;
    interrupted: boolean;
    timeRange: string;
    type: string;
    startTime: string;
    endTime: string;
  }>;
  weekTotalDuration: number; // in hours
  weekCompletionRate: number; // percentage
  dateRange: {
    today: string;
    weekStart: string;
  };
}
```

#### Optimizations:
- Only queries today's sessions + week totals
- Pre-calculated metrics (no complex aggregations)
- Lightweight response structure
- 2-minute stale time for quick updates
- Focused on essential metrics only

### 2. Comprehensive Stats Route (`/stats`)
**Purpose**: Detailed analytics for dashboard
**Endpoint**: `GET /api/pomodoro/stats`
**Use Case**: In-depth analysis and charts

#### Data Returned:
```typescript
interface PomodoroStatsResponse {
  // Basic metrics
  totalSessions: number;
  completedSessions: number;
  interruptedSessions: number;
  totalDuration: number;
  completionRate: number;
  
  // Session breakdowns
  focusSessions: number;
  shortBreakSessions: number;
  longBreakSessions: number;
  focusDuration: number;
  shortBreakDuration: number;
  longBreakDuration: number;
  
  // Advanced analytics
  sessionsByDay: { [date: string]: { count: number; focusMinutes: number } };
  todaySessions: Array<SessionDetail>;
  streakData: Array<{ date: string; count: number }>;
  hourlyDistribution: Array<{ hour: number; value: number }>;
  weeklyComparison: Array<{ name: string; thisWeek: number; lastWeek: number }>;
  dateRange: { startDate: string; endDate: string };
}
```

#### Features:
- Supports date range queries (days, specific date, custom range)
- Complex aggregations for charts
- Historical data analysis
- Detailed session breakdowns
- Longer stale time for comprehensive data

## Frontend Usage

### Stats Popover
```typescript
// Uses quick stats hook
const { data: quickStatsData, isLoading, error } = useGetPomodoroQuickStats();

// Direct access to pre-calculated values
const todayFocusTime = quickStatsData?.todayFocusTime || 0; // minutes
const todayCompletedSessions = quickStatsData?.todayCompletedSessions || 0;
```

### Dashboard
```typescript
// Uses comprehensive stats hook
const { data: statsData, isLoading, error } = useGetPomodoroStats(30, specificDate, dateRange);

// Complex data transformations for charts
const transformedData = transformApiDataToUiFormat(statsData);
```

## Data Consistency

### Cache Invalidation
Both endpoints are invalidated together when sessions are created/updated/deleted:

```typescript
// On session mutations
queryClient.invalidateQueries({ queryKey: ["pomodoro", "quick-stats"] });
queryClient.invalidateQueries({ queryKey: ["pomodoro", "stats"] });
```

### Duration Calculations
Both endpoints use consistent logic:
- `Math.ceil(duration / 60)` for minute conversion (ensures short sessions count)
- `focusDuration || totalDuration` for focus sessions
- `breakDuration || totalDuration` for break sessions

### Data Sources
- **Quick Stats**: Direct database queries with minimal processing
- **Comprehensive Stats**: Complex aggregations with historical analysis

## Performance Benefits

1. **Reduced Load**: Popover doesn't load heavy dashboard data
2. **Faster Response**: Quick stats optimized for speed
3. **Better Caching**: Different cache strategies for different use cases
4. **Scalability**: Separate endpoints can be optimized independently

## Migration Notes

- Stats popover now uses `useGetPomodoroQuickStats()` instead of `useGetPomodoroStats()`
- Data format changed from seconds to minutes for focus time
- Removed dependency on `sessionsByDay` calculation in frontend
- Backend pre-calculates all metrics for better performance
