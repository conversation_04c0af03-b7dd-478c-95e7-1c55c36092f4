import { z } from 'zod';

export const MusicGenreSchema = z.enum(['PIAN<PERSON>','GUITA<PERSON>','RELAX','HAPPY','MEDITATION','LOFI','SAD','JOYFUL','CALM','ENERGETIC','ACOUSTI<PERSON>','COUNTRY','<PERSON><PERSON>CT<PERSON><PERSON><PERSON>','INDIE','AMBIENT','CHILL','STUDY','MYSTERIOUS','MOTIVATIONAL']);

export type MusicGenreType = `${z.infer<typeof MusicGenreSchema>}`

export default MusicGenreSchema;
