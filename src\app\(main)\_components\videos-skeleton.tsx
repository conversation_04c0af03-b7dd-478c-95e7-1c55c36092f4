import { Skeleton } from "@/components/ui/skeleton";

interface VideosSkeletonProps {
  type: "hero" | "grid";
}

export const VideosSkeleton = ({ type }: VideosSkeletonProps) => {
  if (type === "hero") {
    return (
      <section className="w-full py-10 md:py-12 bg-gradient-to-b from-primary/5 via-background/100 to-background border-b border-muted/30 dark:from-primary/10 dark:via-slate-950/100 dark:to-slate-950">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="mb-6">
            <Skeleton className="h-12 w-72 mb-2" />
            <Skeleton className="h-6 w-96" />
          </div>
          
          <div className="flex flex-col md:flex-row gap-6 md:gap-8 items-stretch">
            {/* Left Column - Timer Settings Skeleton */}
            <div className="w-full md:w-[45%] flex flex-col">
              <div className="border rounded-lg p-4 h-full">
                <Skeleton className="h-8 w-3/4 mb-4" />
                <div className="space-y-3">
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-6 w-4/5" />
                </div>
              </div>
            </div>
            
            {/* Right Column - Video Preview Skeleton */}
            <div className="w-full md:w-[55%] min-h-[300px]">
              <Skeleton className="w-full h-full aspect-video rounded-lg" />
            </div>
          </div>
        </div>
      </section>
    );
  }
  
  return (
    <section className="w-full py-12 md:py-16">
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="text-center mb-10">
          <Skeleton className="h-10 w-72 mx-auto mb-4" />
          <Skeleton className="h-6 w-96 mx-auto" />
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array(8).fill(0).map((_, index) => (
            <div key={index} className="flex flex-col space-y-3">
              <Skeleton className="aspect-video rounded-lg w-full" />
              <Skeleton className="h-5 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}; 