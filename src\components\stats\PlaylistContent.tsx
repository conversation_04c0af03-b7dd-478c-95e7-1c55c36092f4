"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Music, PlayCircle, Plus, Clock, Headphones, ListMusic, Volume2 } from "lucide-react"
import { cn } from "@/lib/utils"

interface PlaylistContentProps {
  activeTab: string
}

export function PlaylistContent({ activeTab }: PlaylistContentProps) {
  const renderMyPlaylists = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">My Playlists</h2>
          <p className="text-muted-foreground">Manage your personal music collections</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Create Playlist
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Sample playlists */}
        {[
          { name: "Deep Focus", tracks: 24, duration: "1h 32m", color: "bg-blue-500" },
          { name: "Coding Flow", tracks: 18, duration: "58m", color: "bg-green-500" },
          { name: "Study Session", tracks: 31, duration: "2h 15m", color: "bg-purple-500" },
        ].map((playlist, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-3">
                <div className={cn("h-12 w-12 rounded-lg flex items-center justify-center", playlist.color)}>
                  <Music className="h-6 w-6 text-white" />
                </div>
                <div className="flex-1">
                  <CardTitle className="text-lg">{playlist.name}</CardTitle>
                  <CardDescription>{playlist.tracks} tracks • {playlist.duration}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <Button variant="outline" size="sm" className="w-full">
                <PlayCircle className="h-4 w-4 mr-2" />
                Play
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )

  const renderFocusMusic = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Focus Music</h2>
        <p className="text-muted-foreground">Curated tracks to enhance concentration</p>
      </div>

      <div className="grid gap-6">
        {/* Featured Focus Tracks */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Headphones className="h-5 w-5" />
              Featured Focus Tracks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { title: "Ambient Waves", artist: "Nature Sounds", duration: "45:00", category: "Nature" },
                { title: "Lo-Fi Study Beat", artist: "Chill Collective", duration: "32:15", category: "Lo-Fi" },
                { title: "Classical Focus", artist: "Piano Ensemble", duration: "28:30", category: "Classical" },
                { title: "White Noise", artist: "Sound Therapy", duration: "60:00", category: "Ambient" },
              ].map((track, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-accent/50 transition-colors">
                  <div className="flex items-center gap-3">
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <PlayCircle className="h-4 w-4" />
                    </Button>
                    <div>
                      <p className="font-medium">{track.title}</p>
                      <p className="text-sm text-muted-foreground">{track.artist}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">{track.category}</Badge>
                    <span className="text-sm text-muted-foreground">{track.duration}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )

  const renderAmbientSounds = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Ambient Sounds</h2>
        <p className="text-muted-foreground">Background audio for better focus</p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[
          { name: "Rain Sounds", icon: "🌧️", description: "Gentle rainfall" },
          { name: "Forest Ambience", icon: "🌲", description: "Birds and nature" },
          { name: "Ocean Waves", icon: "🌊", description: "Calming sea sounds" },
          { name: "Coffee Shop", icon: "☕", description: "Café atmosphere" },
          { name: "Fireplace", icon: "🔥", description: "Crackling fire" },
          { name: "Wind Chimes", icon: "🎐", description: "Peaceful chimes" },
        ].map((sound, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6">
              <div className="text-center space-y-3">
                <div className="text-3xl">{sound.icon}</div>
                <div>
                  <h3 className="font-semibold">{sound.name}</h3>
                  <p className="text-sm text-muted-foreground">{sound.description}</p>
                </div>
                <Button variant="outline" size="sm">
                  <Volume2 className="h-4 w-4 mr-2" />
                  Play
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )

  switch (activeTab) {
    case "my-playlists":
      return renderMyPlaylists()
    case "focus-music":
      return renderFocusMusic()
    case "ambient-sounds":
      return renderAmbientSounds()
    default:
      return renderMyPlaylists()
  }
}
