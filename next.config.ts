import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.pexels.com',
      },
      {
        protocol: 'https',
        hostname: 'sunwffry86.ufs.sh',
      },
      {
        protocol: 'https',
        hostname: 'drezk818dh.ufs.sh',
      },
      {
        protocol: 'https',
        hostname: 't3.chat',
      },

      {
        protocol: 'https',
        hostname: 'file.garden',
      },
    ],
  },
};

export default nextConfig;
