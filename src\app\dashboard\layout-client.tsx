"use client"

import { usePathname, useRouter } from "next/navigation"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { EnhancedStatsSidebar } from "@/components/stats/EnhancedStatsSidebar"
import { StatsNavbar } from "@/components/stats/StatsNavbar"
import { GlobalAudioPlayer } from "./playlist/_components/global-audio-player"

interface DashboardLayoutClientProps {
  children: React.ReactNode
}

// Helper function to get page title based on pathname
function getPageInfo(pathname: string) {
  if (pathname.includes('/analytics')) {
    return {
      title: "Focus Analytics",
      showFilters: true,
      showDownload: true
    }
  }
  
  if (pathname.includes('/profile')) {
    return {
      title: "User Profile",
      showFilters: false,
      showDownload: false
    }
  }
  
  if (pathname.includes('/playlist')) {
    return {
      title: "Music & Playlists",
    //   subtitle: "Enhance your focus with audio",
      showFilters: false,
      showDownload: false
    }
  }
  
  if (pathname.includes('/tasks')) {
    return {
      title: "Task Management",
      subtitle: "Organize and track your work",
      showFilters: false,
      showDownload: false
    }
  }

  return {
    title: "Focus Analytics",
    showFilters: false,
    showDownload: false
  }
}

export function DashboardLayoutClient({ children }: DashboardLayoutClientProps) {
  const pathname = usePathname()
  const router = useRouter()
  
  // Map pathnames to tab values for sidebar
  const getActiveTab = (pathname: string) => {
    if (pathname.includes('/analytics')) return "analytics-overview"
    if (pathname.includes('/profile')) return "profile"
    if (pathname.includes('/playlist/musics')) return "musics"
    if (pathname.includes('/playlist/natural-sounds')) return "natural-sounds"
    if (pathname.includes('/playlist')) return "my-playlists"
    if (pathname.includes('/tasks')) return "task-overview"
    return "analytics-overview"
  }

  const handleTabChange = (tab: string) => {
    // Map tab values to routes
    const routeMap: Record<string, string> = {
      "analytics-overview": "/dashboard/analytics",
      "ai-insights": "/dashboard/analytics",
      "profile": "/dashboard/profile",
      "my-playlists": "/dashboard/playlist",
      "musics": "/dashboard/playlist/musics",
      "natural-sounds": "/dashboard/playlist/natural-sounds",
      "task-overview": "/dashboard/tasks",
      "completed-tasks": "/dashboard/tasks",
      "priority-tasks": "/dashboard/tasks"
    }

    const route = routeMap[tab]
    if (route) {
      router.push(route)
    }
  }

  const handleProfileClick = () => {
    router.push("/dashboard/profile")
  }

  const pageInfo = getPageInfo(pathname)
  const activeTab = getActiveTab(pathname)

  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full bg-background overflow-hidden">
        <EnhancedStatsSidebar
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />
        <SidebarInset className="flex-1 overflow-hidden">
          <div className="flex flex-col h-full overflow-hidden">
            {/* Header */}
            <StatsNavbar
              title={pageInfo.title}
              subtitle={pageInfo.subtitle}
              showFilters={pageInfo.showFilters}
              showDownload={pageInfo.showDownload}
              onProfileClick={handleProfileClick}
            />

            {/* Main Content */}
            {children}
          </div>
        </SidebarInset>
      </div>

      {/* Global Audio Player - Rendered at root level for proper fixed positioning */}
      <GlobalAudioPlayer />
    </SidebarProvider>
  )
}