"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useGetDashboardStats } from "@schemas/Admin/admin-query";
import { Music, Leaf, ListMusic, Video, ArrowRight } from "lucide-react";
import { Loader2 } from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface DashboardCardProps {
  title: string;
  description: string;
  count: number | undefined;
  icon: React.ReactNode;
  href: string;
  isLoading: boolean;
  error: Error | null;
  bgColor: string;
  iconColor: string;
  textColor: string;
}

function DashboardCard({
  title,
  description,
  count,
  icon,
  href,
  isLoading,
  error,
  bgColor,
  iconColor,
  textColor
}: DashboardCardProps) {
  return (
    <Card className={cn("overflow-hidden transition-all hover:shadow-md", bgColor)}>
      <Link href={href} className="block h-full">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="space-y-1">
            <CardTitle className={cn("text-xl", textColor)}>{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <div className={cn("rounded-full p-2", iconColor)}>
            {icon}
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center space-x-2">
              <Loader2 className="size-4 animate-spin" />
              <p className="text-xs text-muted-foreground">Loading...</p>
            </div>
          ) : error ? (
            <p className="text-xs text-red-500">Error loading data</p>
          ) : (
            <div className="flex flex-col space-y-1">
              <p className={cn("text-2xl font-bold", textColor)}>{count || 0}</p>
              <div className="flex items-center justify-between">
                <p className="text-xs text-muted-foreground">Manage {title.toLowerCase()}</p>
                <ArrowRight className="size-4 text-muted-foreground" />
              </div>
            </div>
          )}
        </CardContent>
      </Link>
    </Card>
  );
}

export function AdminDashboard() {
  const { data: stats, isLoading, error } = useGetDashboardStats();
  
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
        <p className="text-muted-foreground">
          Manage your content for the pomodoro application
        </p>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <DashboardCard
          title="Musics"
          description="Focus session tracks"
          count={stats?.musicCount}
          icon={<Music className="size-5 text-blue-600" />}
          href="/admin/musics"
          isLoading={isLoading}
          error={error}
          bgColor="bg-blue-50 dark:bg-blue-950/20"
          iconColor="bg-blue-100 dark:bg-blue-900/30"
          textColor="text-blue-600 dark:text-blue-400"
        />
        
        <DashboardCard
          title="Nature Sounds"
          description="Ambient focus sounds"
          count={stats?.natureSoundCount}
          icon={<Leaf className="size-5 text-green-600" />}
          href="/admin/musics"
          isLoading={isLoading}
          error={error}
          bgColor="bg-green-50 dark:bg-green-950/20"
          iconColor="bg-green-100 dark:bg-green-900/30"
          textColor="text-green-600 dark:text-green-400"
        />
        
        <DashboardCard
          title="Music Playlists"
          description="Music collections"
          count={stats?.musicPlaylistCount}
          icon={<ListMusic className="size-5 text-purple-600" />}
          href="/admin/music-playlists"
          isLoading={isLoading}
          error={error}
          bgColor="bg-purple-50 dark:bg-purple-950/20"
          iconColor="bg-purple-100 dark:bg-purple-900/30"
          textColor="text-purple-600 dark:text-purple-400"
        />
        
        <DashboardCard
          title="Nature Playlists"
          description="Nature sound collections"
          count={stats?.naturePlaylistCount}
          icon={<ListMusic className="size-5 text-teal-600" />}
          href="/admin/nature-playlists"
          isLoading={isLoading}
          error={error}
          bgColor="bg-teal-50 dark:bg-teal-950/20"
          iconColor="bg-teal-100 dark:bg-teal-900/30"
          textColor="text-teal-600 dark:text-teal-400"
        />
        
        <DashboardCard
          title="Videos"
          description="Focus background videos"
          count={stats?.videoCount}
          icon={<Video className="size-5 text-orange-500" />}
          href="/admin/videos"
          isLoading={isLoading}
          error={error}
          bgColor="bg-orange-50 dark:bg-orange-950/20"
          iconColor="bg-orange-100 dark:bg-orange-900/30"
          textColor="text-orange-500 dark:text-orange-400"
        />
      </div>
    </div>
  );
} 