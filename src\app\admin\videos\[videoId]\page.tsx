import { Metadata } from "next";
import { VideoClient } from "./components/VideoClient";

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "Video Management",
    description: "Manage and edit video details",
  };
}

export default async function VideoPage({
  params,
}: {
  params: Promise<{ videoId: string }>;
}) {
  const { videoId } = await params;

  // Hidden element to expose playlist data for audio store
  return (
    <>
      <VideoClient videoId={videoId} />
    </>
  );
}
