'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  Download, 
  Smartphone, 
  Monitor,
  Zap,
  Wifi,
  Bell,
  Heart,
  CheckCircle,
  Plus,
  Share,
  MoreHorizontal,
  AlertCircle,
  Loader2,
  X,
  Laptop,
  Tablet,
  Command,
  Grid3X3,
  Home,
  Puzzle
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

type PlatformType = 'ios' | 'android' | 'desktop' | 'unknown';

export const MobileDownloadSection = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [installError, setInstallError] = useState<string | null>(null);
  const [showInstructionsModal, setShowInstructionsModal] = useState(false);
  const [platform, setPlatform] = useState<PlatformType>('unknown');
  const [isClient, setIsClient] = useState(false);

  // Enhanced platform detection
  const detectPlatform = useCallback((): PlatformType => {
    if (typeof window === 'undefined') return 'unknown';
    
    const userAgent = navigator.userAgent.toLowerCase();
    const isIOS = /iphone|ipad|ipod/.test(userAgent);
    const isAndroid = /android/.test(userAgent);
    const isMobile = /mobi|android|touch|tablet|phone/i.test(userAgent);
    const isDesktop = !isMobile && !isIOS && !isAndroid;
    
    if (isIOS) return 'ios';
    if (isAndroid) return 'android';
    if (isDesktop) return 'desktop';
    return 'desktop'; // Default to desktop for better UX if unknown
  }, []);

  // Check if app is already installed (running in standalone mode)
  const checkIfInstalled = useCallback(() => {
    if (typeof window === 'undefined') return false;
    
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true ||
           document.referrer.includes('android-app://');
  }, []);

  useEffect(() => {
    // Set client flag to prevent hydration mismatch
    setIsClient(true);
    
    // Set platform after hydration
    setPlatform(detectPlatform());
    
    // Check if already installed
    setIsInstalled(checkIfInstalled());

    // Listen for install prompt
    const handleBeforeInstallPrompt = (e: Event) => {
      console.log('PWA: Install prompt triggered');
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      setInstallError(null);
    };

    // Listen for successful installation
    const handleAppInstalled = () => {
      console.log('PWA: App installed successfully');
      setIsInstalled(true);
      setIsInstalling(false);
      setDeferredPrompt(null);
      setInstallError(null);
      setShowInstructionsModal(false);
      
      // Analytics - track successful installation
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('event', 'pwa_install_success', {
          'event_category': 'PWA',
          'event_label': detectPlatform(),
        });
      }
    };

    // Listen for visibility change to detect if user came back from install
    const handleVisibilityChange = () => {
      if (!document.hidden && checkIfInstalled() && !isInstalled) {
        handleAppInstalled();
      }
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [detectPlatform, checkIfInstalled, isInstalled]);

  const handleDownloadClick = async () => {
    // If we have a deferred prompt, use it for automatic installation
    if (deferredPrompt) {
      setIsInstalling(true);
      setInstallError(null);

      try {
        console.log('PWA: Triggering install prompt');
        
        // Track install attempt
        if (typeof window !== 'undefined' && (window as any).gtag) {
          (window as any).gtag('event', 'pwa_install_attempt', {
            'event_category': 'PWA',
            'event_label': platform,
          });
        }

        await deferredPrompt.prompt();
        const { outcome } = await deferredPrompt.userChoice;
        
        console.log('PWA: User choice:', outcome);
        
        if (outcome === 'accepted') {
          console.log('PWA: User accepted the install prompt');
          // Don't set isInstalled here immediately, wait for the appinstalled event
        } else {
          console.log('PWA: User dismissed the install prompt');
          setIsInstalling(false);
          
          // Track dismissal
          if (typeof window !== 'undefined' && (window as any).gtag) {
            (window as any).gtag('event', 'pwa_install_dismissed', {
              'event_category': 'PWA',
              'event_label': platform,
            });
          }
        }
        
        setDeferredPrompt(null);
      } catch (error) {
        console.error('PWA: Error during app installation:', error);
        setInstallError('Installation failed. Please try the manual installation.');
        setIsInstalling(false);
        
        // Track error
        if (typeof window !== 'undefined' && (window as any).gtag) {
          (window as any).gtag('event', 'pwa_install_error', {
            'event_category': 'PWA',
            'event_label': platform,
            'value': error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    } else {
      // No automatic install available, show instructions modal
      setShowInstructionsModal(true);
      
      // Track manual install attempt
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('event', 'pwa_manual_install_attempt', {
          'event_category': 'PWA',
          'event_label': platform,
        });
      }
    }
  };

  const getInstallInstructions = () => {
    switch (platform) {
      case 'ios':
        return {
          icon: <Share className="h-6 w-6" />,
          title: "Install on iOS",
          steps: [
            "Tap the Share button (⬆️) at the bottom of Safari",
            "Scroll down and tap 'Add to Home Screen'",
            "Tap 'Add' to install the app",
            "Access from your home screen like any native app"
          ]
        };
      case 'android':
        return {
          icon: <MoreHorizontal className="h-6 w-6" />,
          title: "Install on Android", 
          steps: [
            "Tap the menu button (⋮) in Chrome",
            "Select 'Add to Home screen' or 'Install app'",
            "Tap 'Add' to install the app",
            "Find it in your app drawer with other apps"
          ]
        };
      case 'desktop':
        return {
          icon: <Monitor className="h-6 w-6" />,
          title: "Install on Desktop",
          steps: [
            "Look for the install button (⊕) in your browser's address bar",
            "Click 'Install' when prompted",
            "The app will be added to your desktop and start menu",
            "Launch it like any native desktop application"
          ]
        };
      default:
        return {
          icon: <Download className="h-6 w-6" />,
          title: "Install App",
          steps: [
            "Look for the install option in your browser",
            "Follow your browser's installation prompts",
            "The app will be available on your device"
          ]
        };
    }
  };

  // Get device-specific content
  const getPlatformContent = () => {
    if (!isClient) {
      // Return generic content during SSR to prevent hydration mismatch
      return {
        badgeMessage: "Get the App",
        badgeIcon: <Download className="h-4 w-4" />,
        title: "Take Your Focus Anywhere",
        subtitle: "Install Pomodoro 365 as a native app on your device for the ultimate productivity experience.",
        ctaText: "Download App",
        features: [
          {
            icon: <Wifi className="h-5 w-5" />,
            title: "Works Offline",
            description: "Continue your focus sessions even without internet connection",
            colorScheme: "orange"
          },
          {
            icon: <Zap className="h-5 w-5" />,
            title: "Lightning Fast",
            description: "Native-like performance optimized for your device",
            colorScheme: "red"
          },
          {
            icon: <Bell className="h-5 w-5" />,
            title: "Smart Notifications",
            description: "Get timely alerts for breaks and session completions",
            colorScheme: "emerald"
          },
          {
            icon: <Heart className="h-5 w-5" />,
            title: "Battery Friendly",
            description: "Optimized to preserve your device's battery life",
            colorScheme: "rose"
          }
        ]
      };
    }

    switch (platform) {
      case 'ios':
        return {
          badgeMessage: "Get the iOS App",
          badgeIcon: <Smartphone className="h-4 w-4" />,
          title: "Native iOS Experience",
          subtitle: "Install Pomodoro 365 and enjoy seamless integration with iOS features, widgets, and Shortcuts app.",
          ctaText: "Install for iOS",
          features: [
            {
              icon: <Grid3X3 className="h-5 w-5" />,
              title: "Home Screen Widgets",
              description: "Quick access to your focus sessions right from your iOS home screen",
              colorScheme: "orange"
            },
            {
              icon: <Puzzle className="h-5 w-5" />,
              title: "Shortcuts Integration",
              description: "Create custom automations with the iOS Shortcuts app",
              colorScheme: "red"
            },
            {
              icon: <Bell className="h-5 w-5" />,
              title: "Native Notifications",
              description: "Rich iOS notifications that work even when the app is closed",
              colorScheme: "emerald"
            },
            {
              icon: <Wifi className="h-5 w-5" />,
              title: "Offline Ready",
              description: "Full functionality without internet - perfect for focus sessions",
              colorScheme: "rose"
            }
          ]
        };
      
      case 'android':
        return {
          badgeMessage: "Get the Android App",
          badgeIcon: <Smartphone className="h-4 w-4" />,
          title: "Pure Android Experience",
          subtitle: "Install Pomodoro 365 and enjoy deep Android integration with adaptive icons, widgets, and material design.",
          ctaText: "Install for Android",
          features: [
            {
              icon: <Home className="h-5 w-5" />,
              title: "Adaptive Widgets",
              description: "Beautiful home screen widgets that match your Android theme",
              colorScheme: "orange"
            },
            {
              icon: <Zap className="h-5 w-5" />,
              title: "Background Sync",
              description: "Seamless data sync in the background using Android's WorkManager",
              colorScheme: "red"
            },
            {
              icon: <Bell className="h-5 w-5" />,
              title: "Smart Notifications",
              description: "Rich notifications with quick actions and Android Auto support",
              colorScheme: "emerald"
            },
            {
              icon: <Heart className="h-5 w-5" />,
              title: "Battery Optimized",
              description: "Doze mode compatible with intelligent battery management",
              colorScheme: "rose"
            }
          ]
        };

      case 'desktop':
      case 'unknown':
      default:
        return {
          badgeMessage: "Get the Desktop App",
          badgeIcon: <Monitor className="h-4 w-4" />,
          title: "Professional Desktop Experience",
          subtitle: "Install Pomodoro 365 as a native desktop app with keyboard shortcuts, system tray, and multi-monitor support.",
          ctaText: "Install for Desktop",
          features: [
            {
              icon: <Command className="h-5 w-5" />,
              title: "Keyboard Shortcuts",
              description: "Full keyboard control for power users and productivity enthusiasts",
              colorScheme: "orange"
            },
            {
              icon: <Monitor className="h-5 w-5" />,
              title: "Multi-Monitor Support",
              description: "Position timer on any screen while working on another",
              colorScheme: "red"
            },
            {
              icon: <Bell className="h-5 w-5" />,
              title: "System Notifications",
              description: "Native desktop notifications that respect your system settings",
              colorScheme: "emerald"
            },
            {
              icon: <Zap className="h-5 w-5" />,
              title: "Always Available",
              description: "Quick access from system tray without browser overhead",
              colorScheme: "rose"
            }
          ]
        };
    }
  };

  const content = getPlatformContent();
  const instructions = getInstallInstructions();

  return (
    <>
      <section className="w-full py-16 md:py-20 bg-gradient-to-b from-background via-muted/20 to-background border-t border-muted/30 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0 z-0 pointer-events-none">
          <div className="absolute top-1/4 left-1/6 h-40 w-40 rounded-full bg-gradient-to-r from-orange-500/5 to-red-500/5 blur-3xl animate-pulse" />
          <div className="absolute bottom-1/3 right-1/6 h-32 w-32 rounded-full bg-gradient-to-r from-rose-500/5 to-orange-500/5 blur-3xl animate-pulse delay-1000" />
        </div>

        <div className="container mx-auto px-4 max-w-6xl relative z-10">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="flex flex-col items-center text-center mb-12"
          >
            <div className="inline-flex items-center gap-2 px-4 py-2 mb-6 rounded-full bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-950/30 dark:to-red-950/30 text-orange-600 dark:text-orange-400 text-sm font-medium border border-orange-200/60 dark:border-orange-800/40">
              {content.badgeIcon}
              <span>{content.badgeMessage}</span>
            </div>

            <h2
              className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight text-foreground mb-6"
              style={{
                fontFamily: 'var(--font-geist-sans)',
                letterSpacing: '-0.025em',
              }}
            >
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 dark:from-slate-100 dark:via-slate-200 dark:to-slate-300">
                {content.title.split(' ').slice(0, -1).join(' ')}{' '}
              </span>
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 font-extrabold">
                {content.title.split(' ').slice(-1)[0]}
              </span>
            </h2>

            <p
              className="text-lg text-muted-foreground max-w-2xl leading-relaxed mb-8"
              style={{ fontFamily: 'var(--font-geist-sans)' }}
            >
              {content.subtitle}
            </p>

            {/* Installation Status & CTA */}
            <div className="w-full max-w-md">
              {isInstalled ? (
                <motion.div
                  initial={{ scale: 0.9 }}
                  animate={{ scale: 1 }}
                  className="flex items-center justify-center gap-3 p-4 bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-400 rounded-xl border border-green-200 dark:border-green-800"
                >
                  <CheckCircle className="h-5 w-5" />
                  <span className="font-medium">App Already Installed!</span>
                </motion.div>
              ) : (
                <div className="space-y-4">
                  <Button
                    onClick={handleDownloadClick}
                    disabled={isInstalling}
                    size="lg"
                    className="group relative w-full h-14 text-lg font-semibold cursor-pointer overflow-hidden rounded-xl border-0 bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 text-white shadow-lg transition-all duration-300 ease-out hover:shadow-2xl hover:shadow-orange-500/25 hover:scale-[1.02] active:scale-[0.98] disabled:cursor-not-allowed disabled:opacity-60 disabled:hover:scale-100 disabled:hover:shadow-lg focus-visible:ring-2 focus-visible:ring-orange-400 focus-visible:ring-offset-2"
                  >
                    {isInstalling ? (
                      <>
                        <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                        Installing...
                      </>
                    ) : (
                      <>
                        <Download className="h-5 w-5 mr-2" />
                        {content.ctaText}
                      </>
                    )}
                  </Button>
                  
                  {installError && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex items-start gap-3 p-3 bg-red-50 dark:bg-red-950/20 text-red-700 dark:text-red-400 rounded-lg border border-red-200 dark:border-red-800 text-sm"
                    >
                      <AlertCircle className="h-4 w-4 flex-shrink-0 mt-0.5" />
                      <span>{installError}</span>
                    </motion.div>
                  )}

                  <p className="text-xs text-muted-foreground text-center">
                    {platform === 'desktop' || platform === 'unknown'
                      ? 'Free • Cross-platform • No Microsoft Store required'
                      : platform === 'ios'
                      ? 'Free • Native iOS features • No App Store required'
                      : platform === 'android'
                      ? 'Free • Material Design • No Google Play required'
                      : 'Free • Cross-platform • No Microsoft Store required'
                    }
                  </p>
                </div>
              )}
            </div>
          </motion.div>

          {/* Features Grid */}
          {/* <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 gap-6"
          >
            {content.features.map((feature, index) => {
              // Define color schemes for each feature
              const getFeatureColors = (colorScheme: string) => {
                switch (colorScheme) {
                  case 'orange':
                    return {
                      iconBg: 'bg-gradient-to-br from-orange-500 to-orange-600',
                      hoverText: 'group-hover:text-orange-600 dark:group-hover:text-orange-400'
                    };
                  case 'red':
                    return {
                      iconBg: 'bg-gradient-to-br from-red-500 to-red-600',
                      hoverText: 'group-hover:text-red-600 dark:group-hover:text-red-400'
                    };
                  case 'emerald':
                    return {
                      iconBg: 'bg-gradient-to-br from-emerald-500 to-emerald-600',
                      hoverText: 'group-hover:text-emerald-600 dark:group-hover:text-emerald-400'
                    };
                  case 'rose':
                    return {
                      iconBg: 'bg-gradient-to-br from-rose-500 to-rose-600',
                      hoverText: 'group-hover:text-rose-600 dark:group-hover:text-rose-400'
                    };
                  default:
                    return {
                      iconBg: 'bg-gradient-to-br from-orange-500 to-red-600',
                      hoverText: 'group-hover:text-orange-600 dark:group-hover:text-orange-400'
                    };
                }
              };

              const colors = getFeatureColors(feature.colorScheme);

              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <Card className="group h-full bg-gradient-to-br from-white/80 to-white/40 dark:from-slate-800/80 dark:to-slate-900/40 border-slate-200/60 dark:border-slate-700/60 hover:shadow-lg transition-all duration-300 backdrop-blur-sm">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className={`flex-shrink-0 p-3 ${colors.iconBg} text-white rounded-xl shadow-sm group-hover:scale-110 transition-transform duration-200`}>
                          {feature.icon}
                        </div>
                        <div className="flex-1">
                          <h3
                            className={`text-lg font-semibold text-foreground mb-2 ${colors.hoverText} transition-colors duration-200`}
                            style={{ fontFamily: 'var(--font-geist-sans)' }}
                          >
                            {feature.title}
                          </h3>
                          <p className="text-muted-foreground leading-relaxed" style={{ fontFamily: 'var(--font-geist-sans)' }}>
                            {feature.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </motion.div> */}
        </div>
      </section>

      {/* Installation Instructions Modal */}
      <Dialog open={showInstructionsModal} onOpenChange={setShowInstructionsModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3">
              {instructions.icon}
              {instructions.title}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Follow these quick steps to install the app:
            </p>
            
            <ol className="space-y-3">
              {instructions.steps.map((step, index) => (
                <li key={index} className="flex items-start gap-3 text-sm">
                  <span className="flex-shrink-0 w-6 h-6 bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400 rounded-full flex items-center justify-center text-xs font-medium">
                    {index + 1}
                  </span>
                  <span className="leading-relaxed">{step}</span>
                </li>
              ))}
            </ol>

            <div className="pt-4 border-t">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => setShowInstructionsModal(false)}
                >
                  Got it
                </Button>
                <Button
                  className="flex-1"
                  onClick={() => {
                    setShowInstructionsModal(false);
                    // Optionally try to trigger any available install prompt again
                    if (deferredPrompt) {
                      handleDownloadClick();
                    }
                  }}
                >
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}; 