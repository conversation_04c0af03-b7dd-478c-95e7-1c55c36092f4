'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FileVideo, Upload, Film } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

export function VideoPlaceholder() {
  return (
    <Card className="w-full p-0 overflow-hidden border-0 shadow-lg">
      <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 h-2"></div>
      <CardHeader className="space-y-2 pb-2">
        <CardTitle className="text-2xl font-bold flex items-center gap-2">
          <Film className="h-6 w-6 text-primary" /> 
          <span>No Videos Available</span>
        </CardTitle>
        <CardDescription className="text-base">
          Add background videos to enhance your Pomodoro experience
        </CardDescription>
      </CardHeader>
      <CardContent className="grid md:grid-cols-2 gap-8 p-6">
        <div className="bg-muted rounded-lg p-6 space-y-4">
          <div className="rounded-full w-16 h-16 bg-primary/10 flex items-center justify-center mx-auto">
            <FileVideo className="h-8 w-8 text-primary" />
          </div>
          <h3 className="font-semibold text-center">Add Video Files</h3>
          <ol className="text-sm text-muted-foreground space-y-2 list-decimal list-inside">
            <li>Create <code className="text-xs bg-muted-foreground/20 px-1 py-0.5 rounded">/public/videos/</code> directory</li>
            <li>Add MP4 video files</li>
            <li>Create matching thumbnails</li>
            <li>Update video sources in your code</li>
          </ol>
        </div>
        
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50 rounded-lg p-6 space-y-4 border border-blue-100 dark:border-blue-900">
          <div className="rounded-full w-16 h-16 bg-blue-500/10 flex items-center justify-center mx-auto">
            <Upload className="h-8 w-8 text-blue-500" />
          </div>
          <h3 className="font-semibold text-center">Quick Start</h3>
          <p className="text-sm text-muted-foreground text-center mb-4">
            Don&apos;t have videos yet? You can still try the app with placeholder backgrounds.
          </p>
          <Link href="https://github.com/yourusername/pomodoro-flow" className="block">
            <Button className="w-full bg-gradient-to-r from-blue-500 to-indigo-500 text-white border-0">
              Read Documentation
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
} 