"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON>, <PERSON>, Clock, Zap } from "lucide-react"

interface FocusPatternInsightsProps {
  data: {
    productivityRhythm: string
    focusDurationInsight: string
    consistencyPattern: string
    recommendations: string[]
  }
}

export function FocusPatternInsights({ data }: FocusPatternInsightsProps) {
  return (
    <Card className="border-border bg-card/50 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Brain className="mr-2 h-5 w-5 text-violet-500" /> Focus Pattern Insights
        </CardTitle>
        <CardDescription>AI-powered analysis of your productivity patterns</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="rounded-lg bg-accent/30 p-4">
          <div className="mb-2 flex items-center">
            <Zap className="mr-2 h-5 w-5 text-amber-500" />
            <h3 className="font-medium">Productivity Rhythm</h3>
          </div>
          <p className="text-sm text-muted-foreground">{data.productivityRhythm}</p>
        </div>

        <div className="rounded-lg bg-accent/30 p-4">
          <div className="mb-2 flex items-center">
            <Clock className="mr-2 h-5 w-5 text-emerald-500" />
            <h3 className="font-medium">Focus Duration Insights</h3>
          </div>
          <p className="text-sm text-muted-foreground">{data.focusDurationInsight}</p>
        </div>

        <div className="rounded-lg bg-accent/30 p-4">
          <div className="mb-2 flex items-center">
            <Calendar className="mr-2 h-5 w-5 text-blue-500" />
            <h3 className="font-medium">Consistency Patterns</h3>
          </div>
          <p className="text-sm text-muted-foreground">{data.consistencyPattern}</p>
        </div>

        <div className="mt-6 rounded-lg border border-violet-500/20 bg-violet-500/5 p-4">
          <h3 className="mb-2 font-medium text-violet-400">Personalized Recommendations</h3>
          <ul className="space-y-2 text-sm text-muted-foreground">
            {data.recommendations.map((recommendation: string, index: number) => (
              <li key={index} className="flex items-start">
                <span className="mr-2 mt-1 h-1.5 w-1.5 rounded-full bg-violet-500"></span>
                {recommendation}
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
