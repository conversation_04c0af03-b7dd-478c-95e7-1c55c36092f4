"use client"

import { useState } from "react"

interface DailyHeatmapProps {
  data: Array<{
    hour: number
    day: string
    value: number
  }>
}

export function DailyHeatmap({ data }: DailyHeatmapProps) {
  const [hoveredCell, setHoveredCell] = useState<null | { hour: number; day: string; value: number }>(null)

  // Get unique days and sort them
  const days = Array.from(new Set(data.map((item) => item.day)))

  // Get all hours (0-23)
  const hours = Array.from({ length: 24 }, (_, i) => i)

  // Function to get color based on value
  const getColor = (value: number) => {
    if (value === 0) return "bg-muted"
    if (value < 15) return "bg-primary/20"
    if (value < 30) return "bg-primary/40"
    if (value < 45) return "bg-primary/60"
    if (value < 60) return "bg-primary/80"
    return "bg-primary"
  }

  // Format hour for display
  const formatHour = (hour: number) => {
    const ampm = hour >= 12 ? "PM" : "AM"
    const h = hour % 12 || 12
    return `${h} ${ampm}`
  }

  return (
    <div className="overflow-x-auto">
      <div className="min-w-[800px]">
        {/* Hour labels */}
        <div className="mb-2 flex">
          <div className="w-20"></div>
          {hours
            .filter((hour) => hour % 2 === 0)
            .map((hour) => (
              <div key={hour} className="w-[33.33px] text-center text-xs text-muted-foreground">
                {hour}
              </div>
            ))}
        </div>

        {/* Heatmap grid */}
        <div className="relative">
          {days.map((day) => (
            <div key={day} className="mb-1 flex items-center">
              <div className="w-20 text-sm text-muted-foreground">{day}</div>
              <div className="flex">
                {hours.map((hour) => {
                  const cellData = data.find((d) => d.day === day && d.hour === hour)
                  const value = cellData ? cellData.value : 0

                  return (
                    <div
                      key={`${day}-${hour}`}
                      className={`relative h-6 w-[16.66px] ${getColor(value)} transition-colors hover:opacity-80`}
                      onMouseEnter={() => setHoveredCell({ day, hour, value })}
                      onMouseLeave={() => setHoveredCell(null)}
                    >
                      {hoveredCell && hoveredCell.day === day && hoveredCell.hour === hour && (
                        <div className="absolute -top-16 left-1/2 z-10 w-36 -translate-x-1/2 rounded-lg border border-slate-200/60 dark:border-slate-800/60 bg-gradient-to-br from-white via-white to-slate-50/30 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800/30 p-2 text-center text-xs shadow-xl shadow-slate-900/5 dark:shadow-slate-900/20 backdrop-blur-sm">
                          <p className="font-medium text-slate-900 dark:text-slate-100">
                            {day}, {formatHour(hour)}
                          </p>
                          <p className="text-rose-600 dark:text-rose-400">{value} minutes of focus</p>
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </div>
          ))}

          {/* Legend */}
          <div className="mt-6 flex items-center justify-end">
            <div className="mr-2 text-xs text-muted-foreground">Focus Intensity:</div>
            <div className="flex items-center">
              <div className="h-3 w-3 bg-muted"></div>
              <div className="h-3 w-3 bg-primary/20"></div>
              <div className="h-3 w-3 bg-primary/40"></div>
              <div className="h-3 w-3 bg-primary/60"></div>
              <div className="h-3 w-3 bg-primary/80"></div>
              <div className="h-3 w-3 bg-primary"></div>
            </div>
            <div className="ml-2 flex items-center justify-between text-xs text-muted-foreground">
              <span>Low</span>
              <span className="ml-2">High</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
