import { z } from 'zod';
import { UserRoleSchema } from '../inputTypeSchemas/UserRoleSchema'
import { UserWithRelationsSchema, UserPartialWithRelationsSchema } from './UserSchema'
import type { UserWithRelations, UserPartialWithRelations } from './UserSchema'
import { NatureSoundWithRelationsSchema, NatureSoundPartialWithRelationsSchema } from './NatureSoundSchema'
import type { NatureSoundWithRelations, NatureSoundPartialWithRelations } from './NatureSoundSchema'
import { VideoWithRelationsSchema, VideoPartialWithRelationsSchema } from './VideoSchema'
import type { VideoWithRelations, VideoPartialWithRelations } from './VideoSchema'

/////////////////////////////////////////
// NATURE PLAYLIST SCHEMA
/////////////////////////////////////////

export const NaturePlaylistSchema = z.object({
  creatorType: UserRoleSchema,
  id: z.string().cuid(),
  name: z.string().min(1),
  description: z.string().nullish(),
  imageUrl: z.union([z.instanceof(File), z.string().nullable()]).nullish(),
  isPublic: z.boolean(),
  isDefault: z.boolean(),
  userId: z.string(),
  natureSoundOrder: z.string().array(),
  createdAt: z.union([z.date(), z.string().datetime()]),
  updatedAt: z.union([z.date(), z.string().datetime()]),
})

export type NaturePlaylist = z.infer<typeof NaturePlaylistSchema>

/////////////////////////////////////////
// NATURE PLAYLIST PARTIAL SCHEMA
/////////////////////////////////////////

export const NaturePlaylistPartialSchema = NaturePlaylistSchema.partial()

export type NaturePlaylistPartial = z.infer<typeof NaturePlaylistPartialSchema>

/////////////////////////////////////////
// NATURE PLAYLIST RELATION SCHEMA
/////////////////////////////////////////

export type NaturePlaylistRelations = {
  user: UserWithRelations;
  natureSounds: NatureSoundWithRelations[];
  videos: VideoWithRelations[];
};

export type NaturePlaylistWithRelations = z.infer<typeof NaturePlaylistSchema> & NaturePlaylistRelations

export const NaturePlaylistWithRelationsSchema: z.ZodType<NaturePlaylistWithRelations> = NaturePlaylistSchema.merge(z.object({
  user: z.lazy(() => UserWithRelationsSchema),
  natureSounds: z.lazy(() => NatureSoundWithRelationsSchema).array(),
  videos: z.lazy(() => VideoWithRelationsSchema).array(),
}))

/////////////////////////////////////////
// NATURE PLAYLIST PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type NaturePlaylistPartialRelations = {
  user?: UserPartialWithRelations;
  natureSounds?: NatureSoundPartialWithRelations[];
  videos?: VideoPartialWithRelations[];
};

export type NaturePlaylistPartialWithRelations = z.infer<typeof NaturePlaylistPartialSchema> & NaturePlaylistPartialRelations

export const NaturePlaylistPartialWithRelationsSchema: z.ZodType<NaturePlaylistPartialWithRelations> = NaturePlaylistPartialSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  natureSounds: z.lazy(() => NatureSoundPartialWithRelationsSchema).array(),
  videos: z.lazy(() => VideoPartialWithRelationsSchema).array(),
})).partial()

export type NaturePlaylistWithPartialRelations = z.infer<typeof NaturePlaylistSchema> & NaturePlaylistPartialRelations

export const NaturePlaylistWithPartialRelationsSchema: z.ZodType<NaturePlaylistWithPartialRelations> = NaturePlaylistSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  natureSounds: z.lazy(() => NatureSoundPartialWithRelationsSchema).array(),
  videos: z.lazy(() => VideoPartialWithRelationsSchema).array(),
}).partial())

export default NaturePlaylistSchema;
