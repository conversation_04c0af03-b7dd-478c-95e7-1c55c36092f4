import { Hono } from "hono";
import prisma from "@/lib/prisma";
import { UserVariable } from "../..";
import naturePlaylistAdminRoutes from "../NaturalPlaylist/natural-playlist-admin-route";
import musicPlaylistAdminRoutes from "../MusicPlaylist/music-playlist-admin-route";
import { adminMiddleware } from "@/server/private/middleware";

const app = new Hono<{ Variables: UserVariable }>()
  // Get dashboard statistics
  .get("/dashboard", adminMiddleware, async (c) => {
    // We're not using the user variable for this endpoint
    
    // Count total music tracks
    const musicCount = await prisma.music.count();
    
    // Count total videos
    const videoCount = await prisma.video.count();
    
    // Count music playlists
    const musicPlaylistCount = await prisma.musicPlaylist.count();
    
    // Count nature playlists
    const naturePlaylistCount = await prisma.naturePlaylist.count();
    
    // Count total nature sounds
    const natureSoundCount = await prisma.natureSound.count();
    
    return c.json({
      data: {
        musicCount,
        videoCount,
        musicPlaylistCount,
        naturePlaylistCount,
        natureSoundCount
      }
    });
  })
  
  // Mount nature playlists admin routes
  .route("/naturePlaylists", naturePlaylistAdminRoutes)
  
  // Mount music playlists admin routes
  .route("/musicPlaylists", musicPlaylistAdminRoutes);

export default app; 