"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON>ontaine<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

interface OptimalFocusTimeChartProps {
  data: Array<{
    hour: string
    score: number
  }>
}

export function OptimalFocusTimeChart({ data }: OptimalFocusTimeChartProps) {
  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const score = payload[0].value

      let ratingText = "Low productivity"
      let ratingColor = "text-rose-500"

      if (score >= 80) {
        ratingText = "Peak productivity"
        ratingColor = "text-emerald-500"
      } else if (score >= 60) {
        ratingText = "High productivity"
        ratingColor = "text-amber-500"
      } else if (score >= 40) {
        ratingText = "Moderate productivity"
        ratingColor = "text-blue-500"
      }

      return (
        <div className="rounded-md border border-slate-700 bg-slate-800 p-3 shadow-md">
          <p className="mb-1 font-medium">{label}</p>
          <p className={ratingColor}>
            <span className="font-bold">{score}</span> - {ratingText}
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart data={data} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
        <XAxis dataKey="hour" stroke="#64748b" tickLine={false} axisLine={false} tick={{ fontSize: 12 }} />
        <YAxis
          stroke="#64748b"
          tickLine={false}
          axisLine={false}
          tick={{ fontSize: 12 }}
          width={30}
          domain={[0, 100]}
          hide
        />
        <Tooltip content={<CustomTooltip />} />
        <Bar dataKey="score" radius={[4, 4, 0, 0]} barSize={20}>
          {data.map((entry, index) => (
            <rect
              key={`bar-${index}`}
              fill={
                entry.score >= 80
                  ? "#10b981"
                  : // emerald-500
                    entry.score >= 60
                    ? "#f59e0b"
                    : // amber-500
                      entry.score >= 40
                      ? "#3b82f6"
                      : // blue-500
                        "#f43f5e" // rose-500
              }
            />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
}
