"use client"

import { useTaskStore } from "@/store/taskStore"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { CheckCircle, Clock, ListChecks, Target, Zap } from "lucide-react"
import { motion } from "framer-motion"
import { formatDistanceToNow } from "date-fns"

export function TaskStatsOverview() {
  const { tasks } = useTaskStore()

  // Calculate task statistics
  const totalTasks = tasks.length
  const completedTasks = tasks.filter((task) => task.status === "completed").length
  const pendingTasks = tasks.filter((task) => task.status === "pending").length

  const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0

  // Calculate average pomodoros per task
  const tasksWithPomodoros = tasks.filter((task) => task.pomodoroCount > 0)
  const avgPomodorosPerTask =
    tasksWithPomodoros.length > 0
      ? Math.round(
          (tasksWithPomodoros.reduce((sum, task) => sum + task.pomodoroCount, 0) / tasksWithPomodoros.length) * 10,
        ) / 10
      : 0

  // Calculate estimated vs actual pomodoros
  const tasksWithEstimates = tasks.filter((task) => task.pomodoroEstimate > 0 && task.pomodoroCount > 0)
  const estimationAccuracy =
    tasksWithEstimates.length > 0
      ? Math.round(
          (tasksWithEstimates.reduce((sum, task) => sum + task.pomodoroCount, 0) /
            tasksWithEstimates.reduce((sum, task) => sum + task.pomodoroEstimate, 0)) *
            100,
        )
      : 0

  // Find most recently completed task
  const recentlyCompletedTask = [...tasks]
    .filter((task) => task.status === "completed")
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())[0]

  // Find task with most pomodoros
  const taskWithMostPomodoros = [...tasks].sort((a, b) => b.pomodoroCount - a.pomodoroCount)[0]

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Task Performance</h2>

      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="card-hover"
        >
          <Card className="border-border bg-card/50 shadow-lg">
            <CardContent className="flex items-center p-6">
              <div className="mr-4 rounded-full bg-rose-500/10 p-3">
                <ListChecks className="h-6 w-6 text-primary" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Tasks</p>
                <h3 className="text-2xl font-bold">{totalTasks}</h3>
                <div className="mt-1 flex text-xs">
                  <span className="text-emerald-500">{completedTasks} completed</span>
                  <span className="mx-1 text-muted-foreground">•</span>
                  <span className="text-amber-500">{pendingTasks} pending</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="card-hover"
        >
          <Card className="border-border bg-card/50 shadow-lg">
            <CardContent className="flex items-center p-6">
              <div className="mr-4 rounded-full bg-emerald-500/10 p-3">
                <CheckCircle className="h-6 w-6 text-emerald-500" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completion Rate</p>
                <h3 className="text-2xl font-bold">{completionRate}%</h3>
                <div className="mt-2 w-full">
                  <Progress value={completionRate} className="h-1.5 bg-muted" indicatorClassName="bg-emerald-500" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className="card-hover"
        >
          <Card className="border-border bg-card/50 shadow-lg">
            <CardContent className="flex items-center p-6">
              <div className="mr-4 rounded-full bg-amber-500/10 p-3">
                <Clock className="h-6 w-6 text-amber-500" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg. Pomodoros</p>
                <h3 className="text-2xl font-bold">{avgPomodorosPerTask}</h3>
                <p className="text-xs text-muted-foreground/70">per completed task</p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="card-hover"
        >
          <Card className="border-border bg-card/50 shadow-lg">
            <CardContent className="flex items-center p-6">
              <div className="mr-4 rounded-full bg-blue-500/10 p-3">
                <Target className="h-6 w-6 text-blue-500" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Estimation Accuracy</p>
                <h3 className="text-2xl font-bold">{estimationAccuracy}%</h3>
                <p className="text-xs text-muted-foreground/70">
                  {estimationAccuracy > 100 ? "Underestimated" : "Overestimated"}
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      <div className="grid gap-4 sm:grid-cols-2">
        {recentlyCompletedTask && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.5 }}
            className="card-hover"
          >
            <Card className="border-border bg-card/50 shadow-lg">
              <CardContent className="p-6">
                <div className="mb-4 flex items-center">
                  <CheckCircle className="mr-2 h-5 w-5 text-emerald-500" />
                  <h3 className="font-medium">Recently Completed</h3>
                </div>
                <p className="mb-1 text-lg font-semibold">{recentlyCompletedTask.title}</p>
                <p className="mb-3 text-sm text-muted-foreground line-clamp-2">{recentlyCompletedTask.description}</p>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground/70">
                    Completed {formatDistanceToNow(new Date(recentlyCompletedTask.updatedAt), { addSuffix: true })}
                  </span>
                  <span className="flex items-center text-amber-500">
                    <Clock className="mr-1 h-3 w-3" />
                    {recentlyCompletedTask.pomodoroCount} pomodoros
                  </span>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {taskWithMostPomodoros && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.6 }}
            className="card-hover"
          >
            <Card className="border-border bg-card/50 shadow-lg">
              <CardContent className="p-6">
                <div className="mb-4 flex items-center">
                  <Zap className="mr-2 h-5 w-5 text-amber-500" />
                  <h3 className="font-medium">Most Focus Time</h3>
                </div>
                <p className="mb-1 text-lg font-semibold">{taskWithMostPomodoros.title}</p>
                <p className="mb-3 text-sm text-muted-foreground line-clamp-2">{taskWithMostPomodoros.description}</p>
                <div className="flex items-center justify-between text-xs">
                  <span
                    className={`${taskWithMostPomodoros.status === "completed" ? "text-emerald-500" : "text-amber-500"}`}
                  >
                    {taskWithMostPomodoros.status === "completed" ? "Completed" : "In progress"}
                  </span>
                  <span className="flex items-center text-primary">
                    <Clock className="mr-1 h-3 w-3" />
                    {taskWithMostPomodoros.pomodoroCount} pomodoros ({taskWithMostPomodoros.pomodoroCount * 25} min)
                  </span>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  )
}
