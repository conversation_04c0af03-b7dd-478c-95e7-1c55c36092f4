import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { InferRequestType, InferResponseType } from "hono";
import client from "@/lib/trpc";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

// Get all nature playlists (Admin)
export type GetAdminNaturePlaylists_ResponseType = InferResponseType<
  (typeof client.api.admin.naturePlaylists)["$get"],
  200
>;

export type GetAdminNaturePlaylists_ResponseTypeSuccess = Extract<
  GetAdminNaturePlaylists_ResponseType,
  { data: object }
>["data"];

export const useGetAdminNaturePlaylists = (filters?: {
  isPublic?: boolean;
  isDefault?: boolean;
}) => {
  return useQuery({
    queryKey: ["admin", "naturePlaylists", filters],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      
      if (filters?.isPublic !== undefined) {
        queryParams.append("isPublic", String(filters.isPublic));
      }
      
      if (filters?.isDefault !== undefined) {
        queryParams.append("isDefault", String(filters.isDefault));
      }
      
      const queryString = queryParams.toString();
      
      let response;
      if (queryString) {
        response = await client.api.admin.naturePlaylists.$get({
          query: { 
            isPublic: filters?.isPublic !== undefined ? String(filters.isPublic) : undefined,
            isDefault: filters?.isDefault !== undefined ? String(filters.isDefault) : undefined
          }
        });
      } else {
        response = await client.api.admin.naturePlaylists.$get();
      }
      
      if (!response.ok) {
        throw new Error("Failed to fetch admin nature playlists");
      }
      const { data } = await response.json();
      return data;
    },
  });
};

// Get single nature playlist (Admin)
type GetAdminNaturePlaylist_ResponseType = InferResponseType<
  (typeof client.api.admin.naturePlaylists)[":id"]["$get"],
  200
>;

export type GetAdminNaturePlaylist_ResponseTypeSuccess = Extract<
  GetAdminNaturePlaylist_ResponseType,
  { data: object }
>["data"];

export const useGetAdminNaturePlaylist = (id?: string) => {
  return useQuery({
    queryKey: ["admin", "naturePlaylists", { id }],
    queryFn: async () => {
      if (!id) throw new Error("No nature playlist ID provided");

      const response = await client.api.admin.naturePlaylists[":id"]["$get"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch admin nature playlist");
      }

      const { data } = await response.json();
      return data;
    },
    enabled: !!id,
  });
};

// Create nature playlist (Admin)
interface CreateAdminNaturePlaylistSuccessResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

type CreateAdminNaturePlaylistRequest = InferRequestType<
  (typeof client.api.admin.naturePlaylists)["$post"]
>;

export const useCreateAdminNaturePlaylist = () => {
  const router = useRouter();
  const queryClient = useQueryClient();

  const mutation = useMutation<
    CreateAdminNaturePlaylistSuccessResponse,
    Error,
    CreateAdminNaturePlaylistRequest
  >({
    mutationFn: async ({ form }) => {
      const response = await client.api.admin.naturePlaylists.$post({ form });

      if (!response.ok) {
        throw new Error("Failed to create admin nature playlist");
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Nature playlist created successfully");
      router.refresh();
      queryClient.invalidateQueries({ queryKey: ["admin", "naturePlaylists"] });
    },
    onError: (error) => {
      toast.error(`Failed to create nature playlist: ${error.message}`);
    },
  });

  return mutation;
};

// Update nature playlist (Admin)
type UpdateAdminNaturePlaylist_ResponseType = InferResponseType<
  (typeof client.api.admin.naturePlaylists)[":id"]["$patch"],
  200
>;

export type UpdateAdminNaturePlaylist_ResponseTypeSuccess = Extract<
  UpdateAdminNaturePlaylist_ResponseType,
  { data: object }
>["data"];

type UpdateAdminNaturePlaylistRequest = InferRequestType<
  (typeof client.api.admin.naturePlaylists)[":id"]["$patch"]
>;

export const useUpdateAdminNaturePlaylist = () => {
  const queryClient = useQueryClient();
  
  return useMutation<
    UpdateAdminNaturePlaylist_ResponseType,
    Error,
    UpdateAdminNaturePlaylistRequest
  >({
    mutationFn: async (variables) => {
      const { form, param } = variables;

      if (!param?.id) {
        throw new Error("No nature playlist ID provided");
      }

      const response = await client.api.admin.naturePlaylists[":id"]["$patch"]({
        form,
        param: { id: param.id },
      });

      if (!response.ok) {
        throw new Error(`Failed to update admin nature playlist. Status: ${response.status}`);
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Nature playlist updated successfully");
      const naturePlaylistId = data?.id;
      if (naturePlaylistId) {
        queryClient.invalidateQueries({ queryKey: ["admin", "naturePlaylists", { id: naturePlaylistId }] });
      }
      queryClient.invalidateQueries({ queryKey: ["admin", "naturePlaylists"] });
    },
    onError: (error) => {
      toast.error(`Failed to update nature playlist: ${error.message}`);
    },
  });
};

// Delete nature playlist (Admin)
type DeleteAdminNaturePlaylist_ResponseType = InferResponseType<
  (typeof client.api.admin.naturePlaylists)[":id"]["$delete"],
  200
>;

export const useDeleteAdminNaturePlaylist = () => {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation<
    DeleteAdminNaturePlaylist_ResponseType,
    Error,
    { id: string }
  >({
    mutationFn: async ({ id }) => {
      if (!id) {
        throw new Error("No nature playlist ID provided");
      }

      const response = await client.api.admin.naturePlaylists[":id"]["$delete"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to delete admin nature playlist");
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Nature playlist deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["admin", "naturePlaylists"] });
      router.push("/admin/nature-playlists");
    },
    onError: (error) => {
      toast.error(`Failed to delete nature playlist: ${error.message}`);
    },
  });
};

// Add Nature Sounds to Nature Playlist (Admin)
interface AdminNatureSoundResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface AddNatureSoundsToAdminNaturePlaylistRequest {
  naturePlaylistId: string;
  natureSoundIds: string[];
}

export const useAddNatureSoundsToAdminNaturePlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    AdminNatureSoundResponse,
    Error,
    AddNatureSoundsToAdminNaturePlaylistRequest
  >({
    mutationFn: async ({ naturePlaylistId, natureSoundIds }) => {
      if (!naturePlaylistId) {
        throw new Error("No nature playlist ID provided");
      }

      const response = await client.api.admin.naturePlaylists[":id"]["nature-sounds"].$post({
        param: { id: naturePlaylistId },
        json: { natureSoundIds }
      });

      if (!response.ok) {
        throw new Error(`Failed to add nature sounds to admin nature playlist: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Nature sounds added to nature playlist successfully");
      queryClient.invalidateQueries({ queryKey: ["admin", "naturePlaylists", { id: variables.naturePlaylistId }] });
    },
    onError: (error) => {
      toast.error(`Failed to add nature sounds to nature playlist: ${error.message}`);
    },
  });
};

// Remove Nature Sound from Nature Playlist (Admin)
interface RemoveAdminNatureSoundResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface RemoveNatureSoundFromAdminNaturePlaylistRequest {
  naturePlaylistId: string;
  natureSoundId: string;
}

export const useRemoveNatureSoundFromAdminNaturePlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    RemoveAdminNatureSoundResponse,
    Error,
    RemoveNatureSoundFromAdminNaturePlaylistRequest
  >({
    mutationFn: async ({ naturePlaylistId, natureSoundId }) => {
      if (!naturePlaylistId || !natureSoundId) {
        throw new Error("Both nature playlist ID and nature sound ID are required");
      }

      const response = await client.api.admin.naturePlaylists[":id"]["nature-sounds"][":natureSoundId"]["$delete"]({
        param: { id: naturePlaylistId, natureSoundId },
      });

      if (!response.ok) {
        throw new Error(`Failed to remove nature sound from admin nature playlist: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Nature sound removed from nature playlist");
      queryClient.invalidateQueries({ queryKey: ["admin", "naturePlaylists", { id: variables.naturePlaylistId }] });
      queryClient.invalidateQueries({ queryKey: ["admin", "naturePlaylists"] });
    },
    onError: (error) => {
      toast.error(`Failed to remove nature sound from nature playlist: ${error.message}`);
    },
  });
};

// Reorder Nature Sounds in Nature Playlist (Admin)
interface ReorderAdminNatureSoundResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface ReorderAdminNatureSoundRequest {
  naturePlaylistId: string;
  natureSoundOrder: string[];
}

export const useReorderNatureSoundsInAdminNaturePlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    ReorderAdminNatureSoundResponse,
    Error,
    ReorderAdminNatureSoundRequest
  >({
    mutationFn: async ({ naturePlaylistId, natureSoundOrder }) => {
      if (!naturePlaylistId) {
        throw new Error("No nature playlist ID provided");
      }

      const response = await client.api.admin.naturePlaylists[":id"]["nature-sounds"].reorder.$patch({
        param: { id: naturePlaylistId },
        json: { natureSoundOrder }
      });

      if (!response.ok) {
        throw new Error(`Failed to reorder nature sounds in admin nature playlist: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Nature sounds order updated successfully");
      queryClient.invalidateQueries({ queryKey: ["admin", "naturePlaylists", { id: variables.naturePlaylistId }] });
    },
    onError: (error) => {
      toast.error(`Failed to reorder nature sounds: ${error.message}`);
    },
  });
};

// Add Videos to Nature Playlist (Admin)
interface AdminVideoResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface AddVideosToAdminNaturePlaylistRequest {
  naturePlaylistId: string;
  videoIds: string[];
}

export const useAddVideosToAdminNaturePlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    AdminVideoResponse,
    Error,
    AddVideosToAdminNaturePlaylistRequest
  >({
    mutationFn: async ({ naturePlaylistId, videoIds }) => {
      if (!naturePlaylistId) {
        throw new Error("No nature playlist ID provided");
      }

      const response = await client.api.admin.naturePlaylists[":id"].videos.$post({
        param: { id: naturePlaylistId },
        json: { videoIds }
      });

      if (!response.ok) {
        throw new Error(`Failed to add videos to admin nature playlist: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Videos added to nature playlist successfully");
      queryClient.invalidateQueries({ queryKey: ["admin", "naturePlaylists", { id: variables.naturePlaylistId }] });
    },
    onError: (error) => {
      toast.error(`Failed to add videos to nature playlist: ${error.message}`);
    },
  });
};

// Remove Video from Nature Playlist (Admin)
interface RemoveAdminVideoResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface RemoveVideoFromAdminNaturePlaylistRequest {
  naturePlaylistId: string;
  videoId: string;
}

export const useRemoveVideoFromAdminNaturePlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    RemoveAdminVideoResponse,
    Error,
    RemoveVideoFromAdminNaturePlaylistRequest
  >({
    mutationFn: async ({ naturePlaylistId, videoId }) => {
      if (!naturePlaylistId || !videoId) {
        throw new Error("Both nature playlist ID and video ID are required");
      }

      const response = await client.api.admin.naturePlaylists[":id"].videos[":videoId"]["$delete"]({
        param: { id: naturePlaylistId, videoId },
      });

      if (!response.ok) {
        throw new Error(`Failed to remove video from admin nature playlist: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Video removed from nature playlist");
      queryClient.invalidateQueries({ queryKey: ["admin", "naturePlaylists", { id: variables.naturePlaylistId }] });
    },
    onError: (error) => {
      toast.error(`Failed to remove video from nature playlist: ${error.message}`);
    },
  });
}; 