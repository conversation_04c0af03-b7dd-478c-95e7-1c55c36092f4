"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function DashboardPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to analytics by default
    router.replace("/dashboard/analytics")
  }, [router])

  return (
    <div className="flex min-h-screen items-center justify-center bg-background dark:bg-background">
      <div className="text-center space-y-4">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-muted border-t-primary mx-auto"></div>
        <h3 className="text-lg font-medium">Loading Dashboard</h3>
        <p className="text-muted-foreground">Redirecting to analytics...</p>
      </div>
    </div>
  )
}
