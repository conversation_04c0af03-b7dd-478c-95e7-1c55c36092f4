"use client";

import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Des<PERSON>,
  SheetFooter
} from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Star, Clock } from "lucide-react";
import {
  useCreateMusic,
  useUpdateMusic,
  useGetMusic
} from "@schemas/Music/music-query";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MusicGenre, MediaSource } from "@prisma/client";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { secondsToMinutesAndSeconds } from "@/lib/duration-utils";

interface MusicFormSheetProps {
  musicId?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function MusicFormSheet({
  musicId,
  open,
  onOpenChange,
  onSuccess
}: MusicFormSheetProps) {
  const router = useRouter();
  const isEditMode = !!musicId;
  const title = isEditMode ? "Edit Music" : "Create Music";

  // Form state
  const [formData, setFormData] = useState({
    title: "",
    src: "",
    source: MediaSource.OTHER as MediaSource,
    rating: undefined as number | undefined,
    genres: [] as MusicGenre[],
    isPublic: true,
    isCopyright: false,
    useAsOpeningMusic: false,
    duration: undefined as number | undefined,
    note: "",
  });

  // Duration input state (separate from formData for easier handling)
  const [durationInput, setDurationInput] = useState({
    minutes: 0,
    seconds: 0,
  });

  // Form validation state
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Mutations for creating and updating music
  const createMusicMutation = useCreateMusic();
  const updateMusicMutation = useUpdateMusic();

  // For edit mode, fetch the music details
  const musicQuery = useGetMusic(musicId);

  // When in edit mode and music data is loaded, populate the form
  useEffect(() => {
    if (isEditMode && musicQuery.data) {
      const musicData = musicQuery.data as any;
      setFormData({
        title: musicData.title,
        src: musicData.src || "",
        source: musicData.source || MediaSource.OTHER,
        rating: musicData.rating || undefined,
        genres: musicData.genres || [],
        isPublic: musicData.isPublic,
        isCopyright: musicData.isCopyright || false,
        useAsOpeningMusic: musicData.useAsOpeningMusic || false,
        duration: musicData.duration || undefined,
        note: musicData.note || "",
      });

      // Set duration input fields
      const { minutes, seconds } = secondsToMinutesAndSeconds(musicData.duration);
      setDurationInput({ minutes, seconds });
    }
  }, [isEditMode, musicQuery.data]);

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle star rating changes
  const handleStarRating = (_event: React.MouseEvent<HTMLButtonElement>, star: number) => {
    // No need for half-star logic based on click position, simply set the whole star rating
    setFormData(prev => ({ ...prev, rating: star }));

    // Clear error when field is edited
    if (errors.rating) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.rating;
        return newErrors;
      });
    }
  };

  // Clear rating
  const clearRating = () => {
    setFormData(prev => ({ ...prev, rating: undefined }));
    if (errors.rating) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.rating;
        return newErrors;
      });
    }
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Handle genre checkbox changes
  const handleGenreChange = (genre: MusicGenre, checked: boolean) => {
    setFormData(prev => {
      if (checked) {
        return { ...prev, genres: [...prev.genres, genre] };
      } else {
        return { ...prev, genres: prev.genres.filter(g => g !== genre) };
      }
    });
  };

  // Handle source select changes
  const handleSourceChange = (value: string) => {
    setFormData(prev => ({ ...prev, source: value as MediaSource }));
  };

  // Handle duration input changes
  const handleDurationChange = (field: 'minutes' | 'seconds', value: string) => {
    const numValue = parseInt(value) || 0;

    // Validate seconds range
    if (field === 'seconds' && (numValue < 0 || numValue > 59)) {
      return;
    }

    // Validate minutes range (reasonable limit)
    if (field === 'minutes' && numValue < 0) {
      return;
    }

    const newDurationInput = { ...durationInput, [field]: numValue };
    setDurationInput(newDurationInput);

    // Update formData with total seconds
    const totalSeconds = (newDurationInput.minutes * 60) + newDurationInput.seconds;
    setFormData(prev => ({ ...prev, duration: totalSeconds > 0 ? totalSeconds : undefined }));
  };

  // Handle note changes
  const handleNoteChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { value } = e.target;
    setFormData(prev => ({ ...prev, note: value }));
  };

  // Format source name for display
  const formatSourceName = (source: string) => {
    switch (source) {
      case 'EPIDEMICSOUND':
        return 'Epidemic Sound';
      case 'PIXABAY':
        return 'Pixabay';
      case 'SUNO':
        return 'Suno';
      case 'YOUTUBE':
        return 'YouTube';
      case 'OTHER':
        return 'Other';
      default:
        return source.charAt(0) + source.slice(1).toLowerCase();
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    }

    if (formData.src && formData.src.trim()) {
      try {
        new URL(formData.src);
      } catch {
        newErrors.src = "Please enter a valid URL";
      }
    }

    if (formData.rating !== undefined) {
      if (formData.rating < 0 || formData.rating > 5) {
        newErrors.rating = "Rating must be between 0 and 5";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Make sure genres is properly formatted as an array
    const submitData = {
      title: formData.title,
      src: formData.src || undefined,
      source: formData.source,
      rating: formData.rating !== undefined ? formData.rating.toString() : undefined,
      genres: formData.genres,
      isPublic: formData.isPublic.toString(),
      isCopyright: formData.isCopyright.toString(),
      useAsOpeningMusic: formData.useAsOpeningMusic.toString(),
      duration: formData.duration !== undefined ? formData.duration.toString() : undefined,
      note: formData.note || undefined,
    };

    if (isEditMode && musicId) {
      updateMusicMutation.mutate(
        {
          form: submitData,
          param: { id: musicId },
        },
        {
          onSuccess: () => {
            router.refresh();
            onSuccess?.();
            onOpenChange(false);
            toast.success("Music updated successfully");
          },
          onError: (error) => {
            console.error("Error updating music:", error);
            toast.error("Failed to update music");
          }
        }
      );
    } else {
      createMusicMutation.mutate(
        {
          form: submitData,
        },
        {
          onSuccess: () => {
            router.refresh();
            onSuccess?.();
            onOpenChange(false);
            // Reset form
            setFormData({
              title: "",
              src: "",
              source: MediaSource.OTHER as MediaSource,
              rating: undefined,
              genres: [],
              isPublic: true,
              isCopyright: false,
              useAsOpeningMusic: false,
              duration: undefined,
              note: "",
            });
            setDurationInput({ minutes: 0, seconds: 0 });
          },
          onError: (error) => {
            console.error("Error creating music:", error);
            toast.error("Failed to create music");
          }
        }
      );
    }
  };

  // Check if loading or submitting
  const isLoading =
    (isEditMode && musicQuery.isLoading) ||
    createMusicMutation.isPending ||
    updateMusicMutation.isPending;

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-md md:max-w-lg lg:max-w-xl w-full p-0 focus:outline-none">
        <div className="h-full flex flex-col">
          <SheetHeader className="px-6 pt-6 pb-2">
            <SheetTitle className="text-xl font-semibold">{title}</SheetTitle>
            <SheetDescription>
              {isEditMode
                ? "Update your music details below"
                : "Fill in the details to create new music"
              }
            </SheetDescription>
          </SheetHeader>

          <div className="flex-1 overflow-y-auto px-6">
            <form id="music-form" onSubmit={handleSubmit} className="space-y-5 py-4">
              <div className="space-y-2">
                <Label htmlFor="title" className={errors.title ? 'text-destructive font-medium' : 'font-medium'}>
                  Title <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="title"
                  name="title"
                  placeholder="Enter music title"
                  value={formData.title}
                  onChange={handleChange}
                  className={errors.title ? 'border-destructive' : ''}
                  aria-required="true"
                  autoComplete="off"
                />
                {errors.title && (
                  <p className="text-sm text-destructive mt-1">{errors.title}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="src" className={errors.src ? 'text-destructive font-medium' : 'font-medium'}>
                  Audio URL
                </Label>
                <Input
                  id="src"
                  name="src"
                  placeholder="https://example.com/audio.mp3"
                  value={formData.src}
                  onChange={handleChange}
                  className={errors.src ? 'border-destructive' : ''}
                  autoComplete="off"
                />
                {errors.src && (
                  <p className="text-sm text-destructive mt-1">{errors.src}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="source" className="font-medium">
                  Source
                </Label>
                <Select value={formData.source} onValueChange={handleSourceChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a source" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(MediaSource).map((source) => (
                      <SelectItem key={source} value={source}>
                        {formatSourceName(source)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="rating" className={errors.rating ? 'text-destructive font-medium' : 'font-medium'}>
                  Rating
                </Label>
                <div className="space-y-2">
                  <div className="flex items-center gap-1">
                    {[1, 2, 3, 4, 5].map((star) => {
                      const isFilled = formData.rating !== undefined && formData.rating >= star;
                      
                      return (
                        <button
                          key={star}
                          type="button"
                          onClick={(e) => handleStarRating(e, star)}
                          onMouseEnter={(e) => {
                            // Add hover effect
                            const starElements = e.currentTarget.parentElement?.querySelectorAll('button');
                            starElements?.forEach((starElement, index) => {
                              const starIcon = starElement.querySelector('svg');
                              if (starIcon) {
                                if (index < star) {
                                  starIcon.classList.add('fill-yellow-400', 'text-yellow-400');
                                  starIcon.classList.remove('text-muted-foreground');
                                } else {
                                  starIcon.classList.remove('fill-yellow-400', 'text-yellow-400');
                                  starIcon.classList.add('text-muted-foreground');
                                }
                              }
                            });
                          }}
                          onMouseLeave={(e) => {
                            // Reset to actual rating on mouse leave
                            const starElements = e.currentTarget.parentElement?.querySelectorAll('button');
                            starElements?.forEach((starElement, index) => {
                              const starIcon = starElement.querySelector('svg');
                              if (starIcon) {
                                const currentRating = formData.rating || 0;
                                if (index < currentRating) {
                                  starIcon.classList.add('fill-yellow-400', 'text-yellow-400');
                                  starIcon.classList.remove('text-muted-foreground');
                                } else {
                                  starIcon.classList.remove('fill-yellow-400', 'text-yellow-400');
                                  starIcon.classList.add('text-muted-foreground');
                                }
                              }
                            });
                          }}
                          className="p-1 rounded-sm hover:bg-muted transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                          aria-label={`Rate ${star} star${star > 1 ? 's' : ''}`}
                        >
                          <Star
                            className={`h-6 w-6 transition-colors ${
                              isFilled
                                ? 'fill-yellow-400 text-yellow-400'
                                : 'text-muted-foreground hover:text-yellow-400'
                            }`}
                          />
                        </button>
                      );
                    })}
                    {formData.rating !== undefined && (
                      <div className="flex items-center gap-2 ml-2">
                        <span className="text-sm text-muted-foreground">
                          {formData.rating.toFixed(1)}
                        </span>
                        <button
                          type="button"
                          onClick={clearRating}
                          className="text-xs text-muted-foreground hover:text-foreground underline underline-offset-2 transition-colors"
                          aria-label="Clear rating"
                        >
                          Clear
                        </button>
                      </div>
                    )}
                  </div>
                </div>
                {errors.rating && (
                  <p className="text-sm text-destructive mt-1">{errors.rating}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label className="font-medium flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Duration
                </Label>
                <div className="flex items-center gap-2">
                  <div className="flex-1">
                    <Input
                      type="number"
                      placeholder="0"
                      value={durationInput.minutes}
                      onChange={(e) => handleDurationChange('minutes', e.target.value)}
                      min="0"
                      max="999"
                      className="text-center"
                      aria-label="Minutes"
                    />
                    <Label className="text-xs text-muted-foreground mt-1 block text-center">
                      Minutes
                    </Label>
                  </div>
                  <span className="text-muted-foreground font-medium">:</span>
                  <div className="flex-1">
                    <Input
                      type="number"
                      placeholder="00"
                      value={durationInput.seconds}
                      onChange={(e) => handleDurationChange('seconds', e.target.value)}
                      min="0"
                      max="59"
                      className="text-center"
                      aria-label="Seconds"
                    />
                    <Label className="text-xs text-muted-foreground mt-1 block text-center">
                      Seconds
                    </Label>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Enter the duration in minutes and seconds (e.g., 3:45)
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="note" className="font-medium">
                  Note
                </Label>
                <Textarea
                  id="note"
                  placeholder="Add any notes about this music track..."
                  value={formData.note}
                  onChange={handleNoteChange}
                  rows={3}
                  maxLength={500}
                  className="resize-none"
                />
                <div className="flex justify-between items-center">
                  <p className="text-xs text-muted-foreground">
                    Optional notes or description for this track
                  </p>
                  <span className="text-xs text-muted-foreground">
                    {formData.note.length}/500
                  </span>
                </div>
              </div>

              <div className="space-y-3">
                <Label className="font-medium">Genres</Label>
                <div className="grid grid-cols-2 gap-3">
                  {Object.values(MusicGenre).map((genre) => (
                    <div key={genre} className="flex items-center space-x-2">
                      <Checkbox
                        id={`genre-${genre}`}
                        checked={formData.genres.includes(genre)}
                        onCheckedChange={(checked) => handleGenreChange(genre, checked === true)}
                      />
                      <Label htmlFor={`genre-${genre}`} className="cursor-pointer">
                        {genre}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="isPublic" className="font-medium">
                  Public Music
                </Label>
                <Switch
                  id="isPublic"
                  checked={formData.isPublic}
                  onCheckedChange={(checked) => handleSwitchChange('isPublic', checked === true)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="isCopyright" className="font-medium">
                  Copyright concerns
                </Label>
                <Switch
                  id="isCopyright"
                  checked={formData.isCopyright}
                  onCheckedChange={(checked) => handleSwitchChange('isCopyright', checked === true)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="useAsOpeningMusic" className="font-medium">
                  Use as Opening Music
                </Label>
                <Switch
                  id="useAsOpeningMusic"
                  checked={formData.useAsOpeningMusic}
                  onCheckedChange={(checked) => handleSwitchChange('useAsOpeningMusic', checked === true)}
                  aria-label="Toggle use as opening music"
                />
              </div>
            </form>
          </div>

          <SheetFooter className="px-6 py-4 border-t">
            <Button
              type="submit"
              form="music-form"
              disabled={isLoading}
              className="w-full sm:w-auto"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isEditMode ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  {isEditMode ? 'Update Music' : 'Create Music'}
                </>
              )}
            </Button>
          </SheetFooter>
        </div>
      </SheetContent>
    </Sheet>
  );
}