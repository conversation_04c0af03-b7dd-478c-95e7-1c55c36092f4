import { createMiddleware } from "hono/factory";
import { auth } from "../auth/auth";

// Define the type for the additional context
export type AppVariables = {
  Variables: {
    user: typeof auth.$Infer.Session.user;
    session: typeof auth.$Infer.Session.session;
  };
};

export const privateRoutesMiddleware = createMiddleware<AppVariables>(
  async (c, next) => {
    const session = await auth.api.getSession({ headers: c.req.raw.headers });
    // console.log("session", session);
    if (!session) {
      c.status(401);
      return c.json({ message: "Unauthorized" });
    }
    c.set("user", session.user);
    c.set("session", session.session);
    return next();
  }
);

export const adminMiddleware = createMiddleware<AppVariables>(
  async (c, next) => {
    const session = await auth.api.getSession({ headers: c.req.raw.headers });
    if (!session) {
      c.status(401);
      return c.json({ message: "Unauthorized" });
    }
    const user = session.user;
    c.set("user", user);
    c.set("session", session.session);
    
    if (user.role !== "ADMIN" || user.id !== "rgrD9gaIxTyapjI53TfcHCMKz5a3Mvwj") {
      c.status(403);
      return c.json({ message: "Unauthorized" });
    }
    return next();
  }
);

// export default privateRoutesMiddleware;
