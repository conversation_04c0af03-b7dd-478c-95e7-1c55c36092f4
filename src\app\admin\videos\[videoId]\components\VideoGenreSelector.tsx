"use client";

import { Check, ChevronsUpDown } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { useState } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";

// Video genres from the schema
const VIDEO_GENRES = [
  "MEDITATION",
  "NATURE",
  "COFFEE",
  "BUILDING",
  "ANIMALS",
  "TRAVEL",
  "TECHNOLOGY",
  "ART",
  "STUDY",
  "LOFI",
  "AMBIENT",
];

interface VideoGenreSelectorProps {
  selectedGenres: string[];
  onChange: (genres: string[]) => void;
}

export function VideoGenreSelector({ selectedGenres, onChange }: VideoGenreSelectorProps) {
  const [open, setOpen] = useState(false);

  const toggleGenre = (genre: string) => {
    if (selectedGenres.includes(genre)) {
      onChange(selectedGenres.filter(g => g !== genre));
    } else {
      onChange([...selectedGenres, genre]);
    }
  };

  const formatGenreName = (genre: string) => {
    return genre.charAt(0) + genre.slice(1).toLowerCase();
  };

  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            {selectedGenres.length > 0
              ? `${selectedGenres.length} genre${selectedGenres.length > 1 ? 's' : ''} selected`
              : "Select genres"}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput placeholder="Search genres..." />
            <CommandList>
              <CommandEmpty>No genres found.</CommandEmpty>
              <CommandGroup>
                <ScrollArea className="h-[200px]">
                  {VIDEO_GENRES.map((genre) => (
                    <CommandItem
                      key={genre}
                      onSelect={() => toggleGenre(genre)}
                      className="flex items-center justify-between"
                    >
                      <span>{formatGenreName(genre)}</span>
                      {selectedGenres.includes(genre) && (
                        <Check className="h-4 w-4 text-primary" />
                      )}
                    </CommandItem>
                  ))}
                </ScrollArea>
              </CommandGroup>
              <CommandSeparator />
              <CommandGroup>
                <CommandItem
                  onSelect={() => onChange([])}
                  className="justify-center text-sm text-muted-foreground"
                >
                  Clear selection
                </CommandItem>
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {selectedGenres.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {selectedGenres.map((genre) => (
            <Badge
              key={genre}
              variant="secondary"
              className="flex items-center gap-1"
            >
              {formatGenreName(genre)}
              <button
                className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
                onClick={() => toggleGenre(genre)}
              >
                <span className="sr-only">Remove {genre} genre</span>
                <span className="h-3 w-3 flex items-center justify-center">×</span>
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}
