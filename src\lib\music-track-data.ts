export interface MusicTrack {
  id: string;
  title: string;
  genre: string;
  description: string;
  mood?: string;
  bpm?: number;
  musicList: string[]; // List of audio IDs from audio-store
}

// Sample music tracks data
export const musicTracks: MusicTrack[] = [
  {
    id: 'mt1',
    title: 'Peaceful Piano',
    genre: 'Ambient',
    description: 'Relaxing ocean sounds with gentle ambient music',
    mood: 'Calm',
    bpm: 60,
    musicList: ['1', '2'] // Ocean Waves, Forest Rain
  },
  {
    id: 'mt2',
    title: 'Forest Meditation',
    genre: 'Nature',
    description: 'Immersive forest sounds perfect for focus and meditation',
    mood: 'Peaceful',
    bpm: 65,
    musicList: ['2', '1'] // Forest Rain, Harmony of the World
  },
  {
    id: 'mt3',
    title: 'Jungle Adventure',
    genre: 'Ambient',
    description: 'Exotic jungle sounds mixed with meditative ambient music',
    mood: 'Adventurous',
    bpm: 70,
    musicList: ['1', '3', '4'] // Harmony of the World, Ocean Waves, Meditation Music
  },
  {
    id: 'mt4',
    title: 'Mountain Stream Vibes',
    genre: 'Nature',
    description: 'Flowing water and mountain atmosphere for deep focus',
    mood: 'Refreshing',
    bpm: 62,
    musicList: ['3', '2', '4'] // Ocean Waves, Forest Rain, Meditation Music
  },
  {
    id: 'mt5',
    title: 'Café Ambient',
    genre: 'Urban',
    description: 'Coffee shop ambience with smooth background tunes',
    mood: 'Cozy',
    bpm: 80,
    musicList: ['8', '5'] // Coffee Shop Ambience, Ambient Flow
  },
  {
    id: 'mt6',
    title: 'City Nights',
    genre: 'Lo-Fi',
    description: 'Urban nighttime sounds mixed with lo-fi beats',
    mood: 'Atmospheric',
    bpm: 85,
    musicList: ['9', '5', '10'] // Busy Street, Ambient Flow, Lo-Fi Study Beats
  }
]; 