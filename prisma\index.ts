import { Hono } from "hono";
import authController from "@/server/auth/index";
import { auth } from "@/server/auth/auth";

// import privateSchemaRoutes from "@schemas/private/private-route";
import videoRoutes from "@schemas/Video/video-route";
import musicRoutes from "@schemas/Music/music-route";
import natureSoundRoutes from "@schemas/Natural/nature-sound-route";
import musicPlaylistAdminRoutes from "@schemas/MusicPlaylist/music-playlist-admin-route";
import musicPlaylistUserRoutes from "@schemas/MusicPlaylist/music-playlist-user-route";
import naturePlaylistRoutes from "@schemas/NaturalPlaylist/nature-playlist-route";
import adminRoutes from "@schemas/Admin/admin-route";
import polarRoutes from "@schemas/Payments/polar-route";
import pomodoroRoutes from "@schemas/Pomodoro/pomodoro-route";
import taskRoutes from "@schemas/Tasks/task-route";

// Define the app variables type once
export type UserVariable = {
  user: typeof auth.$Infer.Session.user;
  session: typeof auth.$Infer.Session.session;
};

// Create the app with the centralized type
const app = new Hono<{ Variables: UserVariable }>().basePath("/api");


const routes = app
  .route("/auth", authController)
  .route("/videos", videoRoutes)
  .route("/musics", musicRoutes)
  .route("/natureSounds", natureSoundRoutes)
  .route("/musicPlaylists", musicPlaylistAdminRoutes)
  .route("/musicPlaylistsUser", musicPlaylistUserRoutes)
  .route("/naturePlaylists", naturePlaylistRoutes)
  .route("/admin", adminRoutes)
  .route("/polar", polarRoutes)
  .route("/pomodoro", pomodoroRoutes)
  .route("/tasks", taskRoutes);


export type AppType = typeof routes;
export default app;
