'use client';

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Clock } from 'lucide-react';

// Legacy component for backward compatibility (used within timer display)
interface CurrentTimeDisplayProps {
  showControls: boolean;
  timerColor: any;
  timerSize?: {
    timeScale?: number;
  } | null;
}

export function CurrentTimeDisplay({
  showControls,
  timerColor,
  timerSize
}: CurrentTimeDisplayProps) {
  const [currentTime, setCurrentTime] = useState('');

  // Update current time every second
  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const timeString = now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });
      setCurrentTime(timeString);
    };

    // Update immediately
    updateTime();

    // Set up interval to update every second
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, []);

  // Calculate responsive sizing based on timer scale
  const getResponsiveStyles = () => {
    const baseScale = timerSize?.timeScale || 1;

    if (baseScale > 1.3) {
      return {
        fontSize: `${Math.max(0.7, Math.min(0.9, 0.7 + (baseScale - 1.3) * 0.2))}rem`,
        iconSize: Math.max(12, Math.min(16, 12 + (baseScale - 1.3) * 4)),
        padding: `${Math.max(0.3, Math.min(0.6, 0.3 + (baseScale - 1.3) * 0.3))}rem`,
        gap: `${Math.max(0.3, Math.min(0.5, 0.3 + (baseScale - 1.3) * 0.2))}rem`
      };
    }

    return {
      fontSize: '0.7rem',
      iconSize: 12,
      padding: '0.3rem',
      gap: '0.3rem'
    };
  };

  const styles = getResponsiveStyles();

  return (
    <div
      className={cn(
        "flex items-center justify-center transition-all duration-300 ease-out",
        "bg-black/20 backdrop-blur-sm rounded-lg border border-white/10",
        "timer-control-fade",
        showControls ? "show opacity-100" : "hide opacity-0 pointer-events-none"
      )}
      style={{
        padding: styles.padding,
        gap: styles.gap,
        fontSize: styles.fontSize,
        marginTop: showControls ? '0.5rem' : '0',
        animationDelay: '120ms'
      }}
      role="status"
      aria-label={`Current time: ${currentTime}`}
    >
      <Clock
        className="flex-shrink-0 transition-colors duration-200 text-white"
        style={{
          width: `${styles.iconSize}px`,
          height: `${styles.iconSize}px`,
          filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5))' // Enhanced shadow for glass readability
        }}
      />
      <span
        className="font-mono font-medium tabular-nums transition-colors duration-200 text-white"
        style={{
          fontSize: styles.fontSize,
          textShadow: '0 1px 3px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.3)' // Enhanced shadow for glass readability
        }}
      >
        {currentTime}
      </span>
    </div>
  );
}
