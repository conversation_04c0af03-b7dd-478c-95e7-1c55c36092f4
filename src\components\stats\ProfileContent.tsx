"use client"

import { useState, useEffect } from "react"
import {
  User,
  Mail,
  Calendar,
  Crown,
  Edit,
  X,
  Check,
  Clock,
  CheckCircle,
  Camera,
  Loader2,
  Shield,
  Target,
  TrendingUp,
  Settings,
  Bell,
  Eye,
  Volume2,
  Zap,
  Award,
  Activity,
  BarChart3,
  Timer,
  Flame,
  Brain,
  ChevronRight,
  Download,
  Lock,
  Smartphone,
  Monitor,
  Globe
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { useUserStore } from "@/store/userStore"
import { usePomodoroStore } from "@/lib/pomodoro-store"
import { toast } from "sonner"
import { motion, AnimatePresence } from "framer-motion"
// Import Better Auth UI Components
import {
  ChangeEmailCard,
  ChangePasswordCard,
  DeleteAccountCard,
  ProvidersCard,
  SessionsCard,
  UpdateAvatarCard,
  UpdateUsernameCard
} from "@daveyplate/better-auth-ui"

export function ProfileContent() {
  const {
    user,
    stats,
    preferences,
    getDisplayName,
    getAvatarUrl,
    isPremium,
    isAdmin,
    updateUser,
    updatePreferences,
    isLoading: userLoading
  } = useUserStore()

  const {
    timerSettings,
    updateSettings
  } = usePomodoroStore()

  const [isEditing, setIsEditing] = useState(false)
  const [editedName, setEditedName] = useState(user?.name || "")
  const [isLoading, setIsLoading] = useState(false)
  const [activeSection, setActiveSection] = useState<'overview' | 'preferences' | 'security'>('overview')

  // Update edited name when user changes
  useEffect(() => {
    if (user?.name) {
      setEditedName(user.name)
    }
  }, [user?.name])

  const handleSaveProfile = async () => {
    if (!user || editedName.trim() === "") {
      toast.error("Name cannot be empty")
      return
    }

    if (editedName.trim() === user.name) {
      setIsEditing(false)
      return
    }

    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 800)) // Simulate API call
      updateUser({ name: editedName.trim() })
      setIsEditing(false)
      toast.success("Profile updated successfully", {
        description: "Your changes have been saved."
      })
    } catch (error) {
      toast.error("Failed to update profile", {
        description: "Please try again later."
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelEdit = () => {
    setEditedName(user?.name || "")
    setIsEditing(false)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatRelativeDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) return "Today"
    if (diffDays === 1) return "Yesterday"
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`
    return `${Math.floor(diffDays / 365)} years ago`
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)

    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  const calculateProductivityScore = () => {
    if (stats.totalPomodoroSessions === 0) return 0
    const completionRate = (stats.totalPomodoroSessions / (stats.totalPomodoroSessions + 1)) * 100
    const streakBonus = Math.min(stats.currentStreak * 2, 20)
    return Math.min(Math.round(completionRate + streakBonus), 100)
  }

  const handlePreferenceChange = (category: string, key: string, value: any) => {
    if (category === "notifications") {
      updatePreferences({
        notifications: {
          ...preferences.notifications,
          [key]: value,
        },
      })
    } else if (category === "pomodoro") {
      updatePreferences({
        pomodoro: {
          ...preferences.pomodoro,
          [key]: value,
        },
      })
    } else if (category === "privacy") {
      updatePreferences({
        privacy: {
          ...preferences.privacy,
          [key]: value,
        },
      })
    }
  }

  if (userLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-3">
          <Loader2 className="h-8 w-8 text-primary mx-auto animate-spin" />
          <h3 className="text-lg font-medium">Loading Profile</h3>
          <p className="text-muted-foreground">Please wait while we fetch your information</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-center min-h-[400px]"
      >
        <div className="text-center space-y-4">
          <div className="h-20 w-20 rounded-full bg-muted/30 mx-auto flex items-center justify-center">
            <User className="h-10 w-10 text-muted-foreground" />
          </div>
          <div className="space-y-2">
            <h3 className="text-xl font-medium">Not Signed In</h3>
            <p className="text-muted-foreground max-w-sm">
              Please sign in to view and manage your profile settings
            </p>
          </div>
          <Button onClick={() => window.location.href = "/auth/sign-in"} className="mt-4">
            Sign In
          </Button>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      {/* Profile Header */}
      <Card className="border-border/40 shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <User className="h-4 w-4" />
            Profile Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-start gap-4">
            {/* Avatar Section */}
            <div className="relative group">
              <Avatar className="h-16 w-16 ring-2 ring-border">
                <AvatarImage src={getAvatarUrl() || undefined} alt={getDisplayName()} />
                <AvatarFallback className="bg-gradient-to-br from-primary/10 to-primary/20 text-primary text-lg font-semibold">
                  {getDisplayName().charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <Button
                size="sm"
                variant="secondary"
                className="absolute -bottom-1 -right-1 h-6 w-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg"
              >
                <Camera className="h-3 w-3" />
              </Button>
            </div>

            {/* Profile Info */}
            <div className="flex-1 space-y-3">
              <div className="flex items-start justify-between">
                <div className="space-y-1 flex-1">
                  <div className="flex items-center gap-2">
                    <AnimatePresence mode="wait">
                      {isEditing ? (
                        <motion.div
                          key="editing"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          className="flex items-center gap-2 flex-1"
                        >
                          <Input
                            value={editedName}
                            onChange={(e) => setEditedName(e.target.value)}
                            className="text-lg font-semibold border-primary/20 focus:border-primary h-8"
                            placeholder="Enter your name"
                            onKeyDown={(e) => {
                              if (e.key === "Enter") handleSaveProfile()
                              if (e.key === "Escape") handleCancelEdit()
                            }}
                            autoFocus
                          />
                          <Button size="sm" onClick={handleSaveProfile} disabled={isLoading} className="h-8 w-8 p-0">
                            {isLoading ? <Loader2 className="h-3 w-3 animate-spin" /> : <Check className="h-3 w-3" />}
                          </Button>
                          <Button size="sm" variant="outline" onClick={handleCancelEdit} disabled={isLoading} className="h-8 w-8 p-0">
                            <X className="h-3 w-3" />
                          </Button>
                        </motion.div>
                      ) : (
                        <motion.div
                          key="display"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          className="flex items-center gap-2 group flex-1"
                        >
                          <h1 className="text-lg font-semibold">{getDisplayName()}</h1>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => setIsEditing(true)}
                            className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>

                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Mail className="h-3 w-3" />
                    <span>{user.email}</span>
                    {user.emailVerified && (
                      <Badge variant="secondary" className="text-xs h-5">
                        <CheckCircle className="h-2 w-2 mr-1" />
                        Verified
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-1 ml-2">
                  {isPremium() && (
                    <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-0 shadow-sm text-xs h-5">
                      <Crown className="h-2 w-2 mr-1" />
                      Premium
                    </Badge>
                  )}
                  {isAdmin() && (
                    <Badge variant="outline" className="border-blue-200 text-blue-700 bg-blue-50 dark:bg-blue-950 dark:border-blue-800 dark:text-blue-300 text-xs h-5">
                      <Shield className="h-2 w-2 mr-1" />
                      Admin
                    </Badge>
                  )}
                </div>
              </div>

              {/* Account Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 pt-2 border-t border-border/50">
                <div className="space-y-1">
                  <Label className="text-xs font-medium text-muted-foreground">Join Date</Label>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs">{formatDate(stats.joinedDate)}</span>
                  </div>
                </div>

                <div className="space-y-1">
                  <Label className="text-xs font-medium text-muted-foreground">Last Active</Label>
                  <div className="flex items-center gap-2">
                    <Clock className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs">{formatRelativeDate(stats.lastActiveDate)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Settings Section */}
      <Card className="border-border/40 shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Settings className="h-4 w-4" />
            Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Security */}
          <div className="space-y-4">
            <div className="space-y-1">
              <h3 className="text-sm font-medium text-foreground">Security</h3>
              <p className="text-xs text-muted-foreground">Manage your password and authentication settings</p>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <ChangePasswordCard />
              <ProvidersCard />
            </div>
          </div>

          <Separator />

          {/* Account Management */}
          <div className="space-y-4">
            <div className="space-y-1">
              <h3 className="text-sm font-medium text-foreground">Account Management</h3>
              <p className="text-xs text-muted-foreground">Manage active sessions and account deletion</p>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <SessionsCard />
              <DeleteAccountCard />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}