import { <PERSON>o } from "hono";
import { zValidator } from "@hono/zod-validator";
import { createTaskSchema, updateTaskSchema } from "./task-type";
import prisma from "@/lib/prisma";
import { UserVariable } from "../..";
import { privateRoutesMiddleware } from "@/server/private/middleware";
import { z } from "zod";

// Simple in-memory store for task ID mapping during sync
const taskMappingStore = new Map<string, Record<string, string>>();

// Helper function to get task mapping for a user
export const getTaskMapping = (userId: string): Record<string, string> | undefined => {
  return taskMappingStore.get(userId);
};

// Helper function to clear task mapping for a user (called after session sync)
export const clearTaskMapping = (userId: string): void => {
  taskMappingStore.delete(userId);
};

// Schema for bulk transfer
const bulkTransferTaskSchema = z.object({
  tasks: z.array(z.object({
    localId: z.string(), // Add local task ID for mapping
    title: z.string(),
    completed: z.string(),
    createdAt: z.string(),
    updatedAt: z.string(),
  }))
});

const app = new Hono<{ Variables: UserVariable }>()
  // Get all tasks for the current user
  .get("/", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    
    // Filters
    const isCompleted = c.req.query("completed") === "true" ? true : c.req.query("completed") === "false" ? false : undefined;
    const status = c.req.query("status") as "TODO" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED" | undefined;
    const priority = c.req.query("priority") as "LOW" | "MEDIUM" | "HIGH" | "URGENT" | undefined;
    
    // Date filters
    const scheduledDate = c.req.query("scheduledDate");
    
    const filters: Record<string, unknown> = {
      userId: user.id
    };

    if (isCompleted !== undefined) {
      filters.completed = isCompleted;
    }

    if (status) {
      filters.status = status;
    }
    
    if (priority) {
      filters.priority = priority;
    }
    
    if (scheduledDate) {
      // For scheduled date, we need to get all tasks scheduled for that day
      const date = new Date(scheduledDate);
      const nextDay = new Date(date);
      nextDay.setDate(nextDay.getDate() + 1);
      
      filters.scheduledDate = {
        gte: date,
        lt: nextDay,
      };
    }
    
    // Get tasks sorted by completion status and creation date
    const tasks = await prisma.task.findMany({
      where: filters,
      include: {
        pomodoroSessions: {
          orderBy: {
            startTime: 'desc',
          },
        },
      },
      orderBy: [
        {
          completed: 'asc', // incomplete tasks first (false comes before true)
        },
        {
          createdAt: 'asc', // oldest first within each group (new tasks at bottom)
        }
      ]
    });

    return c.json({ data: tasks });
  })

  // Get single task
  .get("/:id", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");

    const task = await prisma.task.findUnique({
      where: { id },
      include: {
        pomodoroSessions: {
          orderBy: {
            startTime: 'desc',
          },
        },
      },
    });

    if (!task) {
      return c.json({ error: "Task not found" }, 404);
    }

    // Check if user has permission to access this task
    if (task.userId !== user.id) {
      return c.json({ error: "Unauthorized" }, 403);
    }

    return c.json({ data: task });
  })

  // Create task
  .post("/", privateRoutesMiddleware, zValidator("form", createTaskSchema), async (c) => {
    const user = c.get("user");
    const data = c.req.valid("form");

    // Find the highest order value for a task
    const highestOrderTask = await prisma.task.findFirst({
      where: {
        userId: user.id,
      }
    });


    // Create task
    const task = await prisma.task.create({
      data: {
        ...data,
        userId: user.id,
      },
    });

    return c.json({ data: task }, 201);
  })

  // Update task
  .patch("/:id", privateRoutesMiddleware, zValidator("form", updateTaskSchema), async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");
    const updates = c.req.valid("form");

    // First check if task exists
    const existingTask = await prisma.task.findUnique({
      where: { id, userId: user.id },
    });

    if (!existingTask) {
      return c.json({ error: "Task not found" }, 404);
    }

    // Check if user has permission to update
    if (existingTask.userId !== user.id) {
      return c.json({ error: "Unauthorized to update this task" }, 403);
    }

    // Update the task
    const updatedTask = await prisma.task.update({
      where: { id },
      data: updates,
      include: {
        pomodoroSessions: {
          orderBy: {
            startTime: 'desc',
          },
        },
      },
    });

    return c.json({ data: updatedTask });
  })

  // Delete task
  .delete("/:id", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");

    // Find the task
    const task = await prisma.task.findUnique({
      where: { id, userId: user.id },
    });

    if (!task) {
      return c.json({ error: "Task not found" }, 404);
    }

    // Check permissions
    if (task.userId !== user.id) {
      return c.json({ error: "Unauthorized to delete this task" }, 403);
    }

    // Delete task (this will also delete related pomodoro sessions due to cascade)
    await prisma.task.delete({
      where: { id },
    });

    return c.json({ success: true });
  })

  // Bulk transfer local tasks to database
  .post("/bulk-transfer", privateRoutesMiddleware, zValidator("json", bulkTransferTaskSchema), async (c) => {
    const user = c.get("user");
    const { tasks } = c.req.valid("json");

    if (!user) {
      return c.json({ error: "Unauthorized" }, 403);
    }

    let transferred = 0;
    let skipped = 0;
    const taskMapping: Array<{ 
      originalIndex: number; 
      localId: string;
      id: string; 
      title: string 
    }> = [];

    // Store local ID to database ID mapping for pomodoro session sync
    const localToDbMapping: Record<string, string> = {};

    for (let i = 0; i < tasks.length; i++) {
      const taskData = tasks[i];
      try {
        // Validate task data
        if (!taskData.title || taskData.title.trim().length === 0) {
          skipped++;
          continue;
        }

        // Prepare data for Prisma
        const sanitizedData = {
          title: taskData.title.trim(),
          completed: taskData.completed === 'true',
          createdAt: new Date(taskData.createdAt),
          updatedAt: new Date(taskData.updatedAt),
          user: {
            connect: { id: user.id }
          },
        };

        // Create the task
        const createdTask = await prisma.task.create({
          data: sanitizedData,
        });

        // Store mapping for response and for later pomodoro session mapping
        taskMapping.push({
          originalIndex: i,
          localId: taskData.localId,
          id: createdTask.id,
          title: createdTask.title,
        });

        localToDbMapping[taskData.localId] = createdTask.id;
        transferred++;

      } catch (error) {
        console.error('Error transferring task:', error);
        skipped++;
      }
    }

    // Store the mapping temporarily for the user session
    taskMappingStore.set(user.id, localToDbMapping);

    // Set expiration for the mapping (5 minutes should be enough for sync)
    setTimeout(() => {
      taskMappingStore.delete(user.id);
    }, 5 * 60 * 1000); // 5 minutes

    return c.json({
      data: {
        transferred,
        skipped,
        taskMapping,
        localToDbMapping // Include the mapping in response for immediate use
      }
    });
  })
  
  // Reorder tasks
  // .patch("/reorder", privateRoutesMiddleware, async (c) => {
  //   const user = c.get("user");
  //   const { taskOrders } = await c.req.json<{
  //     taskOrders: Array<{ id: string; order: number }>
  //   }>();

  //   if (!user) {
  //     return c.json({ error: "User not authenticated" }, 401);
  //   }

  //   if (!taskOrders || !Array.isArray(taskOrders) || taskOrders.length === 0) {
  //     return c.json({ error: "Invalid request format" }, 400);
  //   }

  //   // Get all tasks to be reordered
  //   const taskIds = taskOrders.map(t => t.id);
  //   const tasks = await prisma.task.findMany({
  //     where: {
  //       id: { in: taskIds }
  //     }
  //   });

  //   if (tasks.length !== taskOrders.length) {
  //     return c.json({ error: "One or more tasks not found" }, 404);
  //   }

  //   // Verify permissions - make sure user owns all tasks
  //   const hasPermission = tasks.every(task => task.userId === user.id);

  //   if (!hasPermission) {
  //     return c.json({ error: "Unauthorized to reorder one or more tasks" }, 403);
  //   }

  //   // Perform reordering in a transaction
  //   const updates = await prisma.$transaction(
  //     taskOrders.map(({ id, order }) =>
  //       prisma.task.update({
  //         where: { id },
  //         data: { order },
  //       })
  //     )
  //   );

  //   return c.json({ 
  //     data: { 
  //       success: true,
  //       count: updates.length 
  //     } 
  //   });
  // });

export default app; 