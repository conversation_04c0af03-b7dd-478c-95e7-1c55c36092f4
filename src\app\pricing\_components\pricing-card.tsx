'use client';

import { motion } from 'framer-motion';
import { Check, X, Crown, Zap } from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface PricingPlan {
  id: string;
  name: string;
  price: number;
  period: string;
  originalPrice?: number;
  description: string;
  features: string[];
  limitations?: string[];
  buttonText: string;
  buttonVariant: 'default' | 'outline';
  popular: boolean;
  badge?: string;
  monthlyEquivalent?: number;
  savings?: {
    percentage: number;
    amount: number;
  };
}

interface PricingCardProps {
  plan: PricingPlan;
}

export const PricingCard = ({ plan }: PricingCardProps) => {
  const isLifetime = plan.period === 'one-time';
  const isFree = plan.price === 0;
  const isPremium = plan.id === 'premium';

  const handlePlanSelect = () => {
    if (isFree) {
      // Redirect to sign up
      window.location.href = '/auth/sign-up';
    } else {
      // TODO: Integrate with payment system
      console.log(`Selected plan: ${plan.id}`);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="relative h-full pt-4"
    >
      <Card className={cn(
        "h-full flex flex-col relative overflow-visible transition-all duration-300",
        plan.popular
          ? "border-orange-200 shadow-lg shadow-orange-500/10 bg-gradient-to-b from-orange-50/50 to-background scale-105"
          : "border-border hover:border-orange-200/50 hover:shadow-md",
        isLifetime && "border-purple-200 bg-gradient-to-b from-purple-50/30 to-background"
      )}>
        {/* Popular Badge */}
        {plan.popular && (
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-20">
            <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 text-xs font-medium shadow-md">
              <Crown className="h-3 w-3 mr-1" />
              Most Popular
            </Badge>
          </div>
        )}

        {/* Custom Badge */}
        {plan.badge && !plan.popular && (
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-20">
            <Badge variant="secondary" className="px-3 py-1 text-xs font-medium shadow-md">
              {isLifetime && <Zap className="h-3 w-3 mr-1" />}
              {plan.badge}
            </Badge>
          </div>
        )}

        <CardHeader className="text-center pb-4 pt-6">
          <div className="space-y-3">
            <h3 className="text-xl font-bold">{plan.name}</h3>
            <div className="space-y-2">
              <div className="flex items-baseline justify-center gap-1">
                <span className="text-3xl md:text-4xl font-bold">
                  ${plan.price}
                </span>
                {!isFree && (
                  <span className="text-muted-foreground text-sm">
                    /{plan.period}
                  </span>
                )}
              </div>

              {/* Simplified pricing display for annual billing */}
              {plan.monthlyEquivalent && (
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">
                    ~${plan.monthlyEquivalent}/month
                  </p>
                  {plan.savings && (
                    <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs">
                      Save {plan.savings.percentage}%
                    </Badge>
                  )}
                </div>
              )}

              {/* Original price comparison for annual - simplified */}
              {plan.originalPrice && (
                <p className="text-sm text-muted-foreground">
                  <span className="line-through">${plan.originalPrice}/{plan.period}</span>
                </p>
              )}
            </div>
            <p className="text-sm text-muted-foreground leading-relaxed">
              {plan.description}
            </p>
          </div>
        </CardHeader>

        <CardContent className="flex-1 flex flex-col">
          {/* Features List */}
          <div className="space-y-3 mb-6 flex-1">
            {plan.features.map((feature, index) => (
              <div key={index} className="flex items-start gap-3">
                <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-foreground">{feature}</span>
              </div>
            ))}
            
            {/* Limitations for free plan */}
            {plan.limitations && plan.limitations.map((limitation, index) => (
              <div key={`limitation-${index}`} className="flex items-start gap-3">
                <X className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                <span className="text-sm text-muted-foreground">{limitation}</span>
              </div>
            ))}
          </div>

          {/* CTA Button */}
          <Button
            onClick={handlePlanSelect}
            variant={plan.buttonVariant}
            size="lg"
            className={cn(
              "w-full font-medium",
              isPremium && plan.buttonVariant === 'default' &&
              "bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg",
              isLifetime && plan.buttonVariant === 'default' &&
              "bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white"
            )}
          >
            {plan.buttonText}
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
};
