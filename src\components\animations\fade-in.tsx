'use client';

import { motion, AnimatePresence, TargetAndTransition } from 'framer-motion';
import { ReactNode } from 'react';

interface FadeInProps {
  children: ReactNode;
  duration?: number;
  delay?: number;
  className?: string;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
  distance?: number;
  customKey?: string;
}

export function FadeIn({
  children,
  duration = 0.5,
  delay = 0,
  className = '',
  direction = 'none',
  distance = 20,
  customKey,
}: FadeInProps) {
  // Define initial and animate states
  const initial: TargetAndTransition = { opacity: 0 };
  const animate: TargetAndTransition = { opacity: 1 };

  // Add direction-based transforms if specified
  if (direction === 'up') {
    initial.y = distance;
    animate.y = 0;
  } else if (direction === 'down') {
    initial.y = -distance;
    animate.y = 0;
  } else if (direction === 'left') {
    initial.x = distance;
    animate.x = 0;
  } else if (direction === 'right') {
    initial.x = -distance;
    animate.x = 0;
  }

  return (
    <motion.div
      key={customKey}
      initial={initial}
      animate={animate}
      exit={{ opacity: 0 }}
      transition={{
        duration,
        delay,
        ease: 'easeInOut',
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

interface FadeInContainerProps {
  children: ReactNode;
  mode?: 'wait' | 'sync' | 'popLayout';
}

export function FadeInContainer({
  children,
  mode = 'wait',
}: FadeInContainerProps) {
  return (
    <AnimatePresence mode={mode}>
      {children}
    </AnimatePresence>
  );
} 