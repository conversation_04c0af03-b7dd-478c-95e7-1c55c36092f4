# Spotify Web Playback SDK Setup Guide

## Why You Were Getting 30-Second Previews

The issue you experienced is **expected behavior** with Spotify's iframe embed player. Here's why:

### 🔍 **Root Cause Analysis**

1. **Iframe Embed Limitations**: Your current implementation uses Spotify's iframe embed (`https://open.spotify.com/embed/...`), which has these restrictions:
   - **Always shows 30-second previews only** - Even for Premium subscribers
   - **No authentication** - The iframe doesn't know you're a Premium user
   - **Limited control** - No programmatic playback control
   - **Web-only restrictions** - Designed for content preview, not full playback

2. **Premium vs Free**: The iframe embed treats all users the same regardless of subscription status because it doesn't authenticate with your Spotify account.

### ✅ **The Solution: Spotify Web Playback SDK**

The new implementation uses Spotify's **Web Playback SDK**, which provides:
- **Full-length playback** for Premium subscribers
- **Complete player controls** (play, pause, skip, volume, seek)
- **Real-time track information** (artwork, metadata, progress)
- **Proper authentication** with your Spotify account
- **Device integration** (appears in Spotify Connect)

## 🚀 Setup Instructions

### Step 1: Create Spotify App

1. Go to [Spotify for Developers Dashboard](https://developer.spotify.com/dashboard)
2. Click **"Create app"**
3. Fill in the form:
   - **App name**: "Pomodoro 365" (or your preferred name)
   - **App description**: "Pomodoro timer with Spotify integration"
   - **Website**: `http://localhost:2604` (for development)
   - **Redirect URI**: `http://localhost:2604/api/spotify/callback`
   - **APIs used**: Select "Web Playback SDK"

4. Accept terms and click **"Save"**

### Step 2: Get Your Credentials

1. In your app dashboard, copy:
   - **Client ID**
   - **Client Secret** (click "View client secret")

### Step 3: Configure Environment Variables

Create a `.env.local` file in your project root:

```bash
# Copy from env.example and add your Spotify credentials
NEXT_PUBLIC_SPOTIFY_CLIENT_ID=your_actual_client_id_here
SPOTIFY_CLIENT_SECRET=your_actual_client_secret_here

# Your existing variables...
POSTGRES_DATABASE_URL=...
BETTER_AUTH_SECRET=...
# etc.
```

### Step 4: Update Redirect URIs for Production

When deploying to production, update your Spotify app settings:

1. Go back to your Spotify app dashboard
2. Click **"Settings"**
3. Add production redirect URI: `https://yourdomain.com/api/spotify/callback`
4. Update your production environment variables:
   ```bash
   NEXTAUTH_URL=https://yourdomain.com
   ```

## 🎵 How to Use

### For Users:

1. **Navigate to Timer Page**: Go to your timer page
2. **Select Spotify Tab**: Click on "Spotify Premium" tab
3. **Authenticate**: Click "Connect Spotify" - this opens a popup for authentication
4. **Wait for Ready**: The player will initialize (shows "Spotify player ready!")
5. **Load Content**: 
   - Paste any Spotify URL (track, album, playlist)
   - Click "Load"
6. **Enjoy Full Playback**: You'll get complete songs, not just previews!

### Supported URL Formats:
- **Tracks**: `https://open.spotify.com/track/4iV5W9uYEdYUVa79Axb7Rh`
- **Albums**: `https://open.spotify.com/album/1DFixLWuPkv3KT3TnV35m3`
- **Playlists**: `https://open.spotify.com/playlist/37i9dQZF1DX0XUsuxWHRQd`
- **Episodes**: `https://open.spotify.com/episode/4rOoJ6Egrf8K2IrywzwOMk`
- **Shows**: `https://open.spotify.com/show/4rOoJ6Egrf8K2IrywzwOMk`
- **Spotify URIs**: `spotify:track:4iV5W9uYEdYUVa79Axb7Rh`

## 🔧 Technical Implementation

### Key Files Added/Modified:

1. **`src/lib/spotify-auth.ts`** - OAuth authentication handler
2. **`src/lib/spotify-sdk.ts`** - Web Playback SDK wrapper
3. **`src/app/api/spotify/callback/route.ts`** - OAuth callback endpoint
4. **`src/app/timer/_components/musics-control/spotify-web-player.tsx`** - New Premium player component
5. **`src/app/timer/_components/musics-control/music-control.tsx`** - Updated to use new player

### Features Implemented:

- ✅ **OAuth Authentication** with automatic token refresh
- ✅ **Full Player Controls** (play, pause, skip, volume, seek)
- ✅ **Real-time Progress** tracking and seeking
- ✅ **Album Artwork** and track metadata display
- ✅ **Error Handling** with user-friendly messages
- ✅ **Responsive Design** with modern UI
- ✅ **Audio Context Integration** (pauses other players when Spotify plays)

## 🚨 Important Requirements

### For Full Playback:
1. **Spotify Premium Subscription** - Required for Web Playback SDK
2. **Browser Support** - Chrome, Firefox, Safari, Edge (modern versions)
3. **HTTPS in Production** - Required by Spotify (localhost works for development)

### Limitations:
- **Premium Only** - Free users cannot use Web Playback SDK
- **Active Device** - Creates a Spotify Connect device in your browser
- **Internet Required** - Streams content from Spotify servers

## 🐛 Troubleshooting

### Common Issues:

1. **"Player not ready"**
   - Ensure you're authenticated
   - Check browser console for errors
   - Verify environment variables are set

2. **"Authentication failed"**
   - Verify Client ID and Secret are correct
   - Check redirect URI matches exactly
   - Ensure you have Premium subscription

3. **"Failed to play content"**
   - Make sure Spotify URL is valid
   - Check if content is available in your region
   - Try refreshing the authentication

4. **No sound**
   - Check browser permissions for audio
   - Verify volume is not muted
   - Check Spotify Connect devices

### Development Notes:

- **Hot Reload**: Player state persists across development reloads
- **Console Logs**: Check browser console for detailed error messages
- **Network Tab**: Monitor API calls for debugging authentication issues

## 🔄 Migration from Old Implementation

The old iframe-based player is now replaced with the new Web Playback SDK implementation. Users will see:

- **Better UI** with actual player controls
- **Full-length playback** instead of 30-second previews
- **Real-time information** about currently playing tracks
- **Improved integration** with other audio players

The iframe embed approach was a limitation of that method, not a bug - this new implementation is the proper solution for Premium users who want full playback functionality.

## 📚 Additional Resources

- [Spotify Web Playback SDK Documentation](https://developer.spotify.com/documentation/web-playback-sdk)
- [Spotify OAuth Guide](https://developer.spotify.com/documentation/general/guides/authorization/)
- [Web API Reference](https://developer.spotify.com/documentation/web-api/reference/)

---

**Note**: This implementation follows Spotify's terms of service and developer guidelines. The Web Playback SDK is intended for personal, non-commercial use cases like this Pomodoro timer application. 