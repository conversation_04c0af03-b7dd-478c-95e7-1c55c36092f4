import { Hono } from "hono";
import { cors } from "hono/cors";
import { UserVariable } from "../..";
import { Polar } from "@polar-sh/sdk";
import { privateRoutesMiddleware } from "@/server/private/middleware";
import { Webhooks } from "@polar-sh/nextjs";
import { NextRequest } from "next/server";
import prisma from "@/lib/prisma";
import { SubscriptionStatus, SubscriptionInterval, SubscriptionType } from "@prisma/client";
import { Prisma } from "@prisma/client";

// Simple logger implementation
const logger = {
  info: (message: string, ...args: unknown[]) => console.log(`[INFO] ${message}`, ...args),
  error: (message: string, ...args: unknown[]) => console.error(`[ERROR] ${message}`, ...args),
  warn: (message: string, ...args: unknown[]) => console.warn(`[WARN] ${message}`, ...args),
};

// Define interface for Polar webhook data
interface PolarWebhookData {
  id: string;
  status: string;
  customerExternalId?: string;
  customer?: {
    id: string;
    externalId: string;
    email: string;
    name: string;
    [key: string]: unknown;
  };
  productId?: string;
  product?: {
    id: string;
    name: string;
    description: string;
    recurringInterval: string;
    isRecurring: boolean;
  };
  productPrice?: {
    id: string;
    priceAmount: number;
    priceCurrency: string;
    recurringInterval: string;
    type: string;
  };
  prices?: Array<{
    id: string;
    priceAmount: number;
    priceCurrency: string;
    recurringInterval?: string;
    type?: string;
    [key: string]: unknown;
  }>;
  checkoutId?: string;
  currentPeriodStart?: string | number | Date;
  currentPeriodEnd?: string | number | Date;
  rawData?: {
    checkoutId?: string;
    [key: string]: unknown;
  };
  // For customer events
  externalId?: string;
  email?: string;
  name?: string;
  [key: string]: unknown;
}

// Simplified webhook event types
type PolarWebhookEventType = 
  | "subscription.created"
  | "subscription.updated"
  | "subscription.canceled"
  | "subscription.active"
  | "checkout.created"
  | "checkout.updated"
  | "checkout.succeeded"
  | "customer.created"
  | "customer.updated"
  | "customer.deleted"
  | "customer.state_changed"
  | "order.created"
  | "order.paid"
  | "order.updated";

// Create Polar API client
const polarApi = new Polar({
  accessToken: process.env.POLAR_ACCESS_TOKEN!,
  server: process.env.NEXT_PUBLIC_POLAR_ENV === "production" ? "production" : "sandbox",
});

const app = new Hono<{ Variables: UserVariable }>()
  // Add CORS middleware to all routes
  .use("/*", cors({
    origin: process.env.NODE_ENV === "production" 
      ? process.env.NEXT_PUBLIC_APP_URL || "*" 
      : "*", // Restrict to app URL in production
    allowMethods: ['GET', 'POST', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization', 'polar-signature'],
    exposeHeaders: ['Content-Length'],
    maxAge: 86400,
  }))
  .get("/checkout", privateRoutesMiddleware, async (c) => {
    try {
      const productId = c.req.query("productId");
      
      if (!productId) {
        return c.json({ error: "Missing productId" }, 400);
      }
      
      // Get user from variables (set by middleware)
      const user = c.get("user");
      
      if (!user) {
        return c.json({ error: "Unauthorized" }, 401);
      }
      
      logger.info(`User ${user.id} initiating checkout for product: ${productId}`);
      
      // For Polar, we need a fully qualified URL without placeholders
      const appUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3045";
      const successUrl = `${appUrl}/confirmation?checkoutId={CHECKOUT_ID}`;
      
      try {
        // Create checkout session with Polar SDK
        const checkoutSession = await polarApi.checkouts.create({
          products: [productId],
          successUrl,
          customerEmail: user.email,
          customerName: user.name,
          customerExternalId: user.id,
        });
        
        // Return the checkout URL to avoid CORS issues
        return c.json({ 
          checkout_url: checkoutSession.url,
          checkout_id: checkoutSession.id
        });
      } catch (error) {
        // Check if it's a token expiration issue
        const isTokenExpired = 
          error instanceof Error && 
          (error.message.includes("invalid_token") || 
           error.message.includes("expired") || 
           error.message.includes("401"));
        
        if (isTokenExpired) {
          logger.error("Polar API token expired or invalid. Please refresh the token.");
          return c.json({ 
            error: "Payment provider authentication failed", 
            details: "Please contact support to resolve this issue.",
            code: "TOKEN_EXPIRED"
          }, 401);
        }
        
        // Other API errors
        logger.error("Polar API error details:", error);
        return c.json({ 
          error: "Failed to create checkout session", 
          details: error instanceof Error ? error.message : String(error)
        }, 500);
      }
    } catch (error) {
      logger.error("Polar checkout error:", error);
      return c.json({ error: "Failed to create checkout session" }, 500);
    }
  })
  .post("/webhooks", async (c) => {
    // We'll manually handle the request to adapt it for the Webhooks handler
    const req = c.req.raw as NextRequest;
    
    try {
      // To prevent duplicate webhook processing
      const processedEvents = new Set<string>();
      
      // Call the Webhooks handler directly with the raw request
      const response = await Webhooks({
        webhookSecret: process.env.POLAR_WEBHOOK_SECRET!,
        onPayload: async (payload) => {
          const eventType = payload.type as PolarWebhookEventType;
          const data = payload.data as PolarWebhookData;
          const eventId = `${eventType}-${data.id}`;

          // Skip if we've already processed this exact event
          if (processedEvents.has(eventId)) {
            logger.info(`Skipping duplicate event: ${eventId}`);
            return;
          }
          
          // Mark this event as processed
          processedEvents.add(eventId);

          console.log("eventType", eventType);
          console.log("payload data", JSON.stringify(data));
          
          try {
            // Handle different event types
            switch (eventType) {
              case "subscription.created": {
                console.log("subscription.created", data);
                await handleSubscriptionCreated(data);
                break;
              }
              
              case "subscription.updated": {
                console.log("subscription.updated", data);
                await handleSubscriptionUpdated(data);
                break;
              }
              
              case "subscription.canceled": {
                console.log("subscription.canceled", data);
                await handleSubscriptionCanceled(data);
                break;
              }

              case "subscription.active": {
                console.log("subscription.active", data);
                // Handle active subscription event if needed
                // Often similar to subscription.updated
                await handleSubscriptionUpdated(data);
                break;
              }
              
              case "checkout.created":
              case "checkout.updated":
              case "checkout.succeeded": {
                console.log(`${eventType}`, data);
                // Store checkout information
                await handleCheckoutEvent(eventType, data);
                break;
              }
              
              case "customer.created":
              case "customer.updated":
              case "customer.deleted":
              case "customer.state_changed": {
                console.log(`${eventType}`, data);
                // Handle customer-related events
                await handleCustomerEvent(eventType, data);
                break;
              }

              case "order.created":
              case "order.paid":
              case "order.updated": {
                console.log(`${eventType}`, data);
                // Handle order-related events
                await handleOrderEvent(eventType, data);
                break;
              }
              
              default: {
                logger.info(`Unhandled webhook event type: ${eventType}`);
                break;
              }
            }
          } catch (error) {
            logger.error(`Error processing webhook event ${eventType}:`, error);
            // We don't throw here to avoid rejecting the webhook
          }
        }
      })(req);
      
      // Return the response from Webhooks handler
      return response;
    } catch (error) {
      logger.error("Webhook handling error:", error);
      return c.json({ error: "Webhook processing failed" }, 500);
    }
  });

// Simple helper to safely extract the checkoutId from webhook data
function extractCheckoutId(data: PolarWebhookData): string | undefined {
  // Direct property
  if (data.checkoutId) {
    return data.checkoutId;
  }
  
  // Look for nested checkoutId based on the example payload
  if (typeof data === 'object' && data !== null) {
    // Try to find it in the root object properties
    for (const key in data) {
      if (key === 'checkoutId' && typeof data[key] === 'string') {
        return data[key] as string;
      }
    }
    
    // If there's a rawData property, check inside it
    const rawData = data.rawData as Record<string, unknown> | undefined;
    if (rawData && typeof rawData === 'object' && rawData !== null) {
      if (typeof rawData.checkoutId === 'string') {
        return rawData.checkoutId;
      }
    }
  }
  
  return undefined;
}

async function handleSubscriptionCreated(data: PolarWebhookData): Promise<void> {
  try {
    const { 
      id: externalId, 
      status,
      productId,
      customer,
      product
    } = data;

    // Check if subscription already exists - prevent duplicates
    const existingSubscription = await prisma.subscription.findFirst({
      where: { 
        OR: [
          { externalId },
          { checkoutId: data.checkoutId as string }
        ]
      }
    });

    if (existingSubscription) {
      logger.info(`Subscription already exists with ID ${existingSubscription.id}, external ID ${externalId}. Updating instead of creating.`);
      // Use the update function instead to refresh the data
      await handleSubscriptionUpdated(data);
      return;
    }

    // Extract customer external ID from the customer object
    const userId = customer?.externalId;
    
    // Skip if we don't have the required data
    if (!product || !userId) {
      logger.error(`Missing required data for subscription creation`, { product, userId, data });
      return;
    }
    
    const polarInterval = mapPolarIntervalToSubscriptionInterval(product.recurringInterval);
    
    // Get price information from the first price in the prices array or productPrice
    const priceInfo = data.prices?.[0] || data.productPrice;
    const priceAmount = priceInfo?.priceAmount || 0;
    const priceCurrency = priceInfo?.priceCurrency || 'USD';
    
    // Determine subscription type based on product name
    const subscriptionType = product.name as SubscriptionType;
    
    // Get checkoutId from the webhook data
    const checkoutIdToSave = extractCheckoutId(data);
    
    // Log the checkout ID being saved
    logger.info(`Saving checkoutId: ${checkoutIdToSave}`);
    
    // Create a new subscription record with minimal fields
    const subscription = await prisma.subscription.create({
      data: {
        userId,
        status: mapPolarStatusToSubscriptionStatus(status),
        currentPeriodStart: data.currentPeriodStart ? new Date(data.currentPeriodStart as string | number | Date) : new Date(),
        currentPeriodEnd: data.currentPeriodEnd ? new Date(data.currentPeriodEnd as string | number | Date) : getEndDateFromInterval(polarInterval),
        externalId,
        checkoutId: checkoutIdToSave,
        planId: productId as string,
        productId: productId as string, // Save the product ID
        priceId: priceInfo?.id as string, // Save the price ID
        price: priceAmount / 100, // Convert from cents to dollars
        currency: priceCurrency.toUpperCase(),
        interval: polarInterval, // Save the interval
        subscriptionType, // Set the subscription type
        // Store all other data in metadata
        polarMetadata: toPrismaJson({
          productId,
          priceId: priceInfo?.id,
          subscriptionType: product.name,
          price: priceAmount / 100,
          currency: priceCurrency.toUpperCase(),
          polarInterval,
          rawData: data
        }),
      }
    });
    
    // Update user's subscription type
    await prisma.user.update({
      where: { id: userId },
      data: {
        subscriptionType,
        premium: subscriptionType !== SubscriptionType.FREE
      }
    });
    
    logger.info(`Subscription created: ${subscription.id}`);
  } catch (error) {
    logger.error("Error handling subscription.created:", error);
  }
}

async function handleSubscriptionUpdated(data: PolarWebhookData): Promise<void> {
  try {
    const { 
      id: externalId, 
      status,
      product,
      productId,
      checkoutId
    } = data;
    
    // Find the subscription by external ID
    const subscription = await prisma.subscription.findFirst({
      where: { 
        OR: [
          { externalId },
          { checkoutId: checkoutId as string }
        ]
      }
    });
    
    if (!subscription) {
      logger.error(`Subscription not found with external ID: ${externalId}${checkoutId ? ` or checkout ID: ${checkoutId}` : ''}`);
      
      // If we have customer info, we can try to create the subscription
      if (data.customer?.externalId) {
        logger.info(`Creating new subscription for update event with external ID: ${externalId}`);
        await handleSubscriptionCreated(data);
      }
      
      return;
    }
    
    // Map Polar status to our status enum
    const subscriptionStatus = mapPolarStatusToSubscriptionStatus(status);
    
    // Determine subscription type if product is available
    let subscriptionType = undefined;
    
    if (product) {
      subscriptionType = product.name as SubscriptionType;
    }
    
    // Get checkoutId from the webhook data
    const checkoutIdToSave = extractCheckoutId(data);
    
    // Log the checkout ID being saved
    if (checkoutIdToSave) {
      logger.info(`Updating subscription with checkoutId: ${checkoutIdToSave}`);
    }
    
    // Get price information from the first price in the prices array or productPrice
    const priceInfo = data.prices?.[0] || data.productPrice;
    const polarInterval = product ? mapPolarIntervalToSubscriptionInterval(product.recurringInterval) : undefined;
    
    // Get current metadata to preserve it
    const currentMetadata = subscription.polarMetadata || {};
    
    // Prepare the merged metadata 
    let mergedMetadata: Record<string, unknown> = {};
    
    try {
      // If we already have structured metadata fields, keep them
      if (typeof currentMetadata === 'object' && currentMetadata !== null) {
        mergedMetadata = { ...currentMetadata as Record<string, unknown> };
      }
      
      // Add or update with the new data
      mergedMetadata.rawData = data;
      
      // Add structured fields if available
      if (productId) mergedMetadata.productId = productId;
      if (priceInfo?.id) mergedMetadata.priceId = priceInfo.id;
      if (product?.name) mergedMetadata.subscriptionType = product.name;
      if (priceInfo?.priceAmount) mergedMetadata.price = (priceInfo.priceAmount as number) / 100;
      if (priceInfo?.priceCurrency) mergedMetadata.currency = (priceInfo.priceCurrency as string).toUpperCase();
      if (polarInterval) mergedMetadata.polarInterval = polarInterval;
    } catch (error) {
      logger.error("Error merging metadata:", error);
      // Fallback to just the raw data
      mergedMetadata = { rawData: data };
    }
    
    // Prepare update data
    const updateData: Prisma.SubscriptionUpdateInput = {
      status: subscriptionStatus,
      updatedAt: new Date(),
      externalId, // Always update external ID to ensure it's correct
      polarMetadata: toPrismaJson(mergedMetadata),
    };

    // Add checkoutId if available
    if (checkoutIdToSave) {
      updateData.checkoutId = checkoutIdToSave;
    }

    // Add productId if available
    if (productId) {
      updateData.productId = productId as string;
      updateData.planId = productId as string;
    }

    // Add price information if available
    if (priceInfo) {
      updateData.priceId = priceInfo.id as string;
      updateData.price = (priceInfo.priceAmount as number) / 100; // Convert from cents to dollars
      updateData.currency = (priceInfo.priceCurrency as string).toUpperCase();
    }

    // Add interval if available
    if (polarInterval) {
      updateData.interval = polarInterval;
    }

    // Add subscription type if available
    if (subscriptionType) {
      updateData.subscriptionType = subscriptionType;
    }

    // Add period info if available
    if (data.currentPeriodStart) {
      updateData.currentPeriodStart = new Date(data.currentPeriodStart as string | number | Date);
    }
    
    if (data.currentPeriodEnd) {
      updateData.currentPeriodEnd = new Date(data.currentPeriodEnd as string | number | Date);
    }
    
    // Update the subscription
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: updateData
    });
    
    // Update user's subscription type if needed
    if (subscriptionType) {
      await prisma.user.update({
        where: { id: subscription.userId },
        data: {
          subscriptionType,
          premium: subscriptionType !== SubscriptionType.FREE
        }
      });
    }
    
    logger.info(`Subscription updated: ${subscription.id}`);
  } catch (error) {
    logger.error("Error handling subscription.updated:", error);
  }
}

async function handleSubscriptionCanceled(data: PolarWebhookData) {
  try {
    const { id: externalId } = data;
    
    // Find the subscription by external ID
    const subscription = await prisma.subscription.findFirst({
      where: { externalId }
    });
    
    if (!subscription) {
      logger.error(`Subscription not found with external ID: ${externalId}`);
      return;
    }
    
    // Update the subscription status
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        status: SubscriptionStatus.CANCELED,
        canceledAt: new Date(),
        updatedAt: new Date(),
      }
    });
    
    // Revert user to FREE subscription type
    await prisma.user.update({
      where: { id: subscription.userId },
      data: {
        subscriptionType: SubscriptionType.FREE,
        premium: false
      }
    });
    
    logger.info(`Subscription canceled: ${subscription.id}`);
  } catch (error) {
    logger.error("Error handling subscription.canceled:", error);
  }
}

/**
 * Handle checkout-related webhook events
 */
async function handleCheckoutEvent(eventType: PolarWebhookEventType, data: PolarWebhookData) {
  try {
    // Extract checkout ID and status (if needed)
    const { id: checkoutId } = data;
    
    // Extract customer information if available
    const customerExternalId = data.customerExternalId;
    
    if (!checkoutId) {
      logger.error("Missing checkout ID in checkout event data");
      return;
    }
    
    // Try to find any existing subscription that might be connected to this checkout
    const subscription = await prisma.subscription.findFirst({
      where: { checkoutId }
    });
    
    // If this is the first time we're seeing this checkout and we have a user ID,
    // we can create a temporary subscription record or update an existing one
    if (customerExternalId && eventType === "checkout.created") {
      // For checkout.created, we might want to create a pending subscription
      // or just log the checkout for future reference
      logger.info(`New checkout ${checkoutId} created for user ${customerExternalId}`);
      
      // Optionally create a minimal subscription record with INCOMPLETE status
      // This is useful if you want to track checkout attempts
      /*
      if (!subscription) {
        await prisma.subscription.create({
          data: {
            userId: customerExternalId,
            checkoutId,
            status: SubscriptionStatus.INCOMPLETE,
            currentPeriodStart: new Date(),
            currentPeriodEnd: new Date(),
            planId: data.productId || "unknown",
            polarMetadata: toPrismaJson({
              checkoutData: data
            })
          }
        });
      }
      */
    } else if (eventType === "checkout.succeeded") {
      // For checkout.succeeded, we can mark the checkout as successful
      // The subscription record will typically be created by the subscription.created event
      logger.info(`Checkout ${checkoutId} succeeded${customerExternalId ? ` for user ${customerExternalId}` : ''}`);
      
      // If we already have a subscription with this checkout ID, we can update it
      if (subscription) {
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            status: SubscriptionStatus.ACTIVE,
            updatedAt: new Date(),
            polarMetadata: toPrismaJson({
              checkoutData: data
            })
          }
        });
      }
    }
  } catch (error) {
    logger.error(`Error handling ${eventType} event:`, error);
  }
}

/**
 * Handle customer-related webhook events
 */
async function handleCustomerEvent(eventType: PolarWebhookEventType, data: PolarWebhookData) {
  try {
    // Extract customer ID and external ID
    const customerId = data.id;
    const customerExternalId = data.externalId || (data.customer?.externalId as string | undefined);
    
    if (!customerId) {
      logger.error("Missing customer ID in customer event data");
      return;
    }
    
    logger.info(`Processing ${eventType} for customer: ${customerId}`);
    
    // In a production system, you might sync customer data with your database
    // or perform other actions based on the event type
    
    // For customer.created, you might want to link the Polar customer ID
    // with your local user ID
    if (eventType === "customer.created" && customerExternalId) {
      // Check if we have a user with this external ID
      const user = await prisma.user.findUnique({
        where: { id: customerExternalId }
      });
      
      if (user) {
        logger.info(`Linked Polar customer ${customerId} with user ${customerExternalId}`);
        // Optionally store the Polar customer ID with the user
      } else {
        logger.warn(`User ${customerExternalId} not found for Polar customer ${customerId}`);
      }
    }
    
    // For customer.deleted, you might want to cancel any active subscriptions
    if (eventType === "customer.deleted" && customerExternalId) {
      logger.info(`Customer ${customerId} deleted, checking for active subscriptions`);
      
      // Find any active subscriptions for this user
      const activeSubscriptions = await prisma.subscription.findMany({
        where: {
          userId: customerExternalId,
          status: {
            in: [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING]
          }
        }
      });
      
      // Cancel active subscriptions
      for (const subscription of activeSubscriptions) {
        logger.info(`Canceling subscription ${subscription.id} due to customer deletion`);
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            status: SubscriptionStatus.CANCELED,
            canceledAt: new Date(),
            updatedAt: new Date(),
          }
        });
      }
      
      // Update user's subscription status
      if (activeSubscriptions.length > 0) {
        await prisma.user.update({
          where: { id: customerExternalId },
          data: {
            subscriptionType: SubscriptionType.FREE,
            premium: false
          }
        });
      }
    }
  } catch (error) {
    logger.error(`Error handling ${eventType} event:`, error);
  }
}

/**
 * Handle order-related webhook events
 */
async function handleOrderEvent(eventType: PolarWebhookEventType, data: Record<string, unknown>) {
  try {
    // Extract order information
    const orderId = data.id as string;
    const status = data.status as string;
    const totalAmount = data.totalAmount as number;
    const currency = (data.currency as string) || 'usd';
    const subscriptionId = data.subscriptionId as string;
    const checkoutId = data.checkoutId as string;
    const customer = data.customer as Record<string, unknown> | undefined;
    
    if (!orderId) {
      logger.error("Missing order ID in order event data");
      return;
    }

    // Map the order status to payment status
    let paymentStatus: PaymentStatus;
    switch (status?.toLowerCase()) {
      case 'paid':
        paymentStatus = PaymentStatus.SUCCEEDED;
        break;
      case 'pending':
      default:
        paymentStatus = PaymentStatus.PENDING;
        break;
    }
    
    // Extract subscription information
    const userId = customer?.externalId as string;
    
    if (!userId) {
      logger.warn(`Missing user ID in order data: ${orderId}`);
    }
    
    logger.info(`Processing ${eventType} for order: ${orderId}, status: ${status}, amount: ${totalAmount/100} ${currency.toUpperCase()}`);
    
    // Check if this payment already exists
    const existingPayment = await prisma.payment.findFirst({
      where: { externalId: orderId }
    });
    
    if (existingPayment) {
      // Update existing payment
      await prisma.payment.update({
        where: { id: existingPayment.id },
        data: {
          status: paymentStatus,
          updatedAt: new Date(),
          // Add or update any other fields as needed
          metadata: toPrismaJson(data)
        }
      });
      
      logger.info(`Updated payment record for order: ${orderId}`);
    } else {
      // Create a new payment record
      // Get product information from data
      const productId = data.productId as string;
      const product = data.product as Record<string, unknown> | undefined;
      
      // Find the subscription if not directly provided
      let subscriptionRecord = null;
      if (subscriptionId) {
        subscriptionRecord = await prisma.subscription.findFirst({
          where: { externalId: subscriptionId }
        });
      } else if (checkoutId) {
        subscriptionRecord = await prisma.subscription.findFirst({
          where: { checkoutId }
        });
      }
      
      // Determine the subscription type
      const subscriptionType = product?.name as string || 
                              subscriptionRecord?.subscriptionType as string ||
                              SubscriptionType.FREE;
      
      // Create the payment record
      await prisma.payment.create({
        data: {
          externalId: orderId,
          amount: totalAmount / 100, // Convert from cents to dollars
          currency: currency.toUpperCase(),
          status: paymentStatus,
          checkoutId,
          productId,
          subscriptionId: subscriptionRecord?.id,
          userId,
          subscriptionType: subscriptionType as SubscriptionType,
          metadata: toPrismaJson(data)
        }
      });
      
      logger.info(`Created payment record for order: ${orderId}`);
      
      // If payment is successful, update subscription's lastPaymentDate
      if (paymentStatus === PaymentStatus.SUCCEEDED && subscriptionRecord) {
        await prisma.subscription.update({
          where: { id: subscriptionRecord.id },
          data: {
            lastPaymentDate: new Date()
          }
        });
        
        logger.info(`Updated lastPaymentDate for subscription: ${subscriptionRecord.id}`);
      }
    }
  } catch (error) {
    logger.error(`Error handling ${eventType} event:`, error);
  }
}

// Helper functions to map Polar statuses to our enums
function mapPolarStatusToSubscriptionStatus(polarStatus: string): SubscriptionStatus {
  switch (polarStatus.toLowerCase()) {
    case 'active':
      return SubscriptionStatus.ACTIVE;
    case 'trialing':
      return SubscriptionStatus.TRIALING;
    case 'past_due':
      return SubscriptionStatus.PAST_DUE;
    case 'canceled':
      return SubscriptionStatus.CANCELED;
    case 'incomplete':
      return SubscriptionStatus.INCOMPLETE;
    default:
      return SubscriptionStatus.INCOMPLETE;
  }
}

// Helper function to map Polar interval to our enum
function mapPolarIntervalToSubscriptionInterval(interval?: string): SubscriptionInterval {
  switch (interval?.toLowerCase()) {
    case 'year':
      return SubscriptionInterval.YEAR;
    case 'month':
    default:
      return SubscriptionInterval.MONTH;
  }
}

// Helper function to calculate end date based on interval
function getEndDateFromInterval(interval: SubscriptionInterval): Date {
  const endDate = new Date();
  
  if (interval === SubscriptionInterval.YEAR) {
    endDate.setFullYear(endDate.getFullYear() + 1);
  } else {
    // Default to month
    endDate.setMonth(endDate.getMonth() + 1);
  }
  
  return endDate;
}

// Helper function to safely convert data to Prisma JSON
function toPrismaJson(data: unknown): Prisma.InputJsonValue {
  return JSON.parse(JSON.stringify(data)) as Prisma.InputJsonValue;
}

// Keeping this function commented out for reference, as we now use product.name directly as SubscriptionType
// function mapProductNameToSubscriptionType(productName: string): SubscriptionType {
//   if (productName.toLowerCase().includes('premium plus')) {
//     return SubscriptionType.PREMIUM_PLUS;
//   } else if (productName.toLowerCase().includes('premium')) {
//     return SubscriptionType.PREMIUM;
//   } else {
//     return SubscriptionType.FREE;
//   }
// }

// Map PaymentStatus enum from Prisma schema
enum PaymentStatus {
  PENDING = "PENDING",
  SUCCEEDED = "SUCCEEDED",
  FAILED = "FAILED"
}

export default app; 