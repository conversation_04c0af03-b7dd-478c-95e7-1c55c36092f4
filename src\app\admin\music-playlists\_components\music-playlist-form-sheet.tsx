"use client";

import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetFooter
} from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2, Image as ImageIcon, Info, Check } from "lucide-react";
import { toast } from "sonner";
import {
  useCreateAdminMusicPlaylist,
  useUpdateAdminMusicPlaylist,
  useGetAdminMusicPlaylist
} from "@schemas/MusicPlaylist/music-playlist-admin-query";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { Toolt<PERSON>, Toolt<PERSON>Content, Toolt<PERSON><PERSON><PERSON>ider, <PERSON><PERSON><PERSON><PERSON>rigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { MUSIC_IMAGE_PRESETS } from "@/config/image-presets";
import { MusicGenre } from "@prisma/client";

interface MusicPlaylistFormSheetProps {
  playlistId?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function MusicPlaylistFormSheet({
  playlistId,
  open,
  onOpenChange,
  onSuccess
}: MusicPlaylistFormSheetProps) {
  const isEditMode = !!playlistId;
  const title = isEditMode ? "Edit Music Playlist (admin)" : "Create Music Playlist (admin)";

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    imageUrl: "",
    isPublic: true,
    genres: [] as MusicGenre[],
  });

  // Form validation state
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Mutations for creating and updating playlists
  const createMusicPlaylistMutation = useCreateAdminMusicPlaylist();
  const updateMusicPlaylistMutation = useUpdateAdminMusicPlaylist();

  // For edit mode, fetch the playlist details
  const musicPlaylistQuery = useGetAdminMusicPlaylist(playlistId);

  // When in edit mode and playlist data is loaded, populate the form
  useEffect(() => {
    if (isEditMode && musicPlaylistQuery.data) {
      setFormData({
        name: musicPlaylistQuery.data.name,
        description: musicPlaylistQuery.data.description || "",
        imageUrl: musicPlaylistQuery.data.imageUrl || "",
        isPublic: musicPlaylistQuery.data.isPublic,
        genres: musicPlaylistQuery.data.genres || [],
      });
    }
  }, [isEditMode, musicPlaylistQuery.data]);

  // Reset form when sheet is opened/closed
  useEffect(() => {
    if (!open) {
      // Reset form state when sheet is closed
      setTimeout(() => {
        if (!isEditMode) {
          setFormData({
            name: "",
            description: "",
            imageUrl: "",
            isPublic: true,
            genres: [],
          });
        }
        setErrors({});
        setTouched({});
      }, 300); // Wait for close animation
    }
  }, [open, isEditMode]);

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Mark field as touched
    if (!touched[name]) {
      setTouched(prev => ({ ...prev, [name]: true }));
    }

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Handle genre changes
  const handleGenreChange = (genre: MusicGenre, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      genres: checked
        ? [...prev.genres, genre]
        : prev.genres.filter(g => g !== genre)
    }));
  };

  // Handle blur event for validation
  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));
    validateField(name, formData[name as keyof typeof formData]);
  };

  // Validate a single field
  const validateField = (name: string, value: string | boolean | MusicGenre[]) => {
    if (name === 'name' && typeof value === 'string' && !value.trim()) {
      setErrors(prev => ({ ...prev, [name]: "Name is required" }));
      return false;
    }

    if (name === 'imageUrl' && typeof value === 'string' && value.trim()) {
      try {
        new URL(value);
      } catch {
        setErrors(prev => ({ ...prev, [name]: "Please enter a valid URL" }));
        return false;
      }
    }

    // Clear any existing errors for this field if validation passes
    setErrors(prev => ({ ...prev, [name]: "" }));
    return true;
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    // Mark all fields as touched
    const newTouched = {
      name: true,
      description: true,
      imageUrl: true,
      isPublic: true,
      genres: true
    };
    setTouched(newTouched);

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
      isValid = false;
    }

    if (formData.imageUrl && formData.imageUrl.trim()) {
      try {
        new URL(formData.imageUrl);
      } catch {
        newErrors.imageUrl = "Please enter a valid URL";
        isValid = false;
      }
    }

    setErrors(newErrors);
    return isValid;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Please fix the errors in the form");
      return;
    }

    // Prepare form data, ensuring empty strings are properly handled
    const preparedData = {
      name: formData.name,
      description: formData.description.trim() || "",
      imageUrl: formData.imageUrl.trim() || "",
      isPublic: formData.isPublic.toString(),
      genres: formData.genres,
    };

    if (isEditMode && playlistId) {
      updateMusicPlaylistMutation.mutate(
        {
          form: preparedData,
          param: { id: playlistId },
        },
        {
          onSuccess: () => {
            onSuccess?.();
          },
          onError: (error) => {
            toast.error(`Failed to update music playlist: ${error.message}`);
          },
        }
      );
    } else {
      createMusicPlaylistMutation.mutate(
        {
          form: preparedData,
        },
        {
          onSuccess: (response) => {
            const newPlaylistId = response.data.id;
            toast.success("Music playlist created successfully");
            onSuccess?.();
            window.location.href = `/admin/music-playlists/${newPlaylistId}`;
          },
          onError: (error) => {
            toast.error(`Failed to create music playlist: ${error.message}`);
          },
        }
      );
    }
  };

  // Check if loading or submitting
  const isLoading =
    (isEditMode && musicPlaylistQuery.isLoading) ||
    createMusicPlaylistMutation.isPending ||
    updateMusicPlaylistMutation.isPending;

  // Preview image URL if valid
  const isValidImageUrl = formData.imageUrl && !errors.imageUrl;
  const showImagePreview = isValidImageUrl && touched.imageUrl;

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-md md:max-w-lg lg:max-w-xl w-full p-0 focus:outline-none">
        <div className="h-full flex flex-col">
          <SheetHeader className="px-6 pt-6 pb-2 border-b">
            <div className="flex items-center justify-between">
              <SheetTitle className="text-xl font-semibold">{title}</SheetTitle>
              {isEditMode && (
                <Badge variant="outline" className="bg-blue-50 dark:bg-blue-900/20">
                  Editing
                </Badge>
              )}
            </div>
            <SheetDescription>
              {isEditMode
                ? "Update your music playlist details below"
                : "Fill in the details to create a new music playlist"
              }
            </SheetDescription>
          </SheetHeader>

          <div className="flex-1 overflow-y-auto px-6">
            <form id="music-playlist-form" onSubmit={handleSubmit} className="space-y-6 py-6">
              {/* Name field */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label
                    htmlFor="name"
                    className={cn(
                      "font-medium",
                      errors.name && touched.name ? "text-destructive" : ""
                    )}
                  >
                    Name <span className="text-destructive">*</span>
                  </Label>
                  {touched.name && errors.name && (
                    <span className="text-xs text-destructive">{errors.name}</span>
                  )}
                </div>
                <Input
                  id="name"
                  name="name"
                  placeholder="Enter music playlist name"
                  value={formData.name}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  className={cn(
                    errors.name && touched.name ? "border-destructive ring-destructive" : "",
                    "transition-all"
                  )}
                  aria-required="true"
                  aria-invalid={errors.name && touched.name ? "true" : "false"}
                  autoComplete="off"
                />
              </div>

              {/* Description field */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="description" className="font-medium">
                    Description
                  </Label>
                  <span className="text-xs text-muted-foreground">Optional</span>
                </div>
                <Textarea
                  id="description"
                  name="description"
                  placeholder="Enter music playlist description"
                  value={formData.description}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  rows={3}
                  className="resize-none"
                />
              </div>

              {/* Genres field */}
              <div className="space-y-3">
                <Label className="font-medium">Genres</Label>
                <div className="grid grid-cols-2 gap-3">
                  {Object.values(MusicGenre).map((genre) => (
                    <div key={genre} className="flex items-center space-x-2">
                      <Checkbox
                        id={`genre-${genre}`}
                        checked={formData.genres.includes(genre)}
                        onCheckedChange={(checked) => handleGenreChange(genre, checked === true)}
                      />
                      <Label htmlFor={`genre-${genre}`} className="cursor-pointer">
                        {genre}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Image URL field */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Label
                      htmlFor="imageUrl"
                      className={cn(
                        "font-medium",
                        errors.imageUrl && touched.imageUrl ? "text-destructive" : ""
                      )}
                    >
                      Cover Image URL
                    </Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">Enter a valid URL to an image that will be used as the playlist cover</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  {touched.imageUrl && errors.imageUrl && (
                    <span className="text-xs text-destructive">{errors.imageUrl}</span>
                  )}
                </div>
                <div className="relative">
                  <Input
                    id="imageUrl"
                    name="imageUrl"
                    placeholder="https://example.com/image.jpg"
                    value={formData.imageUrl}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={cn(
                      "pl-10",
                      errors.imageUrl && touched.imageUrl ? "border-destructive ring-destructive" : "",
                      "transition-all"
                    )}
                    aria-invalid={errors.imageUrl && touched.imageUrl ? "true" : "false"}
                    autoComplete="off"
                  />
                  <ImageIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                </div>

                {/* Image Presets */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">
                    Or choose from presets:
                  </Label>
                  <div className="grid grid-cols-3 gap-2">
                    {MUSIC_IMAGE_PRESETS.map((preset) => (
                      <motion.button
                        key={preset.id}
                        type="button"
                        onClick={() => {
                          setFormData(prev => ({ ...prev, imageUrl: preset.url }));
                          setTouched(prev => ({ ...prev, imageUrl: true }));
                        }}
                        className={cn(
                          "relative aspect-video rounded-md border-2 overflow-hidden transition-all duration-200 hover:scale-105",
                          formData.imageUrl === preset.url
                            ? "border-blue-500 ring-2 ring-blue-500/20"
                            : "border-border hover:border-blue-300"
                        )}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Image
                          src={preset.url}
                          alt={preset.alt}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 33vw, 200px"
                        />
                        {formData.imageUrl === preset.url && (
                          <div className="absolute inset-0 bg-blue-500/20 flex items-center justify-center">
                            <div className="bg-blue-500 text-white rounded-full p-1">
                              <Check className="h-3 w-3" />
                            </div>
                          </div>
                        )}
                      </motion.button>
                    ))}
                  </div>
                </div>

                {/* Image preview */}
                {/* {showImagePreview && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    transition={{ duration: 0.3 }}
                    className="mt-2 overflow-hidden"
                  >
                    <div className="relative aspect-video rounded-md border overflow-hidden bg-muted/30">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Image
                          src={formData.imageUrl}
                          alt="Preview"
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, 500px"
                          onError={() => {
                            setErrors(prev => ({ ...prev, imageUrl: "Invalid image URL" }));
                          }}
                        />
                        <div className="absolute bottom-2 right-2 bg-background/80 backdrop-blur-sm text-xs px-2 py-1 rounded-md">
                          Preview
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )} */}
              </div>

              {/* Public switch */}
              <div className="flex items-center justify-between pt-2">
                <div className="space-y-0.5">
                  <Label htmlFor="isPublic" className="font-medium">
                    Public Playlist
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Public playlists are visible to all users
                  </p>
                </div>
                <Switch
                  id="isPublic"
                  checked={formData.isPublic}
                  onCheckedChange={(checked) => handleSwitchChange('isPublic', checked === true)}
                />
              </div>
            </form>
          </div>

          <SheetFooter className="px-6 py-4 border-t">
            <div className="flex w-full flex-col sm:flex-row gap-3 sm:justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
                className="sm:order-1 order-2"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                form="music-playlist-form"
                disabled={isLoading}
                className={cn(
                  "sm:order-2 order-1",
                  "bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                )}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isEditMode ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    {isEditMode ? 'Update Playlist' : 'Create Playlist'}
                  </>
                )}
              </Button>
            </div>
          </SheetFooter>
        </div>
      </SheetContent>
    </Sheet>
  );
}