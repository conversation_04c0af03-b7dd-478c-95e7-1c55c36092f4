'use client';

import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Play, Pause, ExternalLink, AlertCircle, CheckCircle, SkipBack, SkipForward } from 'lucide-react';
import { useAudioContext } from './music-control';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface SoundCloudWebPlayerProps {
  className?: string;
  onPlayerRef?: (ref: any) => void;
}

interface SoundCloudTrack {
  id: number;
  title: string;
  user?: {
    username?: string;
  };
  duration: number;
  artwork_url?: string;
}

interface SoundCloudWidget {
  bind: (event: string, callback: (data?: any) => void) => void;
  unbind: (event: string) => void;
  load: (url: string, options?: any) => void;
  play: () => void;
  pause: () => void;
  next: () => void;
  prev: () => void;
  seekTo: (position: number) => void;
  setVolume: (volume: number) => void;
  getCurrentSound: (callback: (sound: SoundCloudTrack) => void) => void;
  getSounds: (callback: (sounds: SoundCloudTrack[]) => void) => void;
  getCurrentSoundIndex: (callback: (index: number) => void) => void;
  getPosition: (callback: (position: number) => void) => void;
  getDuration: (callback: (duration: number) => void) => void;
}

declare global {
  interface Window {
    SC: {
      Widget: {
        Events: {
          READY: string;
          PLAY: string;
          PAUSE: string;
          FINISH: string;
          SEEK: string;
          PLAY_PROGRESS: string;
          LOAD_PROGRESS: string;
          ERROR: string;
        };
        (iframeElement: HTMLIFrameElement): SoundCloudWidget;
      };
    };
  }
}

// Custom SoundCloud icon component
const SoundCloudIcon = ({ className }: { className?: string }) => (
  <svg 
    viewBox="0 0 24 24" 
    className={className}
    fill="currentColor"
  >
    <path d="M3.5 12.5c0-.4.2-.7.5-.9v2.8c-.3-.2-.5-.5-.5-.9zm1 0c0-.4.2-.7.5-.9v1.8c-.3-.2-.5-.5-.5-.9zm1 0c0-.4.2-.7.5-.9v1.8c-.3-.2-.5-.5-.5-.9zm1 0c0-.4.2-.7.5-.9v1.8c-.3-.2-.5-.5-.5-.9zm1 .1c0-.5.3-.9.7-1.1v2.1c-.4-.1-.7-.6-.7-1zm1.2-.1c0-.6.4-1.1.8-1.3v2.5c-.4-.1-.8-.7-.8-1.2zm1.3-.1c0-.7.5-1.3 1-1.5v3c-.5-.2-1-.8-1-1.5zm1.5-.1c0-.8.6-1.5 1.2-1.7v3.4c-.6-.2-1.2-.9-1.2-1.7zm1.7-.1c0-.9.7-1.7 1.4-1.9v3.8c-.7-.2-1.4-1-1.4-1.9zm2-.1c0-1 .8-1.9 1.6-2.1v4.2c-.8-.2-1.6-1.1-1.6-2.1zm2.2-.1c0-1.1.9-2.1 1.8-2.3v4.6c-.9-.2-1.8-1.2-1.8-2.3zm2.4-.1c0-1.2 1-2.3 2-2.5v5c-1-.2-2-1.3-2-2.5zm2.6-.1c0-1.3 1.1-2.5 2.2-2.7v5.4c-1.1-.2-2.2-1.4-2.2-2.7z"/>
  </svg>
);

export function SoundCloudWebPlayer({ className, onPlayerRef }: SoundCloudWebPlayerProps) {
  const audioContext = useAudioContext();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const widgetRef = useRef<SoundCloudWidget | null>(null);
  
  // Player state
  const [isPlaying, setIsPlaying] = useState(false);
  const [playerLoaded, setPlayerLoaded] = useState(false);
  const [currentUrl, setCurrentUrl] = useState<string>('');
  const [widgetApiLoaded, setWidgetApiLoaded] = useState(false);
  
  // Playlist state
  const [playlist, setPlaylist] = useState<SoundCloudTrack[]>([]);
  const [currentTrack, setCurrentTrack] = useState<SoundCloudTrack | null>(null);
  const [currentTrackIndex, setCurrentTrackIndex] = useState<number>(0);
  const [isPlaylist, setIsPlaylist] = useState(false);
  
  // Input state
  const [inputUrl, setInputUrl] = useState('');
  const [isValidUrl, setIsValidUrl] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Default SoundCloud track
  const defaultSoundCloudUrl = 'https://soundcloud.com/lofi_girl/sets/peaceful-piano-music-to-focus?si=7d4df17f832c4ac9aa92d06837ab81fa&utm_source=clipboard&utm_medium=text&utm_campaign=social_sharing';

  // Load SoundCloud Widget API
  useEffect(() => {
    if (window.SC) {
      setWidgetApiLoaded(true);
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://w.soundcloud.com/player/api.js';
    script.onload = () => setWidgetApiLoaded(true);
    document.head.appendChild(script);

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, []);

  // URL validation for SoundCloud
  const validateSoundCloudUrl = useCallback((url: string): boolean => {
    if (!url.trim()) return false;
    
    const soundcloudUrlRegex = /^https?:\/\/(www\.)?(soundcloud\.com|snd\.sc)\/.+/;
    return soundcloudUrlRegex.test(url.trim());
  }, []);

  // Convert SoundCloud URL to embed URL
  const convertToEmbedUrl = useCallback((url: string): string => {
    const cleanUrl = url.split('?')[0]; // Remove query parameters for cleaner embed
    const embedUrl = `https://w.soundcloud.com/player/?url=${encodeURIComponent(cleanUrl)}&color=%23ff5500&auto_play=false&hide_related=false&show_comments=false&show_user=true&show_reposts=false&show_teaser=false&visual=false`;
    return embedUrl;
  }, []);

  // Initialize widget when iframe and API are ready
  useEffect(() => {
    if (!widgetApiLoaded || !iframeRef.current || widgetRef.current) return;

    const widget = window.SC.Widget(iframeRef.current);
    widgetRef.current = widget;

    // Set up event listeners
    widget.bind(window.SC.Widget.Events.READY, () => {
      setPlayerLoaded(true);
      setSuccess('SoundCloud player ready!');
      setTimeout(() => setSuccess(null), 3000);

      // Get initial track info
      widget.getSounds((sounds) => {
        if (sounds && sounds.length > 0) {
          // console.log('Loaded tracks:', sounds); // Debug log
          setPlaylist(sounds);
          setIsPlaylist(sounds.length > 1);
          if (sounds[0]) {
            setCurrentTrack(sounds[0]);
          }
          setCurrentTrackIndex(0);
        }
      });
    });

    widget.bind(window.SC.Widget.Events.PLAY, () => {
      setIsPlaying(true);
      audioContext.pauseSystemAudio();
      audioContext.pauseYouTubeAudio();
      
      // Update current track info
      widget.getCurrentSound((sound) => {
        setCurrentTrack(sound);
      });
      
      widget.getCurrentSoundIndex((index) => {
        setCurrentTrackIndex(index);
      });
    });

    widget.bind(window.SC.Widget.Events.PAUSE, () => {
      setIsPlaying(false);
    });

    widget.bind(window.SC.Widget.Events.FINISH, () => {
      setIsPlaying(false);
      
      // Update track index when track finishes (auto-advance)
      setTimeout(() => {
        widget.getCurrentSoundIndex((index) => {
          setCurrentTrackIndex(index);
        });
        widget.getCurrentSound((sound) => {
          setCurrentTrack(sound);
        });
      }, 100);
    });

    return () => {
      if (widgetRef.current) {
        widgetRef.current.unbind(window.SC.Widget.Events.READY);
        widgetRef.current.unbind(window.SC.Widget.Events.PLAY);
        widgetRef.current.unbind(window.SC.Widget.Events.PAUSE);
        widgetRef.current.unbind(window.SC.Widget.Events.FINISH);
      }
    };
  }, [widgetApiLoaded, audioContext]);

  // Initialize with default track
  useEffect(() => {
    if (widgetApiLoaded && !currentUrl) {
      const embedUrl = convertToEmbedUrl(defaultSoundCloudUrl);
      setCurrentUrl(embedUrl);
    }
  }, [widgetApiLoaded, currentUrl, convertToEmbedUrl, defaultSoundCloudUrl]);

  // Handle URL input change
  const handleUrlChange = useCallback((url: string) => {
    setInputUrl(url);
    setIsValidUrl(validateSoundCloudUrl(url));
    setError(null);
  }, [validateSoundCloudUrl]);

  // Load content
  const loadContent = useCallback(async () => {
    if (!isValidUrl || !inputUrl.trim() || !widgetRef.current) return;

    try {
      setLoading(true);
      setError(null);
      
      const embedUrl = convertToEmbedUrl(inputUrl);
      setCurrentUrl(embedUrl);
      
      // Load new content in widget
      widgetRef.current.load(inputUrl, {
        auto_play: false,
        color: '#ff5500',
        hide_related: false,
        show_comments: false,
        show_user: true,
        show_reposts: false,
        show_teaser: false,
        visual: false
      });
      
      // Reset playlist state
      setPlaylist([]);
      setCurrentTrack(null);
      setCurrentTrackIndex(0);
      setIsPlaylist(false);
      setIsPlaying(false);
      
      setSuccess('Content loaded successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Failed to load content:', error);
      setError(`Failed to load content: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  }, [isValidUrl, inputUrl, convertToEmbedUrl]);

  // Player controls
  const handlePlay = useCallback(() => {
    if (widgetRef.current) {
      widgetRef.current.play();
    }
  }, []);

  const handlePause = useCallback(() => {
    if (widgetRef.current) {
      widgetRef.current.pause();
    }
  }, []);

  const handleNext = useCallback(() => {
    if (widgetRef.current && isPlaylist) {
      widgetRef.current.next();
      
      // Update state immediately
      setTimeout(() => {
        widgetRef.current?.getCurrentSoundIndex((index) => {
          setCurrentTrackIndex(index);
        });
        widgetRef.current?.getCurrentSound((sound) => {
          setCurrentTrack(sound);
        });
      }, 100);
    }
  }, [isPlaylist]);

  const handlePrevious = useCallback(() => {
    if (widgetRef.current && isPlaylist) {
      widgetRef.current.prev();
      
      // Update state immediately
      setTimeout(() => {
        widgetRef.current?.getCurrentSoundIndex((index) => {
          setCurrentTrackIndex(index);
        });
        widgetRef.current?.getCurrentSound((sound) => {
          setCurrentTrack(sound);
        });
      }, 100);
    }
  }, [isPlaylist]);

  const handleTrackSelect = useCallback((index: number) => {
    if (widgetRef.current && isPlaylist && index !== currentTrackIndex && playlist.length > index && index >= 0) {
      // Update state immediately for instant UI feedback
      setCurrentTrackIndex(index);
      
      // Safely set current track if it exists
      if (playlist[index]) {
        setCurrentTrack(playlist[index]);
      }
      
      // Navigate to the track
      const diff = index - currentTrackIndex;
      
      if (diff > 0) {
        // Going forward
        for (let i = 0; i < diff; i++) {
          widgetRef.current.next();
        }
      } else if (diff < 0) {
        // Going backward
        for (let i = 0; i < Math.abs(diff); i++) {
          widgetRef.current.prev();
        }
      }
      
      // Verify and sync state after navigation
      setTimeout(() => {
        widgetRef.current?.getCurrentSoundIndex((actualIndex) => {
          if (typeof actualIndex === 'number' && actualIndex >= 0) {
            setCurrentTrackIndex(actualIndex);
          }
        });
        widgetRef.current?.getCurrentSound((sound) => {
          if (sound) {
            setCurrentTrack(sound);
          }
        });
      }, 200);
    }
  }, [currentTrackIndex, isPlaylist, playlist]);

  // Provide pause function to parent
  useEffect(() => {
    if (onPlayerRef) {
      onPlayerRef({ 
        pauseVideo: handlePause
      });
    }
  }, [onPlayerRef, handlePause]);

  // Format duration
  const formatDuration = (ms: number): string => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`space-y-3 max-w-sm ${className}`}>
      {/* Header */}
      <div className="flex items-center gap-2">
        <div className="h-5 w-5 bg-gradient-to-br from-red-500/20 to-red-600/20 rounded flex items-center justify-center">
          <SoundCloudIcon className="h-3 w-3 text-red-500" />
        </div>
        <div>
          <h3 className="text-sm font-medium text-foreground">SoundCloud Player</h3>
        </div>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="flex items-center gap-2 text-sm text-red-600 bg-red-50 dark:bg-red-900/20 px-3 py-2 rounded-lg border border-red-200 dark:border-red-800">
          <AlertCircle className="h-4 w-4" />
          {error}
        </div>
      )}

      {success && (
        <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 dark:bg-green-900/20 px-3 py-2 rounded-lg border border-green-200 dark:border-green-800">
          <CheckCircle className="h-4 w-4" />
          {success}
        </div>
      )}

      {/* URL Input */}
      <div className="space-y-2">
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Input
              type="text"
              value={inputUrl}
              onChange={(e) => handleUrlChange(e.target.value)}
              placeholder="SoundCloud track, playlist, or album URL"
              className="pr-8"
            />
            <ExternalLink className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground/40" />
          </div>
          <Button
            onClick={loadContent}
            disabled={!isValidUrl || loading}
            size="sm"
          >
            {loading ? 'Loading...' : 'Load'}
          </Button>
        </div>

        {inputUrl && !isValidUrl && (
          <p className="text-sm text-amber-600">
            Please enter a valid SoundCloud URL
          </p>
        )}
      </div>

      {/* Player Status */}
      {!playerLoaded && (
        <div className="text-center p-3 border border-dashed border-border rounded-lg">
          <div className="animate-spin h-5 w-5 border-2 border-red-500 border-t-transparent rounded-full mx-auto mb-2"></div>
          <p className="text-xs text-muted-foreground">Loading SoundCloud player...</p>
        </div>
      )}

      {/* SoundCloud Embed */}
      {currentUrl && (
        <div className="bg-muted/20 rounded-lg border border-border/50 overflow-hidden">
          <iframe
            ref={iframeRef}
            width="100%"
            height="166"
            scrolling="no"
            frameBorder="no"
            allow="autoplay"
            src={currentUrl}
            className="w-full"
          />
        </div>
      )}

      {/* Playlist Controls */}
      {playerLoaded && isPlaylist && (
        <div className="bg-muted/20 rounded-lg p-2 border border-border/50 space-y-2">
          {/* Current Track Info */}
          {currentTrack && (
            <div className="text-center">
              <p className="text-xs font-medium text-foreground truncate">
                {currentTrack.title || 'Unknown Track'}
              </p>
              <p className="text-xs text-muted-foreground">
                by {currentTrack.user?.username || 'Unknown Artist'}
              </p>
            </div>
          )}

          {/* Playback Controls */}
          <div className="flex justify-center items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePrevious}
              disabled={!isPlaylist || currentTrackIndex === 0}
              className="h-7 w-7 p-0"
            >
              <SkipBack className="h-3.5 w-3.5" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={isPlaying ? handlePause : handlePlay}
              className="h-7 w-7 p-0"
            >
              {isPlaying ? <Pause className="h-3.5 w-3.5" /> : <Play className="h-3.5 w-3.5" />}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNext}
              disabled={!isPlaylist || currentTrackIndex === playlist.length - 1}
              className="h-7 w-7 p-0"
            >
              <SkipForward className="h-3.5 w-3.5" />
            </Button>
          </div>

          {/* Track Counter */}
          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              Track {currentTrackIndex + 1} of {playlist.length}
            </p>
          </div>
        </div>
      )}

      {/* Playlist */}
      {playerLoaded && isPlaylist && playlist.length > 0 && (
        <div className="bg-muted/20 rounded-lg border border-border/50">
          <div className="p-2 border-b border-border/30">
            <h4 className="text-xs font-medium text-foreground">Playlist ({playlist.length} tracks)</h4>
          </div>
          <div className="max-h-32 overflow-y-auto">
            {playlist.map((track, index) => (
              <button
                key={track.id || index}
                onClick={() => handleTrackSelect(index)}
                className={`w-full text-left p-2 hover:bg-muted/40 transition-colors border-b border-border/20 last:border-b-0 ${
                  index === currentTrackIndex ? 'bg-red-500/10 border-red-500/20' : ''
                }`}
              >
                <div className="flex items-center gap-2">
                  <div className="flex items-center justify-center w-5 h-5 rounded-full bg-red-500/10 text-red-500 text-xs font-medium">
                    {index + 1}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className={`text-xs truncate ${
                      index === currentTrackIndex ? 'font-medium text-red-600' : 'text-foreground'
                    }`}>
                      {track.title || 'Unknown Track'}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {track.user?.username || 'Unknown Artist'} • {formatDuration(track.duration || 0)}
                    </p>
                  </div>
                  {index === currentTrackIndex && isPlaying && (
                    <div className="flex items-center gap-1">
                      <div className="w-1 h-2 bg-red-500 rounded-full animate-pulse"></div>
                      <div className="w-1 h-1.5 bg-red-500 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                      <div className="w-1 h-2.5 bg-red-500 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                    </div>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 