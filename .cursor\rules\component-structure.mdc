---
description: 
globs: 
alwaysApply: false
---
# Component Structure

## Main Components
- [VideoBackground](mdc:src/components/video-background.tsx) - Handles video playback and UI container
- [TimerDisplay](mdc:src/components/timer-display.tsx) - Shows timer and controls
- [SettingsForm](mdc:src/components/settings-form.tsx) - Timer configuration
- [VideoGrid](mdc:src/components/video-grid.tsx) - Video selection interface

## UI Components
The project uses Shadcn UI components (based on Radix UI) located in [src/components/ui](mdc:src/components/ui).

## Component Relationships
- **Pages**: App pages in [src/app](mdc:src/app) integrate main components
- **Timer Page**: Displays video background with timer overlay
- **Home Page**: Shows video selection and settings

## Component Design Pattern
- Components follow client/server component separation
- UI component composition for reusability
- 'use client' directive for interactive components
- Component-specific props with TypeScript interfaces
