import { z } from 'zod';
import { JsonValueSchema } from '../inputTypeSchemas/JsonValueSchema'
import { PomodoroTypeSchema } from '../inputTypeSchemas/PomodoroTypeSchema'
import type { JsonValueType } from '../inputTypeSchemas/JsonValueSchema';
import { UserWithRelationsSchema, UserPartialWithRelationsSchema } from './UserSchema'
import type { UserWithRelations, UserPartialWithRelations } from './UserSchema'
import { TaskWithRelationsSchema, TaskPartialWithRelationsSchema } from './TaskSchema'
import type { TaskWithRelations, TaskPartialWithRelations } from './TaskSchema'

/////////////////////////////////////////
// POMODORO SESSION SCHEMA
/////////////////////////////////////////

export const PomodoroSessionSchema = z.object({
  intervalType: PomodoroTypeSchema,
  id: z.string().cuid(),
  startTime: z.coerce.date(),
  endTime: z.coerce.date().nullish(),
  totalDuration: z.number().int(),
  focusDuration: z.number().int().nullish(),
  breakDuration: z.number().int().nullish(),
  completed: z.boolean(),
  interrupted: z.boolean(),
  interruptedSessions: JsonValueSchema.nullable(),
  note: z.string().nullish(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  userId: z.string(),
  taskId: z.string().nullish(),
})

export type PomodoroSession = z.infer<typeof PomodoroSessionSchema>

/////////////////////////////////////////
// POMODORO SESSION PARTIAL SCHEMA
/////////////////////////////////////////

export const PomodoroSessionPartialSchema = PomodoroSessionSchema.partial()

export type PomodoroSessionPartial = z.infer<typeof PomodoroSessionPartialSchema>

/////////////////////////////////////////
// POMODORO SESSION RELATION SCHEMA
/////////////////////////////////////////

export type PomodoroSessionRelations = {
  user: UserWithRelations;
  task?: TaskWithRelations | null;
};

export type PomodoroSessionWithRelations = Omit<z.infer<typeof PomodoroSessionSchema>, "interruptedSessions"> & {
  interruptedSessions?: JsonValueType | null;
} & PomodoroSessionRelations

export const PomodoroSessionWithRelationsSchema: z.ZodType<PomodoroSessionWithRelations> = PomodoroSessionSchema.merge(z.object({
  user: z.lazy(() => UserWithRelationsSchema),
  task: z.lazy(() => TaskWithRelationsSchema).nullish(),
}))

/////////////////////////////////////////
// POMODORO SESSION PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type PomodoroSessionPartialRelations = {
  user?: UserPartialWithRelations;
  task?: TaskPartialWithRelations | null;
};

export type PomodoroSessionPartialWithRelations = Omit<z.infer<typeof PomodoroSessionPartialSchema>, "interruptedSessions"> & {
  interruptedSessions?: JsonValueType | null;
} & PomodoroSessionPartialRelations

export const PomodoroSessionPartialWithRelationsSchema: z.ZodType<PomodoroSessionPartialWithRelations> = PomodoroSessionPartialSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  task: z.lazy(() => TaskPartialWithRelationsSchema).nullish(),
})).partial()

export type PomodoroSessionWithPartialRelations = Omit<z.infer<typeof PomodoroSessionSchema>, "interruptedSessions"> & {
  interruptedSessions?: JsonValueType | null;
} & PomodoroSessionPartialRelations

export const PomodoroSessionWithPartialRelationsSchema: z.ZodType<PomodoroSessionWithPartialRelations> = PomodoroSessionSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  task: z.lazy(() => TaskPartialWithRelationsSchema).nullish(),
}).partial())

export default PomodoroSessionSchema;
