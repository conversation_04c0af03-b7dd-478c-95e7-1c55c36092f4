import { AdminHeader } from "./_components/admin-header"
import { AdminSidebar } from "./_components/admin-sidebar"
import { SidebarProvider } from "./_components/sidebar-context"
import { getUserServer } from "@/server/private/get-user-server";
import { redirect } from "next/navigation";

// Force dynamic rendering for authentication
export const dynamic = 'force-dynamic';

interface AdminLayoutProps {
  children: React.ReactNode
}

export default async function AdminLayout({ children }: AdminLayoutProps) {
  const { user, isAuthenticated, error } = await getUserServer();

  // If user is not authenticated, redirect to login
  if (!user || !isAuthenticated || error) {
    redirect("/auth/sign-in");
  }

  // Check if user has admin role and specific admin ID
  if (user.role !== "ADMIN" || user.id !== "rgrD9gaIxTyapjI53TfcHCMKz5a3Mvwj") {
    redirect("/");
  }

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full flex-col overflow-hidden">
        <AdminHeader />
        <div className="flex flex-1 overflow-hidden">
          <div className="hidden md:block">
            <AdminSidebar />
          </div>
          <main className="flex-1 overflow-auto p-4 md:p-6">
            <div className="container mx-auto max-w-7xl">{children}</div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  )
}
