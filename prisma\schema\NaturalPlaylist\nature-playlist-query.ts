import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { InferRequestType, InferResponseType } from "hono";
import client from "@/lib/trpc";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

// Get all nature playlists
export type GetNaturePlaylists_ResponseType = InferResponseType<
  (typeof client.api.naturePlaylists)["$get"],
  200
>;

export type GetNaturePlaylists_ResponseTypeSuccess = Extract<
  GetNaturePlaylists_ResponseType,
  { data: object }
>["data"];

export const useGetNaturePlaylists = (filters?: {
  isPublic?: boolean;
  isDefault?: boolean;
}) => {
  return useQuery({
    queryKey: ["naturePlaylists", filters],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      
      if (filters?.isPublic !== undefined) {
        queryParams.append("isPublic", String(filters.isPublic));
      }
      
      if (filters?.isDefault !== undefined) {
        queryParams.append("isDefault", String(filters.isDefault));
      }
      
      const queryString = queryParams.toString();
      
      let response;
      if (queryString) {
        response = await client.api.naturePlaylists.$get({
          query: { 
            isPublic: filters?.isPublic !== undefined ? String(filters.isPublic) : undefined,
            isDefault: filters?.isDefault !== undefined ? String(filters.isDefault) : undefined
          }
        });
      } else {
        response = await client.api.naturePlaylists.$get();
      }
      
      if (!response.ok) {
        throw new Error("Failed to fetch nature playlists");
      }
      const { data } = await response.json();
      return data;
    },
  });
};

// Get single nature playlist
type GetNaturePlaylist_ResponseType = InferResponseType<
  (typeof client.api.naturePlaylists)[":id"]["$get"],
  200
>;

export type GetNaturePlaylist_ResponseTypeSuccess = Extract<
  GetNaturePlaylist_ResponseType,
  { data: object }
>["data"];

export const useGetNaturePlaylist = (id?: string) => {
  return useQuery({
    queryKey: ["naturePlaylists", { id }],
    queryFn: async () => {
      if (!id) throw new Error("No nature playlist ID provided");

      const response = await client.api.naturePlaylists[":id"]["$get"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch nature playlist");
      }

      const { data } = await response.json();
      return data;
    },
    enabled: !!id,
  });
};

// Create nature playlist
interface CreateNaturePlaylistSuccessResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

type CreateNaturePlaylistRequest = InferRequestType<
  (typeof client.api.naturePlaylists)["$post"]
>;

export const useCreateNaturePlaylist = () => {
  const router = useRouter();
  const queryClient = useQueryClient();

  const mutation = useMutation<
    CreateNaturePlaylistSuccessResponse,
    Error,
    CreateNaturePlaylistRequest
  >({
    mutationFn: async ({ form }) => {
      const response = await client.api.naturePlaylists.$post({ form });

      if (!response.ok) {
        throw new Error("Failed to create nature playlist");
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Nature playlist created successfully");
      router.refresh();
      queryClient.invalidateQueries({ queryKey: ["naturePlaylists"] });
    },
    onError: (error) => {
      toast.error(`Failed to create nature playlist: ${error.message}`);
    },
  });

  return mutation;
};

// Update nature playlist
type UpdateNaturePlaylist_ResponseType = InferResponseType<
  (typeof client.api.naturePlaylists)[":id"]["$patch"],
  200
>;

export type UpdateNaturePlaylist_ResponseTypeSuccess = Extract<
  UpdateNaturePlaylist_ResponseType,
  { data: object }
>["data"];

type UpdateNaturePlaylistRequest = InferRequestType<
  (typeof client.api.naturePlaylists)[":id"]["$patch"]
>;

export const useUpdateNaturePlaylist = () => {
  const queryClient = useQueryClient();
  
  return useMutation<
    UpdateNaturePlaylist_ResponseType,
    Error,
    UpdateNaturePlaylistRequest
  >({
    mutationFn: async (variables) => {
      const { form, param } = variables;

      if (!param?.id) {
        throw new Error("No nature playlist ID provided");
      }

      const response = await client.api.naturePlaylists[":id"]["$patch"]({
        form,
        param: { id: param.id },
      });

      if (!response.ok) {
        throw new Error(`Failed to update nature playlist. Status: ${response.status}`);
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Nature playlist updated successfully");
      const naturePlaylistId = data?.id;
      if (naturePlaylistId) {
        queryClient.invalidateQueries({ queryKey: ["naturePlaylists", { id: naturePlaylistId }] });
      }
      queryClient.invalidateQueries({ queryKey: ["naturePlaylists"] });
    },
    onError: (error) => {
      toast.error(`Failed to update nature playlist: ${error.message}`);
    },
  });
};

// Delete nature playlist
type DeleteNaturePlaylist_ResponseType = InferResponseType<
  (typeof client.api.naturePlaylists)[":id"]["$delete"],
  200
>;

export const useDeleteNaturePlaylist = () => {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation<
    DeleteNaturePlaylist_ResponseType,
    Error,
    { id: string }
  >({
    mutationFn: async ({ id }) => {
      if (!id) {
        throw new Error("No nature playlist ID provided");
      }

      const response = await client.api.naturePlaylists[":id"]["$delete"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to delete nature playlist");
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Nature playlist deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["naturePlaylists"] });
      router.push("/nature-playlists");
    },
    onError: (error) => {
      toast.error(`Failed to delete nature playlist: ${error.message}`);
    },
  });
};

// Add Nature Sounds to Nature Playlist
interface NatureSoundResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface AddNatureSoundsToNaturePlaylistRequest {
  naturePlaylistId: string;
  natureSoundIds: string[];
}

export const useAddNatureSoundsToNaturePlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    NatureSoundResponse,
    Error,
    AddNatureSoundsToNaturePlaylistRequest
  >({
    mutationFn: async ({ naturePlaylistId, natureSoundIds }) => {
      if (!naturePlaylistId) {
        throw new Error("No nature playlist ID provided");
      }

      // Use the API route
      const response = await client.api.naturePlaylists[":id"]["nature-sounds"].$post({
        param: { id: naturePlaylistId },
        json: { natureSoundIds }
      });

      if (!response.ok) {
        throw new Error(`Failed to add nature sounds to nature playlist: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Nature sounds added to nature playlist successfully");
      queryClient.invalidateQueries({ queryKey: ["naturePlaylists", { id: variables.naturePlaylistId }] });
    },
    onError: (error) => {
      toast.error(`Failed to add nature sounds to nature playlist: ${error.message}`);
    },
  });
};

// Remove Nature Sound from Nature Playlist
interface RemoveNatureSoundResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface RemoveNatureSoundFromNaturePlaylistRequest {
  naturePlaylistId: string;
  natureSoundId: string;
}

export const useRemoveNatureSoundFromNaturePlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    RemoveNatureSoundResponse,
    Error,
    RemoveNatureSoundFromNaturePlaylistRequest
  >({
    mutationFn: async ({ naturePlaylistId, natureSoundId }) => {
      if (!naturePlaylistId || !natureSoundId) {
        throw new Error("Both nature playlist ID and nature sound ID are required");
      }

      const response = await client.api.naturePlaylists[":id"]["nature-sounds"][":natureSoundId"]["$delete"]({
        param: { id: naturePlaylistId, natureSoundId },
      });

      if (!response.ok) {
        throw new Error(`Failed to remove nature sound from nature playlist: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Nature sound removed from nature playlist");
      // Invalidate both the specific nature playlist and the nature playlists list
      queryClient.invalidateQueries({ queryKey: ["naturePlaylists", { id: variables.naturePlaylistId }] });
      queryClient.invalidateQueries({ queryKey: ["naturePlaylists"] });
    },
    onError: (error) => {
      toast.error(`Failed to remove nature sound from nature playlist: ${error.message}`);
    },
  });
};

// Reorder Nature Sounds in Nature Playlist
interface ReorderNatureSoundResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface ReorderNatureSoundRequest {
  naturePlaylistId: string;
  natureSoundOrder: string[];
}

export const useReorderNatureSoundsInNaturePlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    ReorderNatureSoundResponse,
    Error,
    ReorderNatureSoundRequest
  >({
    mutationFn: async ({ naturePlaylistId, natureSoundOrder }) => {
      if (!naturePlaylistId) {
        throw new Error("No nature playlist ID provided");
      }

      const response = await client.api.naturePlaylists[":id"]["nature-sounds"].reorder.$patch({
        param: { id: naturePlaylistId },
        json: { natureSoundOrder }
      });

      if (!response.ok) {
        throw new Error(`Failed to reorder nature sounds in nature playlist: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Nature sounds order updated successfully");
      queryClient.invalidateQueries({ queryKey: ["naturePlaylists", { id: variables.naturePlaylistId }] });
    },
    onError: (error) => {
      toast.error(`Failed to reorder nature sounds: ${error.message}`);
    },
  });
};

// Add Videos to Nature Playlist
interface VideoResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface AddVideosToNaturePlaylistRequest {
  naturePlaylistId: string;
  videoIds: string[];
}

export const useAddVideosToNaturePlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    VideoResponse,
    Error,
    AddVideosToNaturePlaylistRequest
  >({
    mutationFn: async ({ naturePlaylistId, videoIds }) => {
      if (!naturePlaylistId) {
        throw new Error("No nature playlist ID provided");
      }

      const response = await client.api.naturePlaylists[":id"].videos.$post({
        param: { id: naturePlaylistId },
        json: { videoIds }
      });

      if (!response.ok) {
        throw new Error(`Failed to add videos to nature playlist: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Videos added to nature playlist successfully");
      queryClient.invalidateQueries({ queryKey: ["naturePlaylists", { id: variables.naturePlaylistId }] });
    },
    onError: (error) => {
      toast.error(`Failed to add videos to nature playlist: ${error.message}`);
    },
  });
};

// Remove Video from Nature Playlist
interface RemoveVideoResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface RemoveVideoFromNaturePlaylistRequest {
  naturePlaylistId: string;
  videoId: string;
}

export const useRemoveVideoFromNaturePlaylist = () => {
  const queryClient = useQueryClient();

  return useMutation<
    RemoveVideoResponse,
    Error,
    RemoveVideoFromNaturePlaylistRequest
  >({
    mutationFn: async ({ naturePlaylistId, videoId }) => {
      if (!naturePlaylistId || !videoId) {
        throw new Error("Both nature playlist ID and video ID are required");
      }

      const response = await client.api.naturePlaylists[":id"].videos[":videoId"]["$delete"]({
        param: { id: naturePlaylistId, videoId },
      });

      if (!response.ok) {
        throw new Error(`Failed to remove video from nature playlist: ${response.statusText}`);
      }

      return await response.json();
    },
    onSuccess: (_, variables) => {
      toast.success("Video removed from nature playlist");
      queryClient.invalidateQueries({ queryKey: ["naturePlaylists", { id: variables.naturePlaylistId }] });
    },
    onError: (error) => {
      toast.error(`Failed to remove video from nature playlist: ${error.message}`);
    },
  });
}; 