'use client';

import { useMemo } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { positionClasses, getBackgroundOpacity } from './common/constants';
import { TimerGlobalStyles } from './styles/global-styles';
import { ResizeHandle } from './resize-handle';
import type { CustomPosition } from './common/types';
import type { InteractionHandlers } from './timer-interaction-manager';

interface TimerContainerProps {
  timerRef: React.RefObject<HTMLDivElement | null>;
  showControls: boolean;
  isRunning: boolean;
  isDragging: boolean;
  isResizing: boolean;
  isResizePreview: boolean;
  showPositionIndicator: boolean;
  showResizeHandles: boolean;
  isResizeHandleHovered: boolean;
  customPosition: CustomPosition | null;
  timerPosition: string;
  timerSize: { width?: number; timeScale?: number } | null;
  timerUIStyle: string;
  timerOpacity: number;
  interactionHandlers: InteractionHandlers;
  handleDragStartMouseWrapper: (e: React.MouseEvent) => void;
  handleDragStartTouchWrapper: (e: React.TouchEvent) => void;
  handleDoubleClickWrapper: (e: React.MouseEvent) => void;
  handleKeyDown: (e: React.KeyboardEvent) => void;
  handleResizeHandleMouseEnter: () => void;
  handleResizeHandleMouseLeave: () => void;
  handleResizeStartMouse: (e: React.MouseEvent) => void;
  handleResizeStartTouch: (e: React.TouchEvent) => void;
  currentPhaseLabel: string;
  timeDisplay: string;
  children: React.ReactNode;
}

export function TimerContainer({
  timerRef,
  showControls,
  isRunning,
  isDragging,
  isResizing,
  isResizePreview,
  showPositionIndicator,
  showResizeHandles,
  isResizeHandleHovered,
  customPosition,
  timerPosition,
  timerSize,
  timerUIStyle,
  timerOpacity,
  interactionHandlers,
  handleDragStartMouseWrapper,
  handleDragStartTouchWrapper,
  handleDoubleClickWrapper,
  handleKeyDown,
  handleResizeHandleMouseEnter,
  handleResizeHandleMouseLeave,
  handleResizeStartMouse,
  handleResizeStartTouch,
  currentPhaseLabel,
  timeDisplay,
  children
}: TimerContainerProps) {

  // Calculate stable styles to prevent expensive recalculations on hover
  const cardStyles = useMemo(() => {
    // Get viewport dimensions and calculate bottom position
    // This ensures the timer stays at the bottom edge even during animations
    const topPosition = customPosition?.top;

    // Calculate opacity based on user settings and ensure minimum contrast
    const adjustedOpacity = getBackgroundOpacity(timerOpacity) / 100;

    // Precompute background colors to avoid recalculation during animation
    const transparentBg = 'transparent';

    // Precompute shadow values for performance
    const noShadow = 'none';
    const normalShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
    const controlsShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
    const runningShadow = '0 8px 10px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';

    return {
      transform: isResizePreview ? 'scale3d(1.008, 1.008, 1)' : 'scale3d(1, 1, 1)',
      // Use will-change only when necessary to reduce GPU memory consumption
      willChange: isDragging
        ? 'transform, left, top'
        : isResizing
          ? 'width'
          : showControls
            ? 'opacity, transform'
            : 'auto',
      top: topPosition,
      left: customPosition?.left,
      // Disable transition during drag/resize for better performance
      transition: isDragging || isResizing
        ? 'none'
        : 'transform 220ms cubic-bezier(0.16, 1, 0.3, 1), opacity 220ms cubic-bezier(0.16, 1, 0.3, 1), background-color 220ms ease-out, box-shadow 220ms ease-out',
      width: timerSize?.width,
      // Optimized background calculations - precomputed values
      backgroundColor: timerOpacity === 0
        ? transparentBg
        : transparentBg, // Always use transparent background to prevent dimming effect
      backgroundImage: showControls
        ? undefined // Remove gradient when controls are shown
        : isRunning
          ? 'linear-gradient(135deg, rgba(255,255,255,0.02), rgba(255,255,255,0))'
          : undefined,
      // Optimized shadow calculations - precomputed values
      boxShadow: timerOpacity < 10
        ? noShadow
        : showControls
          ? controlsShadow
          : isRunning
            ? runningShadow
            : normalShadow,
      // Reduce blur amount for better performance, but keep it active when showControls is true to ensure readability
      backdropFilter: timerOpacity === 0
        ? 'none'
        : showControls
          ? `blur(${Math.max(4, Math.min(12, 10 * adjustedOpacity))}px)` // Enhanced blur for better readability without background
          : `blur(${Math.max(3, Math.min(10, 8 * adjustedOpacity))}px)`,
      // Add hardware acceleration with properly typed properties
      WebkitBackfaceVisibility: 'hidden' as const,
      backfaceVisibility: 'hidden' as const,
      transformStyle: 'preserve-3d' as const,
      contain: 'paint' as const,
    };
  }, [
    showControls,
    isResizePreview,
    isDragging,
    isResizing,
    customPosition?.top,
    customPosition?.left,
    timerSize?.width,
    isRunning,
    timerOpacity,
  ]);

  // Compute content styles separately for better performance
  const contentStyles = useMemo(() => {
    const baseOpacity = 1; // Always use full opacity to prevent dimming effect

    // Calculate enhanced padding for larger timer scales
    if (timerSize?.timeScale && timerSize.timeScale > 1.3) {
      const scaleFactor = timerSize.timeScale - 1.3;
      const basePaddingX = showControls ? 1.5 : isRunning ? 0.5 : 1;
      const basePaddingY = showControls ? 3 : isRunning ? 1.5 : 2;

      const enhancedPaddingX = Math.max(basePaddingX, Math.min(basePaddingX + scaleFactor * 0.8, basePaddingX * 1.6));
      const enhancedPaddingY = Math.max(basePaddingY, Math.min(basePaddingY + scaleFactor * 1.2, basePaddingY * 1.8));

      return {
        opacity: baseOpacity,
        paddingLeft: `${enhancedPaddingX * 0.25}rem`,
        paddingRight: `${enhancedPaddingX * 0.25}rem`,
        paddingTop: `${enhancedPaddingY * 0.25}rem`,
        paddingBottom: `${enhancedPaddingY * 0.25}rem`
      };
    }

    // Default padding for smaller timer scales
    return {
      opacity: baseOpacity,
      paddingLeft: showControls ? '0.375rem' : isRunning ? '0.125rem' : '0.25rem',
      paddingRight: showControls ? '0.375rem' : isRunning ? '0.125rem' : '0.25rem',
      paddingTop: showControls ? '0.75rem' : isRunning ? '0.375rem' : '0.5rem',
      paddingBottom: showControls ? '0.75rem' : isRunning ? '0.375rem' : '0.5rem'
    };
  }, [showControls, isRunning, timerSize?.timeScale]);

  // Pre-compute card class names to avoid recalculation on render
  const cardClassNames = useMemo(() => cn(
    "fixed z-20 transition-properties ease-out border-0", // z-20 ensures timer is above video (z-0) but below navigation controls (z-50)
    // Apply glass effect styling
    "timer-glass-container text-neutral-600 dark:text-neutral-300",
    showControls
      ? "backdrop-blur-lg shadow-xl p-3"
      : isRunning
        ? "backdrop-blur-md shadow-md p-2"
        : "backdrop-blur-md shadow-md p-2",
    !customPosition ? positionClasses[timerPosition as keyof typeof positionClasses] : '',
    "touch-manipulation rounded-xl",
    isDragging ? "opacity-90 cursor-grabbing select-none !transition-none" : "cursor-pointer",
    isResizePreview ? "!ring-2 ring-offset-1 ring-offset-background/3 ring-white/12" : "",
    showPositionIndicator ? "ring-2 ring-white/15 ring-offset-1 ring-offset-background/3" : ""
  ), [
    showControls,
    isRunning,
    customPosition,
    timerPosition,
    isDragging,
    isResizePreview,
    showPositionIndicator,
  ]);

  // Precompute content class names and styles
  const contentClassNames = useMemo(() => cn(
    "transition-properties ease-out",
    // Reduced padding for circular timer to minimize empty space
    timerUIStyle === 'circular' ? "px-1 py-1.5 flex justify-center items-center" : ""
  ), [timerUIStyle]);

  return (
    <>
      <TimerGlobalStyles />
      <Card
        ref={timerRef}
        className={cn(
          cardClassNames,
          "timer-focus-ring"
        )}
        style={{
          ...cardStyles,
          ...(showControls && !isDragging && !isResizing
            ? { animation: 'timerScaleIn 220ms cubic-bezier(0.16, 1, 0.3, 1) forwards' }
            : {})
        }}
        onMouseEnter={interactionHandlers.handleMouseEnter}
        onMouseLeave={interactionHandlers.handleMouseLeave}
        onTouchStart={interactionHandlers.handleTouchStartControls}
        onTouchEnd={interactionHandlers.handleTouchEnd}
        onMouseDown={handleDragStartMouseWrapper}
        onTouchStartCapture={handleDragStartTouchWrapper}
        onKeyDown={handleKeyDown}
        onDoubleClick={handleDoubleClickWrapper}
        role="region"
        aria-label={`${currentPhaseLabel} timer with ${timeDisplay} remaining`}
        tabIndex={0}
      >
        {/* Help tooltip for drag/position - shown briefly on resize/reposition */}
        {showPositionIndicator && (
          <div className="absolute inset-0 pointer-events-none z-50 flex items-center justify-center">
            <div className="text-xs text-white font-medium bg-black/75 backdrop-blur-sm px-3 py-2 rounded-lg shadow-lg border border-white/12 animate-fade-in">
              Timer position reset
            </div>
          </div>
        )}

        {/* Single resize handle in the bottom-right corner */}
        {showResizeHandles && (
          <ResizeHandle
            onMouseEnter={handleResizeHandleMouseEnter}
            onMouseLeave={handleResizeHandleMouseLeave}
            onMouseDown={handleResizeStartMouse}
            onTouchStart={handleResizeStartTouch}
            isHovered={isResizeHandleHovered}
            isResizing={isResizing}
            showControls={showControls}
          />
        )}

        {/* Resize preview hint - shows when hovering resize handle */}
        {isResizePreview && (
          <div className="absolute inset-0 pointer-events-none z-30 flex items-center justify-center">
            <div className="text-xs text-white font-medium bg-black/50 backdrop-blur-md px-3 py-2 rounded-lg shadow-md border border-white/12 animate-fade-in">
              Click and drag to resize
            </div>
          </div>
        )}

        <CardContent className={contentClassNames} style={contentStyles}>
          <div className="flex flex-col items-center">
            {children}
          </div>
        </CardContent>
      </Card>
    </>
  );
}