'use client';

import { MusicControlProps, PlaybackMode } from './types';

// Type guards to check playlist types
function hasMusicPlaylist(playlist: MusicControlProps['playlist']): playlist is {
  id: string;
  name: string;
  musics: {
    id: string;
    title: string;
    src?: string | null;
    createdAt?: string;
    updatedAt?: string;
    isPublic?: boolean;
    creatorType?: string;
    userId?: string;
    genres?: string[];
  }[];
  [key: string]: any;
} {
  return playlist !== null && 
         typeof playlist === 'object' && 
         'musics' in playlist && 
         Array.isArray(playlist.musics) && 
         playlist.musics.length > 0;
}

function hasNatureSoundsPlaylist(playlist: MusicControlProps['playlist']): playlist is {
  id: string;
  name: string;
  natureSounds: {
    id: string;
    title: string;
    src?: string | null;
    category?: string[];
  }[];
  [key: string]: any;
} {
  return playlist !== null && 
         typeof playlist === 'object' && 
         'natureSounds' in playlist && 
         Array.isArray(playlist.natureSounds) && 
         playlist.natureSounds.length > 0;
}



// Global system player manager - singleton pattern
class SystemPlayerManager {
  private static instance: SystemPlayerManager | null = null;
  private mainAudioRef: HTMLAudioElement | null = null;
  private audioElementsMap: Map<string, HTMLAudioElement> = new Map();
  private currentPlaylist: MusicControlProps['playlist'] = null;
  private isInitialized = false;
  
  // State
  private currentMusicIndex = 0;
  private isMainPlaying = false;
  private mainVolume = 70;
  private isMainMuted = false;
  private playbackMode: PlaybackMode = PlaybackMode.LOOP_ALL;
  private currentTime = 0;
  private duration = 0;
  private natureSounds: Array<{
    id: string;
    title: string;
    src: string;
    category: string[];
    isPlaying: boolean;
    volume: number;
    isMuted: boolean;
  }> = [];

  private constructor() {
    // Private constructor for singleton
  }

  // Load nature sound volumes from localStorage
  private loadNatureSoundVolumes(): { [key: string]: number } {
    if (typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem('naturalSoundVolumes');
        if (saved) {
          const parsed = JSON.parse(saved);
          if (typeof parsed === 'object' && parsed !== null) {
            const volumes: { [key: string]: number } = {};
            for (const [key, value] of Object.entries(parsed)) {
              if (typeof value === 'number' && value >= 0 && value <= 100) {
                volumes[key] = value;
              }
            }
            return volumes;
          }
        }
      } catch (e) {
        console.error('Failed to load nature sound volumes:', e);
      }
    }
    return {};
  }

  public static getInstance(): SystemPlayerManager {
    if (!SystemPlayerManager.instance) {
      SystemPlayerManager.instance = new SystemPlayerManager();
    }
    return SystemPlayerManager.instance;
  }

  // Track initialization state to prevent multiple calls
  private isInitializing = false;
  private lastInitializedPlaylistId: string | null = null;

  // Initialize with playlist
  public async initialize(playlist: MusicControlProps['playlist'], shouldAutoPlay: boolean = true): Promise<void> {
    // Prevent multiple simultaneous initializations
    if (this.isInitializing) {
      console.log('SystemPlayerManager: Already initializing, skipping...');
      return;
    }

    // Always re-initialize when requested to ensure proper video-to-playlist synchronization
    // This is especially important for mobile swipe gestures where videos change frequently
    this.isInitializing = true;
    console.log('SystemPlayerManager: Initializing with playlist', playlist?.name, `(ID: ${playlist?.id})`);
    console.log('SystemPlayerManager: Previous playlist ID:', this.lastInitializedPlaylistId, 'New playlist ID:', playlist?.id);

    try {
      // Clean up previous state
      this.cleanup();

      this.currentPlaylist = playlist;
      this.isInitialized = true;
      this.lastInitializedPlaylistId = playlist?.id || null;

      if (!hasMusicPlaylist(playlist)) {
        console.log('SystemPlayerManager: No music playlist available');
        return;
      }

      // Initialize nature sounds if available
      if (hasNatureSoundsPlaylist(playlist)) {
        this.initializeNatureSounds(playlist, shouldAutoPlay);
      }

      // Auto-play first track if requested
      if (shouldAutoPlay && playlist.musics[0]) {
        setTimeout(() => {
          // Double-check we're still initialized and not cleaned up
          if (this.isInitialized && this.lastInitializedPlaylistId === playlist.id) {
            this.playMainTrack(0);
            console.log(`SystemPlayerManager: Auto-playing first track: ${playlist.musics[0].title}`);
          }
        }, 300);
      }
    } finally {
      this.isInitializing = false;
    }
  }

  // Track current play request to prevent overlapping
  private currentPlayPromise: Promise<void> | null = null;

  // Play main track by index
  public playMainTrack(index: number): void {
    if (!hasMusicPlaylist(this.currentPlaylist) || index >= this.currentPlaylist.musics.length) {
      return;
    }

    const track = this.currentPlaylist.musics[index];
    if (!track.src) {
      console.warn(`SystemPlayerManager: Track ${track.title} has no source`);
      return;
    }

    // Cancel any ongoing play request
    if (this.currentPlayPromise) {
      console.log('SystemPlayerManager: Cancelling previous play request');
    }

    // Create or reuse audio element
    if (!this.mainAudioRef) {
      this.mainAudioRef = new Audio();
      this.setupMainAudioEvents();
    }

    // Stop current audio if playing
    if (!this.mainAudioRef.paused) {
      this.mainAudioRef.pause();
    }

    // Update state
    this.currentMusicIndex = index;
    this.mainAudioRef.src = track.src;
    this.mainAudioRef.volume = this.mainVolume / 100;
    this.mainAudioRef.muted = this.isMainMuted;
    this.mainAudioRef.loop = this.playbackMode === PlaybackMode.LOOP_ONE;

    // Play audio with proper error handling
    this.currentPlayPromise = this.mainAudioRef.play()
      .then(() => {
        this.isMainPlaying = true;
        this.notifyStateChange();
        console.log(`SystemPlayerManager: Playing track: ${track.title}`);
        this.currentPlayPromise = null;
      })
      .catch((error) => {
        // Only log error if it's not an AbortError from overlapping requests
        if (error.name !== 'AbortError') {
          console.error('SystemPlayerManager: Failed to play track:', error);
        }
        this.currentPlayPromise = null;
      });
  }

  // Toggle play/pause
  public toggleMainPlay(): void {
    if (!this.mainAudioRef) {
      if (hasMusicPlaylist(this.currentPlaylist)) {
        this.playMainTrack(this.currentMusicIndex);
      }
      return;
    }

    if (this.isMainPlaying) {
      this.mainAudioRef.pause();
      this.isMainPlaying = false;
    } else {
      this.mainAudioRef.play()
        .then(() => {
          this.isMainPlaying = true;
        })
        .catch(() => {});
    }
    
    this.notifyStateChange();
  }

  // Skip to next track
  public skipToNext(): void {
    if (!hasMusicPlaylist(this.currentPlaylist)) return;

    let nextIndex: number;
    if (this.playbackMode === 'SHUFFLE') {
      nextIndex = Math.floor(Math.random() * this.currentPlaylist.musics.length);
    } else {
      nextIndex = (this.currentMusicIndex + 1) % this.currentPlaylist.musics.length;
    }

    this.playMainTrack(nextIndex);
  }

  // Skip to previous track
  public skipToPrevious(): void {
    if (!hasMusicPlaylist(this.currentPlaylist)) return;

    let prevIndex: number;
    if (this.playbackMode === 'SHUFFLE') {
      prevIndex = Math.floor(Math.random() * this.currentPlaylist.musics.length);
    } else {
      prevIndex = this.currentMusicIndex === 0 
        ? this.currentPlaylist.musics.length - 1 
        : this.currentMusicIndex - 1;
    }

    this.playMainTrack(prevIndex);
  }

  // Volume control
  public setMainVolume(volume: number): void {
    this.mainVolume = volume;
    if (this.mainAudioRef) {
      this.mainAudioRef.volume = volume / 100;
    }
    this.notifyStateChange();
  }

  // Mute control
  public toggleMainMute(): void {
    this.isMainMuted = !this.isMainMuted;
    if (this.mainAudioRef) {
      this.mainAudioRef.muted = this.isMainMuted;
    }
    this.notifyStateChange();
  }

  // Playback mode control
  public togglePlaybackMode(): void {
    const modes: PlaybackMode[] = [PlaybackMode.LOOP_ALL, PlaybackMode.LOOP_ONE, PlaybackMode.SHUFFLE];
    const currentIndex = modes.indexOf(this.playbackMode);
    this.playbackMode = modes[(currentIndex + 1) % modes.length];

    if (this.mainAudioRef) {
      this.mainAudioRef.loop = this.playbackMode === PlaybackMode.LOOP_ONE;
    }

    this.notifyStateChange();
  }

  // Seek control
  public handleSeek(time: number): void {
    if (this.mainAudioRef) {
      this.mainAudioRef.currentTime = time;
      this.currentTime = time;
      this.notifyStateChange();
    }
  }

  // Get current state
  public getState() {
    const currentTrack = hasMusicPlaylist(this.currentPlaylist) 
      ? this.currentPlaylist.musics[this.currentMusicIndex] 
      : undefined;
    
    return {
      currentTrack,
      currentMusicIndex: this.currentMusicIndex,
      isMainPlaying: this.isMainPlaying,
      mainVolume: this.mainVolume,
      isMainMuted: this.isMainMuted,
      playbackMode: this.playbackMode,
      currentTime: this.currentTime,
      duration: this.duration,
      natureSounds: this.natureSounds,
      playlist: this.currentPlaylist,
      isInitialized: this.isInitialized,
      musicCount: hasMusicPlaylist(this.currentPlaylist) ? this.currentPlaylist.musics.length : 0,
    };
  }

  // Pause only the main music track (keeps nature sounds playing)
  public pauseMainOnly(): void {
    if (this.mainAudioRef && !this.mainAudioRef.paused) {
      this.mainAudioRef.pause();
      this.isMainPlaying = false;
    }

    this.notifyStateChange();
  }

  // Pause all audio
  public pauseAll(): void {
    if (this.mainAudioRef && !this.mainAudioRef.paused) {
      this.mainAudioRef.pause();
      this.isMainPlaying = false;
    }

    this.audioElementsMap.forEach((audio) => {
      if (!audio.paused) {
        audio.pause();
      }
    });

    // Update nature sounds state
    this.natureSounds = this.natureSounds.map(sound => ({
      ...sound,
      isPlaying: false
    }));

    this.notifyStateChange();
  }

  // Setup main audio events
  private setupMainAudioEvents(): void {
    if (!this.mainAudioRef) return;

    this.mainAudioRef.onended = () => {
      this.handleMainTrackEnded();
    };

    this.mainAudioRef.ontimeupdate = () => {
      this.currentTime = this.mainAudioRef?.currentTime || 0;
      this.duration = this.mainAudioRef?.duration || 0;
      this.notifyStateChange();
    };

    this.mainAudioRef.onloadedmetadata = () => {
      this.duration = this.mainAudioRef?.duration || 0;
      this.notifyStateChange();
    };
  }

  // Handle track ended
  private handleMainTrackEnded(): void {
    if (this.playbackMode === PlaybackMode.LOOP_ONE) {
      // Loop is handled by audio element
      return;
    }

    if (this.playbackMode === PlaybackMode.SHUFFLE) {
      this.skipToNext();
    } else {
      // LOOP_ALL
      const nextIndex = (this.currentMusicIndex + 1) % (hasMusicPlaylist(this.currentPlaylist) ? this.currentPlaylist.musics.length : 1);
      this.playMainTrack(nextIndex);
    }
  }

  // Initialize nature sounds
  private initializeNatureSounds(playlist: MusicControlProps['playlist'], shouldAutoPlay: boolean = true): void {
    if (!hasNatureSoundsPlaylist(playlist)) return;

    // Load saved volumes from localStorage (similar to old system)
    const savedVolumes = this.loadNatureSoundVolumes();

    this.natureSounds = playlist.natureSounds.map(sound => {
      // Use saved volume if available, otherwise default to 50
      const savedVolume = savedVolumes[sound.id];
      const initialVolume = savedVolume !== undefined ? savedVolume : 50;

      return {
        id: sound.id,
        title: sound.title,
        src: sound.src || '',
        category: sound.category || [],
        isPlaying: shouldAutoPlay, // Set to auto-play state
        volume: initialVolume,
        isMuted: false,
      };
    });

    this.notifyStateChange();

    // Auto-play nature sounds if requested (similar to old system)
    if (shouldAutoPlay) {
      setTimeout(() => {
        this.natureSounds.forEach(sound => {
          if (sound.isPlaying && !sound.isMuted && sound.src) {
            this.startNatureSound(sound.id);
          }
        });
        console.log('SystemPlayerManager: Auto-playing nature sounds');
      }, 300); // Same delay as main music
    }
  }

  // Toggle nature sound
  public toggleNatureSound(soundId: string): void {
    const soundIndex = this.natureSounds.findIndex(s => s.id === soundId);
    if (soundIndex === -1) return;

    const sound = this.natureSounds[soundIndex];

    if (sound.isPlaying) {
      // Stop the sound
      const audioElement = this.audioElementsMap.get(soundId);
      if (audioElement) {
        audioElement.pause();
        audioElement.currentTime = 0;
      }
      this.natureSounds[soundIndex].isPlaying = false;
    } else {
      // Start the sound
      let audioElement = this.audioElementsMap.get(soundId);

      if (!audioElement) {
        audioElement = new Audio(sound.src);
        audioElement.loop = true;
        this.audioElementsMap.set(soundId, audioElement);
      }

      audioElement.volume = sound.volume / 100;
      audioElement.muted = sound.isMuted;

      audioElement.play()
        .then(() => {
          this.natureSounds[soundIndex].isPlaying = true;
          this.notifyStateChange();
        })
        .catch((error) => {
          console.error(`Failed to play nature sound ${sound.title}:`, error);
        });
    }

    this.notifyStateChange();
  }

  // Start nature sound (for auto-play)
  private startNatureSound(soundId: string): void {
    const soundIndex = this.natureSounds.findIndex(s => s.id === soundId);
    if (soundIndex === -1) return;

    const sound = this.natureSounds[soundIndex];
    if (!sound.src) return;

    // Create audio element if it doesn't exist
    let audioElement = this.audioElementsMap.get(soundId);
    if (!audioElement) {
      audioElement = new Audio(sound.src);
      audioElement.loop = true;
      this.audioElementsMap.set(soundId, audioElement);
    }

    // Set volume and mute state
    audioElement.volume = sound.isMuted ? 0 : sound.volume / 100;
    audioElement.muted = sound.isMuted;

    // Start playing with fade-in effect (similar to old system)
    audioElement.play()
      .then(() => {
        this.natureSounds[soundIndex].isPlaying = true;
        this.notifyStateChange();

        // Implement fade-in effect
        if (!sound.isMuted) {
          let volume = 0;
          const targetVolume = sound.volume / 100;
          audioElement!.volume = 0;

          const fadeInInterval = setInterval(() => {
            if (volume < targetVolume) {
              volume += 0.05; // Increase by 5% each step
              if (audioElement) {
                audioElement.volume = Math.min(volume, targetVolume);
              }
            } else {
              clearInterval(fadeInInterval);
            }
          }, 100); // Adjust every 100ms for smooth fade-in
        }
      })
      .catch((error) => {
        console.error(`Failed to auto-play nature sound ${sound.title}:`, error);
      });
  }

  // Handle nature sound volume
  public handleNatureSoundVolume(soundId: string, volume: number): void {
    const soundIndex = this.natureSounds.findIndex(s => s.id === soundId);
    if (soundIndex === -1) return;

    this.natureSounds[soundIndex].volume = volume;

    const audioElement = this.audioElementsMap.get(soundId);
    if (audioElement) {
      audioElement.volume = volume / 100;
    }

    this.notifyStateChange();
  }

  // Toggle nature sound mute
  public toggleNatureSoundMute(soundId: string): void {
    const soundIndex = this.natureSounds.findIndex(s => s.id === soundId);
    if (soundIndex === -1) return;

    this.natureSounds[soundIndex].isMuted = !this.natureSounds[soundIndex].isMuted;

    const audioElement = this.audioElementsMap.get(soundId);
    if (audioElement) {
      audioElement.muted = this.natureSounds[soundIndex].isMuted;
    }

    this.notifyStateChange();
  }

  // Get playing nature sounds count
  public getPlayingNatureSoundsCount(): number {
    return this.natureSounds.filter(sound => sound.isPlaying).length;
  }

  // Debounce state change notifications to prevent excessive updates
  private notificationTimeout: NodeJS.Timeout | null = null;

  // Notify state change to subscribers
  private notifyStateChange(): void {
    // Debounce notifications to prevent excessive updates
    if (this.notificationTimeout) {
      clearTimeout(this.notificationTimeout);
    }

    this.notificationTimeout = setTimeout(() => {
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('system-player-state-change', {
          detail: this.getState()
        }));
      }
      this.notificationTimeout = null;
    }, 16); // ~60fps debouncing
  }

  // Stop all audio for navigation (preserves state)
  public stopAllForNavigation(): void {
    console.log('SystemPlayerManager: Stopping all audio for navigation');

    // Pause main audio
    if (this.mainAudioRef && !this.mainAudioRef.paused) {
      this.mainAudioRef.pause();
      this.isMainPlaying = false;
    }

    // Pause all nature sounds
    this.audioElementsMap.forEach((audio) => {
      if (!audio.paused) {
        audio.pause();
      }
    });

    // Update nature sounds state to stopped
    this.natureSounds = this.natureSounds.map(sound => ({
      ...sound,
      isPlaying: false
    }));

    // Reset playback position
    this.currentTime = 0;

    this.notifyStateChange();
  }

  // Full cleanup (removes all resources)
  public cleanup(): void {
    console.log('SystemPlayerManager: Full cleanup - removing all resources');

    // Clear any pending notifications
    if (this.notificationTimeout) {
      clearTimeout(this.notificationTimeout);
      this.notificationTimeout = null;
    }

    // Cancel any ongoing play requests
    this.currentPlayPromise = null;

    if (this.mainAudioRef) {
      this.mainAudioRef.pause();
      this.mainAudioRef.onended = null;
      this.mainAudioRef.ontimeupdate = null;
      this.mainAudioRef.onloadedmetadata = null;
      this.mainAudioRef.src = '';
      this.mainAudioRef = null;
    }

    this.audioElementsMap.forEach((audio) => {
      audio.pause();
      audio.onended = null;
      audio.ontimeupdate = null;
      audio.src = '';
    });
    this.audioElementsMap.clear();

    this.isMainPlaying = false;
    this.currentTime = 0;
    this.duration = 0;
    this.natureSounds = [];
    this.isInitialized = false;
    this.isInitializing = false;
    this.lastInitializedPlaylistId = null;

    console.log('SystemPlayerManager: Full cleanup completed');
  }

  // Cleanup audio resources but preserve playlist and settings
  public cleanupAudioResources(): void {
    console.log('SystemPlayerManager: Cleaning up audio resources while preserving settings');

    if (this.mainAudioRef) {
      this.mainAudioRef.pause();
      this.mainAudioRef.onended = null;
      this.mainAudioRef.ontimeupdate = null;
      this.mainAudioRef.onloadedmetadata = null;
      this.mainAudioRef.src = '';
      this.mainAudioRef = null;
    }

    this.audioElementsMap.forEach((audio) => {
      audio.pause();
      audio.onended = null;
      audio.ontimeupdate = null;
      audio.src = '';
    });
    this.audioElementsMap.clear();

    // Reset playback state but preserve playlist and volume settings
    this.isMainPlaying = false;
    this.currentTime = 0;
    this.duration = 0;

    // Reset nature sounds playing state but preserve their configuration
    this.natureSounds = this.natureSounds.map(sound => ({
      ...sound,
      isPlaying: false
    }));

    this.notifyStateChange();
  }
}

// Export singleton instance
export const systemPlayerManager = SystemPlayerManager.getInstance();
