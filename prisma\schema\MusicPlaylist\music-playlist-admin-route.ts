import { <PERSON>o } from "hono";
import { zValida<PERSON> } from "@hono/zod-validator";
import { createMusicPlaylistSchemaAdmin, updateMusicPlaylistSchemaAdmin } from "./music-playlist-type";
import prisma from "@/lib/prisma";
import { UserVariable } from "../..";
import { UserRole } from "@prisma/client";
import { z } from "zod";
import { adminMiddleware } from "@/server/private/middleware";

const app = new Hono<{ Variables: UserVariable }>()
  // Get all music playlists (Admin can see all)
  .get("/", adminMiddleware, async (c) => {
    const isPublic = c.req.query("isPublic") === "true" ? true : c.req.query("isPublic") === "false" ? false : undefined;
    const isDefault = c.req.query("isDefault") === "true" ? true : c.req.query("isDefault") === "false" ? false : undefined;

    const filters: Record<string, unknown> = {};

    // Add filters based on query parameters
    if (isPublic !== undefined) {
      filters.isPublic = isPublic;
    }

    if (isDefault !== undefined) {
      filters.isDefault = isDefault;
    }

    // Admin can access all playlists
    const musicPlaylists = await prisma.musicPlaylist.findMany({
      where: filters,
      include: {
        videos: {
          select: {
            id: true,
            title: true,
            thumbnail: true
          }
        },
        musics: {
          select: {
            id: true,
            title: true,
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
    });

    return c.json({ data: musicPlaylists });
  })

  // Get single music playlist (Admin can access any)
  .get("/:id", adminMiddleware, async (c) => {
    const id = c.req.param("id");

    const musicPlaylist = await prisma.musicPlaylist.findUnique({
      where: { id },
      include: {
        videos: true,
        musics: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
    });

    console.log("musicPlaylist server", musicPlaylist);

    if (!musicPlaylist) {
      return c.json({ error: "Music playlist not found" }, 404);
    }

    return c.json({ data: musicPlaylist });
  })

  // Create music playlist (Admin)
  .post("/", adminMiddleware, zValidator("form", createMusicPlaylistSchemaAdmin), async (c) => {
    const user = c.get("user");
    const { name, description, isPublic, imageUrl, genres, videoIds, musicIds } = c.req.valid("form");

    // Create relationships with videos and music tracks if provided
    const connectVideos = videoIds && videoIds.length > 0 
      ? { connect: videoIds.map(id => ({ id })) } 
      : undefined;
      
    const connectMusics = musicIds && musicIds.length > 0 
      ? { connect: musicIds.map(id => ({ id })) } 
      : undefined;

    // Create music playlist in database
    const musicPlaylist = await prisma.musicPlaylist.create({
      data: {
        name,
        description,
        isPublic: isPublic ?? false,
        imageUrl: imageUrl || "",
        genres: genres || [],
        userId: user.id,
        creatorType: UserRole.ADMIN, // Always set as ADMIN for admin-created playlists
        videos: connectVideos,
        musics: connectMusics,
      },
      include: {
        videos: true,
        musics: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    return c.json({ data: musicPlaylist });
  })

  // Update music playlist (Admin can update any)
  .patch("/:id", adminMiddleware, zValidator("form", updateMusicPlaylistSchemaAdmin), async (c) => {
    const id = c.req.param("id");
    const updates = c.req.valid("form");

    // Check if music playlist exists
    const existingMusicPlaylist = await prisma.musicPlaylist.findUnique({
      where: { id },
    });

    if (!existingMusicPlaylist) {
      return c.json({ error: "Music playlist not found" }, 404);
    }

    // Prepare update data with proper typing
    const updateData = {
      ...(updates.name !== undefined && { name: updates.name }),
      ...(updates.description !== undefined && { description: updates.description }),
      ...(updates.isPublic !== undefined && { isPublic: updates.isPublic }),
      ...(updates.imageUrl !== undefined && { imageUrl: updates.imageUrl }),
      ...(updates.genres !== undefined && { genres: updates.genres }),
    };

    // Handle video and music connections/disconnections
    let relationshipUpdates = {};
    
    // Videos
    if (updates.videoIds !== undefined) {
      relationshipUpdates = {
        ...relationshipUpdates,
        videos: {
          ...(updates.videoIds.length > 0 
            ? { set: updates.videoIds.map(id => ({ id })) }
            : { set: [] })
        }
      };
    }
    
    // Music tracks
    if (updates.musicIds !== undefined) {
      relationshipUpdates = {
        ...relationshipUpdates,
        musics: {
          ...(updates.musicIds.length > 0 
            ? { set: updates.musicIds.map(id => ({ id })) }
            : { set: [] })
        }
      };
    }

    // Update the music playlist
    const updatedMusicPlaylist = await prisma.musicPlaylist.update({
      where: { id },
      data: {
        ...updateData,
        ...relationshipUpdates
      },
      include: {
        videos: true,
        musics: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    return c.json({ data: updatedMusicPlaylist });
  })

  // Delete music playlist (Admin can delete any except defaults)
  .delete("/:id", adminMiddleware, async (c) => {
    const id = c.req.param("id");

    // Find the music playlist
    const musicPlaylist = await prisma.musicPlaylist.findUnique({
      where: { id },
    });

    if (!musicPlaylist) {
      return c.json({ error: "Music playlist not found" }, 404);
    }

    // Check if this is a default playlist which shouldn't be deleted
    if (musicPlaylist.isDefault) {
      return c.json({ error: "Cannot delete a default music playlist" }, 403);
    }

    // Delete music playlist
    await prisma.musicPlaylist.delete({
      where: { id },
    });

    return c.json({ success: true });
  })

  // Music Management (Admin)
  .post("/:id/music", adminMiddleware, zValidator("json", z.object({
    musicIds: z.array(z.string())
  })), async (c) => {
    const musicPlaylistId = c.req.param("id");
    const { musicIds } = c.req.valid("json");

    // Check music playlist exists
    const musicPlaylist = await prisma.musicPlaylist.findUnique({
      where: { id: musicPlaylistId },
    });

    if (!musicPlaylist) {
      return c.json({ error: "Music playlist not found" }, 404);
    }

    // Get current musicOrder
    const currentMusicOrder = musicPlaylist.musicOrder || [];

    // Add new music IDs to the end of the order
    const newMusicOrder = [...currentMusicOrder, ...musicIds];

    // Add music items to playlist and update musicOrder
    const updatedMusicPlaylist = await prisma.musicPlaylist.update({
      where: { id: musicPlaylistId },
      data: {
        musics: {
          connect: musicIds.map(id => ({ id }))
        },
        musicOrder: newMusicOrder
      },
      include: {
        musics: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    return c.json({ data: updatedMusicPlaylist });
  })

  // Reorder music in playlist (Admin)
  .patch("/:id/music/reorder", adminMiddleware, zValidator("json", z.object({
    musicOrder: z.array(z.string())
  })), async (c) => {
    const musicPlaylistId = c.req.param("id");
    const { musicOrder } = c.req.valid("json");

    // Check music playlist exists
    const musicPlaylist = await prisma.musicPlaylist.findUnique({
      where: { id: musicPlaylistId },
      include: {
        musics: {
          select: { id: true }
        }
      }
    });

    if (!musicPlaylist) {
      return c.json({ error: "Music playlist not found" }, 404);
    }

    // Validate that all music IDs in the new order exist in the playlist
    const musicPlaylistMusicIds = new Set(musicPlaylist.musics.map(m => m.id));
    const hasInvalidIds = musicOrder.some(id => !musicPlaylistMusicIds.has(id));
    
    if (hasInvalidIds) {
      return c.json({ error: "Invalid music IDs in order array" }, 400);
    }

    // Update the music playlist with new music order
    const updatedMusicPlaylist = await prisma.musicPlaylist.update({
      where: { id: musicPlaylistId },
      data: {
        musicOrder
      },
      include: {
        musics: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    return c.json({ data: updatedMusicPlaylist });
  })

  // Remove a specific music from playlist (Admin)
  .delete("/:id/music/:musicId", adminMiddleware, async (c) => {
    const musicPlaylistId = c.req.param("id");
    const musicId = c.req.param("musicId");

    // Check music playlist exists
    const musicPlaylist = await prisma.musicPlaylist.findUnique({
      where: { id: musicPlaylistId },
      include: {
        musics: {
          where: { id: musicId },
          select: { id: true }
        }
      }
    });

    if (!musicPlaylist) {
      return c.json({ error: "Music playlist not found" }, 404);
    }

    // Check if music exists in playlist
    if (musicPlaylist.musics.length === 0) {
      return c.json({ error: "Music not found in music playlist" }, 404);
    }

    // Get current musicOrder and remove the musicId
    const currentMusicOrder = musicPlaylist.musicOrder || [];
    const newMusicOrder = currentMusicOrder.filter(id => id !== musicId);

    // Remove music from playlist and update musicOrder
    const updatedMusicPlaylist = await prisma.musicPlaylist.update({
      where: { id: musicPlaylistId },
      data: {
        musics: {
          disconnect: { id: musicId }
        },
        musicOrder: newMusicOrder
      }
    });

    return c.json({ data: updatedMusicPlaylist });
  })

  // Videos Management (Admin)
  .post("/:id/videos", adminMiddleware, zValidator("json", z.object({
    videoIds: z.array(z.string())
  })), async (c) => {
    const musicPlaylistId = c.req.param("id");
    const { videoIds } = c.req.valid("json");

    // Check music playlist exists
    const musicPlaylist = await prisma.musicPlaylist.findUnique({
      where: { id: musicPlaylistId },
    });

    if (!musicPlaylist) {
      return c.json({ error: "Music playlist not found" }, 404);
    }

    // Add videos to music playlist
    const updatedMusicPlaylist = await prisma.musicPlaylist.update({
      where: { id: musicPlaylistId },
      data: {
        videos: {
          connect: videoIds.map(id => ({ id }))
        }
      },
      include: {
        videos: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    return c.json({ data: updatedMusicPlaylist });
  })

  // Remove a specific video from music playlist (Admin)
  .delete("/:id/videos/:videoId", adminMiddleware, async (c) => {
    const musicPlaylistId = c.req.param("id");
    const videoId = c.req.param("videoId");

    // Check music playlist exists
    const musicPlaylist = await prisma.musicPlaylist.findUnique({
      where: { id: musicPlaylistId },
      include: {
        videos: {
          where: { id: videoId },
          select: { id: true }
        }
      }
    });

    if (!musicPlaylist) {
      return c.json({ error: "Music playlist not found" }, 404);
    }

    // Check if video exists in playlist
    if (musicPlaylist.videos.length === 0) {
      return c.json({ error: "Video not found in music playlist" }, 404);
    }

    // Remove video from music playlist
    const updatedMusicPlaylist = await prisma.musicPlaylist.update({
      where: { id: musicPlaylistId },
      data: {
        videos: {
          disconnect: { id: videoId }
        }
      }
    });

    return c.json({ data: updatedMusicPlaylist });
  });

export default app; 