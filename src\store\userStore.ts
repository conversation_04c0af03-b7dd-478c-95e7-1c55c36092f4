"use client";

import { create } from "zustand";
import { persist } from "zustand/middleware";
import type {
  User,
  Session,
  UserSession,
  UserState,
  UserPreferences,
  UserStats
} from "@/types/user";

// Default preferences
const defaultPreferences: UserPreferences = {
  theme: "light",
  notifications: {
    email: true,
    push: true,
    pomodoroReminders: true,
    breakReminders: true,
  },
  pomodoro: {
    autoStartBreaks: false,
    autoStartPomodoros: false,
    soundEnabled: true,
    volume: 50,
  },
  privacy: {
    profileVisible: true,
    statsVisible: true,
  },
  ui: {
    dismissedSignInPrompt: false,
  },
};

// Default stats
const defaultStats: UserStats = {
  totalPomodoroSessions: 0,
  totalFocusTime: 0,
  currentStreak: 0,
  longestStreak: 0,
  tasksCompleted: 0,
  joinedDate: new Date().toISOString(),
  lastActiveDate: new Date().toISOString(),
};

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      session: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      preferences: {
        ...defaultPreferences,
        // Ensure ui preferences are always initialized
        ui: defaultPreferences.ui || { dismissedSignInPrompt: false },
      },
      stats: defaultStats,

      // Actions
      setUser: (user: User | null) => {
        set((state) => ({
          user,
          isAuthenticated: !!user,
          error: null,
          // Update stats join date if this is the first time setting user
          stats: user && !state.user ? {
            ...state.stats,
            joinedDate: user.createdAt,
          } : state.stats,
        }));
      },

      setSession: (session: Session | null) => {
        set({
          session,
          error: null,
        });
      },

      setUserSession: (userSession: UserSession | null) => {
        if (userSession) {
          set((state) => ({
            user: userSession.user,
            session: userSession.session,
            isAuthenticated: true,
            error: null,
            // Update stats join date if this is the first time setting user
            stats: !state.user ? {
              ...state.stats,
              joinedDate: userSession.user.createdAt,
            } : state.stats,
          }));
        } else {
          set({
            user: null,
            session: null,
            isAuthenticated: false,
            error: null,
          });
        }
      },

      updateUser: (updates: Partial<User>) => {
        set((state) => ({
          user: state.user ? { ...state.user, ...updates } : null,
          error: null,
        }));
      },

      updatePreferences: (preferences: Partial<UserPreferences>) => {
        set((state) => ({
          preferences: {
            ...state.preferences,
            ...preferences,
            notifications: {
              ...state.preferences.notifications,
              ...(preferences.notifications || {}),
            },
            pomodoro: {
              ...state.preferences.pomodoro,
              ...(preferences.pomodoro || {}),
            },
            privacy: {
              ...state.preferences.privacy,
              ...(preferences.privacy || {}),
            },
            ui: {
              ...(state.preferences.ui || { dismissedSignInPrompt: false }),
              ...(preferences.ui || {}),
            },
          },
        }));
      },

      updateStats: (stats: Partial<UserStats>) => {
        set((state) => ({
          stats: {
            ...state.stats,
            ...stats,
            lastActiveDate: new Date().toISOString(),
          },
        }));
      },

      clearUser: () => {
        set({
          user: null,
          session: null,
          isAuthenticated: false,
          error: null,
          isLoading: false,
        });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error, isLoading: false });
      },

      // Computed getters
      isAdmin: () => {
        const { user } = get();
        return user?.role === "ADMIN";
      },

      isPremium: () => {
        const { user } = get();
        return user?.premium === true ||
               user?.subscriptionType === "PREMIUM" ||
               user?.subscriptionType === "PREMIUM_PLUS";
      },

      isSessionValid: () => {
        const { session } = get();
        if (!session) return false;

        const expirationDate = new Date(session.expiresAt);
        const now = new Date();
        return expirationDate > now;
      },

      getDisplayName: () => {
        const { user } = get();
        return user?.name || "Anonymous User";
      },

      getAvatarUrl: () => {
        const { user } = get();
        return user?.image || null;
      },
    }),
    {
      name: "user-storage",
      partialize: (state) => ({
        // Persist user data, preferences, and stats
        user: state.user,
        session: state.session,
        isAuthenticated: state.isAuthenticated,
        preferences: state.preferences,
        stats: state.stats,
        // Don't persist loading states and errors
      }),
      onRehydrateStorage: () => {
        return (state) => {
          if (state) {
            // Check if session is still valid on rehydration
            if (state.session && !state.isSessionValid()) {
              // Session expired, clear user data
              state.clearUser();
            }

            // Ensure ui preferences exist (migration for existing users)
            if (!state.preferences.ui) {
              state.preferences.ui = {
                dismissedSignInPrompt: false,
              };
            }

            // Update last active date
            state.updateStats({ lastActiveDate: new Date().toISOString() });
          }
        };
      },
    }
  )
);

// Helper functions for external use
export const userStoreHelpers = {
  // Initialize user from session data (like the one you provided)
  initializeFromSessionData: (sessionData: any) => {
    const { setUserSession } = useUserStore.getState();

    if (sessionData?.session && sessionData?.user) {
      const userSession: UserSession = {
        user: {
          id: sessionData.user.id,
          name: sessionData.user.name,
          email: sessionData.user.email,
          emailVerified: sessionData.user.emailVerified,
          image: sessionData.user.image,
          createdAt: sessionData.user.createdAt,
          updatedAt: sessionData.user.updatedAt,
          role: sessionData.user.role === "ADMIN" ? "ADMIN" : "USER",
          banned: sessionData.user.banned || false,
          banReason: sessionData.user.banReason,
          banExpires: sessionData.user.banExpires,
          premium: sessionData.user.premium || false,
          subscriptionType: sessionData.user.subscriptionType || "FREE",
        },
        session: {
          id: sessionData.session.id,
          token: sessionData.session.token,
          expiresAt: sessionData.session.expiresAt,
          createdAt: sessionData.session.createdAt,
          updatedAt: sessionData.session.updatedAt,
          ipAddress: sessionData.session.ipAddress,
          userAgent: sessionData.session.userAgent,
          userId: sessionData.session.userId,
          impersonatedBy: sessionData.session.impersonatedBy,
        },
      };

      setUserSession(userSession);
    } else {
      setUserSession(null);
    }
  },

  // Get current user safely
  getCurrentUser: () => {
    const { user } = useUserStore.getState();
    return user;
  },

  // Check authentication status
  isAuthenticated: () => {
    const { isAuthenticated, isSessionValid } = useUserStore.getState();
    return isAuthenticated && isSessionValid();
  },
};
