"use client"

import { Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

interface FocusFlowChartProps {
  data: Array<{
    time: string
    flowScore: number
  }>
}

export function FocusFlowChart({ data }: FocusFlowChartProps) {
  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const flowScore = payload[0].value

      let flowState = "Distracted"
      let flowColor = "text-rose-500"

      if (flowScore >= 80) {
        flowState = "Deep Flow"
        flowColor = "text-emerald-500"
      } else if (flowScore >= 60) {
        flowState = "Flow"
        flowColor = "text-amber-500"
      } else if (flowScore >= 40) {
        flowState = "Focused"
        flowColor = "text-blue-500"
      }

      return (
        <div className="rounded-md border border-slate-700 bg-slate-800 p-3 shadow-md">
          <p className="mb-1 font-medium">{label}</p>
          <p className={flowColor}>
            <span className="font-bold">{flowScore}</span> - {flowState}
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <AreaChart data={data} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
        <defs>
          <linearGradient id="flowGradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#f43f5e" stopOpacity={0.8} />
            <stop offset="95%" stopColor="#f43f5e" stopOpacity={0} />
          </linearGradient>
        </defs>
        <CartesianGrid strokeDasharray="3 3" stroke="#334155" vertical={false} />
        <XAxis dataKey="time" stroke="#64748b" tickLine={false} axisLine={false} tick={{ fontSize: 12 }} />
        <YAxis
          stroke="#64748b"
          tickLine={false}
          axisLine={false}
          tick={{ fontSize: 12 }}
          width={30}
          domain={[0, 100]}
          ticks={[0, 25, 50, 75, 100]}
        />
        <Tooltip content={<CustomTooltip />} />
        <Area
          type="monotone"
          dataKey="flowScore"
          stroke="#f43f5e"
          fillOpacity={1}
          fill="url(#flowGradient)"
          strokeWidth={2}
        />
      </AreaChart>
    </ResponsiveContainer>
  )
}
