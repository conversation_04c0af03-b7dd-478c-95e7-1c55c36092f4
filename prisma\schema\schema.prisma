// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

datasource db {
  provider     = "postgresql"
  url          = env("POSTGRES_DATABASE_URL")
  relationMode = "prisma"
}

generator client {
  provider = "prisma-client-js"
  output   = "../../node_modules/.prisma/client"
}

generator zod {
  provider                  = "npx zod-prisma-types"
  output                    = "../zod-auto-types" // default is ./generated/zod
  useMultipleFiles          = true // default is false
  writeBarrelFiles          = true // default is true
  createInputTypes          = false // default is true
  createModelTypes          = true // default is true
  addInputTypeValidation    = true // default is true
  addIncludeType            = true // default is true
  addSelectType             = true // default is true
  validateWhereUniqueInput  = true // default is true
  // createOptionalDefaultValuesTypes = true // default is false
  createRelationValuesTypes = true // default is false
  createPartialTypes        = true // default is false
  // useDefaultValidators             = false // default is true
  // coerceDate                       = false // default is true
  writeNullishInModelTypes  = true // default is false
  // prismaClientPath = "./path/to/prisma/client" // default is client output path
}
