import { <PERSON><PERSON> } from "hono";
import { zValidator } from "@hono/zod-validator";
import { createMusicSchema, updateMusicSchema } from "./music-type";
import prisma from "@/lib/prisma";
import { UserVariable } from "../..";
import { UserRole } from "@prisma/client";
import { privateRoutesMiddleware } from "@/server/private/middleware";

const app = new Hono<{ Variables: UserVariable }>()
  // Get all music tracks
  .get("/", async (c) => {
    // We don't use the user variable in this route anymore
    const isPublic = c.req.query("isPublic") === "true" ? true : c.req.query("isPublic") === "false" ? false : undefined;
    const playlistId = c.req.query("playlistId");

    console.log({
      isPublic,
      playlistId,
    });

    const filters: Record<string, unknown> = {};

    // Add filters based on query parameters
    if (isPublic !== undefined) {
      filters.isPublic = isPublic;
    }

    // Instead of directly filtering by playlistId, we'll need to adjust our query
    const whereCondition: any = { ...filters };
    
    const music = await prisma.music.findMany({
      where: playlistId ? {
        ...whereCondition,
        musicPlaylists: {
          some: {
            id: playlistId
          }
        }
      } : whereCondition,
      include: {
        musicPlaylists: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc'
      },
    });

    return c.json({ data: music });
  })

  // Get user music tracks (limited fields for dashboard users)
  .get("/user", async (c) => {
    const isPublic = c.req.query("isPublic") === "true" ? true : c.req.query("isPublic") === "false" ? false : undefined;
    const playlistId = c.req.query("playlistId");

    const filters: Record<string, unknown> = {};

    // Add filters based on query parameters
    if (isPublic !== undefined) {
      filters.isPublic = isPublic;
    }

    // Instead of directly filtering by playlistId, we'll need to adjust our query
    const whereCondition: any = { ...filters };

    const music = await prisma.music.findMany({
      where: playlistId ? {
        ...whereCondition,
        musicPlaylists: {
          some: {
            id: playlistId
          }
        }
      } : whereCondition,
      select: {
        id: true,
        title: true,
        src: true,
        genres: true,
        isPublic: true,
        duration: true,
        note: true,
        useAsOpeningMusic: true,
        userId: true,
        creatorType: true,
        createdAt: true,
        updatedAt: true,
        musicPlaylists: {
          select: {
            id: true,
            name: true,
          },
        },
        // Exclude: source, rating, isCopyright
      },
      orderBy: {
        createdAt: 'desc'
      },
    });

    return c.json({ data: music });
  })

  // Get single music track
  .get("/:id", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");

    const music = await prisma.music.findUnique({
      where: { id },
      include: {
        musicPlaylists: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!music) {
      return c.json({ error: "Music track not found" }, 404);
    }

    // Check if user can access this music track
    const canAccess =
      music.isPublic ||
      music.creatorType === UserRole.ADMIN ||
      music.userId === user.id;

    if (!canAccess) {
      return c.json({ error: "Unauthorized" }, 403);
    }

    return c.json({ data: music });
  })

  // Create music track - only admin can create
  .post("/", privateRoutesMiddleware, zValidator("form", createMusicSchema), async (c) => {
    const user = c.get("user");
    
    // Check if user is admin
    if (user.role !== UserRole.ADMIN) {
      return c.json({ error: "Only admins can create music tracks" }, 403);
    }

    const { title, src, source, rating, isPublic, isCopyright, playlistId, genres } = c.req.valid("form");

    // Create music track in database
    const music = await prisma.music.create({
      data: {
        title,
        src: src || "",
        source,
        rating,
        isPublic: isPublic ?? false,
        isCopyright: isCopyright ?? false,
        userId: user.id,
        creatorType: user.role as UserRole, // Set creatorType based on user's role
        genres: genres || [],
        ...(playlistId ? {
          musicPlaylists: {
            connect: {
              id: playlistId
            }
          }
        } : {})
      },
    });

    return c.json({ data: music });
  })

  // Update music track - only owner or admin can update admin-created content
  .patch("/:id", privateRoutesMiddleware, zValidator("form", updateMusicSchema), async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");
    const updates = c.req.valid("form");

    // First check if music track exists
    const existingMusic = await prisma.music.findUnique({
      where: { id },
    });

    if (!existingMusic) {
      return c.json({ error: "Music track not found" }, 404);
    }

    // Security check: Only allow updates if:
    // 1. User owns the music, OR
    // 2. User is admin (can update any music), OR
    // 3. Admin-created content can only be updated by admins
    const canUpdate = 
      existingMusic.userId === user.id ||
      (user.role === UserRole.ADMIN) ||
      (existingMusic.creatorType === UserRole.ADMIN && user.role !== UserRole.ADMIN ? false : true);

    if (!canUpdate || (existingMusic.creatorType === UserRole.ADMIN && user.role !== UserRole.ADMIN)) {
      return c.json({ error: "Unauthorized to update this music track" }, 403);
    }

    // Prepare update data with proper typing
    const updateData = {
      ...(updates.title !== undefined && { title: updates.title }),
      ...(updates.src !== undefined && { src: updates.src }),
      ...(updates.source !== undefined && { source: updates.source }),
      ...(updates.rating !== undefined && { rating: updates.rating }),
      ...(updates.isPublic !== undefined && { isPublic: updates.isPublic }),
      ...(updates.isCopyright !== undefined && { isCopyright: updates.isCopyright }),
      ...(updates.genres !== undefined && { genres: updates.genres }),
    };

    // Handle playlist connection/disconnection if needed
    let playlistUpdateOperation = {};
    if (updates.playlistId !== undefined) {
      if (updates.playlistId) {
        // Connect to the specified playlist
        playlistUpdateOperation = {
          musicPlaylists: {
            connect: {
              id: updates.playlistId
            }
          }
        };
      } else {
        // If playlistId is null or empty, disconnect from any playlists
        // This would require fetching existing playlists and disconnecting them
        const existingPlaylists = await prisma.musicPlaylist.findMany({
          where: {
            musics: {
              some: {
                id
              }
            }
          }
        });
        
        if (existingPlaylists.length > 0) {
          playlistUpdateOperation = {
            musicPlaylists: {
              disconnect: existingPlaylists.map((playlist) => ({ id: playlist.id }))
            }
          };
        }
      }
    }

    // Update the music track
    const updatedMusic = await prisma.music.update({
      where: { id },
      data: {
        ...updateData,
        ...playlistUpdateOperation
      },
    });

    return c.json({ data: updatedMusic });
  })

  // Delete music track - only admin can delete
  .delete("/:id", privateRoutesMiddleware, async (c) => {
    const user = c.get("user");
    const id = c.req.param("id");

    // Check if user is admin
    if (user.role !== UserRole.ADMIN) {
      return c.json({ error: "Only admins can delete music tracks" }, 403);
    }

    // Find the music track
    const music = await prisma.music.findUnique({
      where: { id },
    });

    if (!music) {
      return c.json({ error: "Music track not found" }, 404);
    }

    // Delete music track
    await prisma.music.delete({
      where: { id },
    });

    return c.json({ success: true });
  });

export default app; 