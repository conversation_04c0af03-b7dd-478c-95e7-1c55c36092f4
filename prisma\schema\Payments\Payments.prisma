// Track user subscriptions
model Subscription {
    id                 String                @id @default(cuid())
    userId             String
    planId             String // Required field for plan reference
    status             SubscriptionStatus    @default(ACTIVE)
    currentPeriodStart DateTime
    currentPeriodEnd   DateTime
    cancelAtPeriodEnd  Boolean               @default(false)
    canceledAt         DateTime?
    externalId         String? // Polar subscription ID
    checkoutId         String? // Polar checkout ID
    productId          String? // Polar product ID
    priceId            String? // Polar price ID
    subscriptionType   SubscriptionType      @default(FREE) // Name of the subscribed product
    price              Float? // Price amount
    currency           String                @default("USD")
    interval           SubscriptionInterval? // Subscription interval
    lastPaymentDate    DateTime? // Date of last successful payment
    createdAt          DateTime              @default(now())
    updatedAt          DateTime              @updatedAt
    polarMetadata      Json? // Store additional Polar-specific data

    user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    payments Payment[]

    @@index([userId])
    @@index([planId])
    @@index([status])
    @@index([externalId])
    @@index([checkoutId])
    @@index([productId])
    @@index([priceId])
}

// Track individual payment transactions
model Payment {
    id               String           @id @default(cuid())
    subscriptionId   String?
    userId           String?
    amount           Float
    currency         String           @default("USD")
    status           PaymentStatus    @default(PENDING)
    externalId       String? // Polar payment/order ID
    checkoutId       String? // Polar checkout ID
    productId        String? // Polar product ID
    priceId          String? // Polar price ID
    subscriptionType SubscriptionType @default(FREE) // Name of the subscribed product
    metadata         Json? // Additional payment data
    createdAt        DateTime         @default(now())
    updatedAt        DateTime         @updatedAt

    subscription Subscription? @relation(fields: [subscriptionId], references: [id])
    user         User?         @relation(fields: [userId], references: [id], onDelete: SetNull)

    @@index([userId])
    @@index([subscriptionId])
    @@index([status])
    @@index([externalId])
    @@index([checkoutId])
    @@index([productId])
    @@index([priceId])
}

// Enums for standardizing statuses and types
enum SubscriptionStatus {
    ACTIVE
    PAST_DUE
    CANCELED
    TRIALING
    INCOMPLETE
}

enum SubscriptionInterval {
    MONTH
    YEAR
}

enum PaymentStatus {
    PENDING
    SUCCEEDED
    FAILED
}
