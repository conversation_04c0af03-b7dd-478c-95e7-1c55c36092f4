'use client';

/**
 * iOS-safe video loading utilities
 * Handles iOS Safari's strict autoplay policies and video loading restrictions
 */

export interface VideoLoadOptions {
  autoplay?: boolean;
  muted?: boolean;
  volume?: number;
  preload?: 'none' | 'metadata' | 'auto';
  onCanPlay?: () => void;
  onError?: (error: Error) => void;
  onLoadStart?: () => void;
}

export interface DeviceInfo {
  isIOS: boolean;
  isSafari: boolean;
  isPrivateBrowsing: boolean;
  supportsAutoplay: boolean;
  version?: string;
}

/**
 * Detect device and browser capabilities
 */
export function getDeviceInfo(): DeviceInfo {
  if (typeof window === 'undefined') {
    return {
      isIOS: false,
      isSafari: false,
      isPrivateBrowsing: false,
      supportsAutoplay: false,
    };
  }

  const userAgent = navigator.userAgent;
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
  
  // Detect private browsing (simplified check)
  let isPrivateBrowsing = false;
  try {
    localStorage.setItem('test', 'test');
    localStorage.removeItem('test');
  } catch (e) {
    isPrivateBrowsing = true;
  }

  // iOS version detection
  const iosMatch = userAgent.match(/OS (\d+)_(\d+)/);
  const version = iosMatch ? `${iosMatch[1]}.${iosMatch[2]}` : undefined;

  // Autoplay support detection (basic heuristic)
  const supportsAutoplay = !isIOS || (isIOS && parseFloat(version || '0') >= 10);

  return {
    isIOS,
    isSafari,
    isPrivateBrowsing,
    supportsAutoplay,
    version,
  };
}

/**
 * iOS-safe video element creation and configuration
 */
export function createIOSSafeVideo(src: string, options: VideoLoadOptions = {}): HTMLVideoElement {
  const video = document.createElement('video');
  const deviceInfo = getDeviceInfo();

  // Set basic attributes
  video.src = src;
  video.playsInline = true; // Essential for iOS
  video.loop = true;
  video.preload = options.preload || (deviceInfo.isIOS ? 'metadata' : 'auto');

  // Handle muting for iOS autoplay
  if (deviceInfo.isIOS && options.autoplay) {
    video.muted = true; // iOS requires muted for autoplay
  } else {
    video.muted = options.muted || false;
  }

  // Set volume (only if not muted)
  if (!video.muted && options.volume !== undefined) {
    video.volume = Math.max(0, Math.min(1, options.volume));
  }

  // Add event listeners
  if (options.onLoadStart) {
    video.addEventListener('loadstart', options.onLoadStart);
  }

  if (options.onCanPlay) {
    video.addEventListener('canplay', options.onCanPlay);
  }

  if (options.onError) {
    video.addEventListener('error', () => {
      const error = new Error(`Video loading failed: ${video.error?.message || 'Unknown error'}`);
      options.onError!(error);
    });
  }

  return video;
}

/**
 * Safe video play with iOS fallbacks
 */
export async function safeVideoPlay(
  video: HTMLVideoElement, 
  options: { fallbackToMuted?: boolean; maxRetries?: number } = {}
): Promise<boolean> {
  const { fallbackToMuted = true, maxRetries = 2 } = options;
  const deviceInfo = getDeviceInfo();

  let attempts = 0;
  let lastError: Error | null = null;

  while (attempts < maxRetries) {
    try {
      // On iOS, ensure the video is ready
      if (deviceInfo.isIOS && video.readyState < 2) {
        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => reject(new Error('Video load timeout')), 5000);
          
          const onCanPlay = () => {
            clearTimeout(timeout);
            video.removeEventListener('canplay', onCanPlay);
            video.removeEventListener('error', onError);
            resolve(void 0);
          };
          
          const onError = () => {
            clearTimeout(timeout);
            video.removeEventListener('canplay', onCanPlay);
            video.removeEventListener('error', onError);
            reject(new Error('Video failed to load'));
          };

          video.addEventListener('canplay', onCanPlay);
          video.addEventListener('error', onError);
        });
      }

      // Attempt to play
      const playPromise = video.play();
      
      if (playPromise !== undefined) {
        await playPromise;
      }
      
      return true; // Success
      
    } catch (error) {
      lastError = error as Error;
      attempts++;

      console.warn(`Video play attempt ${attempts} failed:`, error);

      // On first failure, try with muted if not already muted
      if (attempts === 1 && fallbackToMuted && !video.muted) {
        console.log('Retrying video play with muted audio...');
        video.muted = true;
        continue;
      }

      // On iOS, try reloading the video element
      if (attempts === 2 && deviceInfo.isIOS) {
        console.log('Reloading video element for iOS...');
        const currentTime = video.currentTime;
        video.load();
        video.currentTime = currentTime;
        continue;
      }
    }
  }

  console.error('All video play attempts failed:', lastError);
  return false;
}

/**
 * Safe video cleanup for iOS
 */
export function safeVideoCleanup(video: HTMLVideoElement): void {
  try {
    // Pause the video
    video.pause();
    
    // Remove all event listeners by cloning the element
    const newVideo = video.cloneNode(true) as HTMLVideoElement;
    video.parentNode?.replaceChild(newVideo, video);
    
    // Clear source and load to free memory
    video.removeAttribute('src');
    video.load();
    
  } catch (error) {
    console.warn('Error during video cleanup:', error);
  }
}

/**
 * Check if autoplay is likely to work
 */
export async function checkAutoplaySupport(): Promise<boolean> {
  if (typeof window === 'undefined') return false;

  const deviceInfo = getDeviceInfo();
  
  // iOS generally doesn't support unmuted autoplay
  if (deviceInfo.isIOS) {
    return false;
  }

  // Create a test video element
  const testVideo = document.createElement('video');
  testVideo.muted = true;
  testVideo.playsInline = true;
  testVideo.style.position = 'absolute';
  testVideo.style.top = '-9999px';
  testVideo.style.left = '-9999px';
  testVideo.style.width = '1px';
  testVideo.style.height = '1px';

  // Use a data URL for a minimal video
  testVideo.src = 'data:video/mp4;base64,AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMWF2YzEAAAAIZnJlZQAAABBtZGF0AAAADGWIhAAV//728P/+';

  document.body.appendChild(testVideo);

  try {
    const playPromise = testVideo.play();
    if (playPromise !== undefined) {
      await playPromise;
    }
    document.body.removeChild(testVideo);
    return true;
  } catch (error) {
    document.body.removeChild(testVideo);
    return false;
  }
}

/**
 * iOS-safe image preloading
 */
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => resolve();
    img.onerror = () => reject(new Error(`Failed to load image: ${src}`));
    
    // Set loading attributes for better performance
    img.loading = 'eager';
    img.fetchPriority = 'high';
    img.src = src;
  });
}
