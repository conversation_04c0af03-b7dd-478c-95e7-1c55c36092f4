import { Prisma } from '@prisma/client';
import { Video } from '@/lib/pomodoro-store';

// Define the Prisma Video type with includes
export type PrismaVideoWithIncludes = Prisma.VideoGetPayload<{
  include: {
    musicPlaylist: {
      include: {
        musics: true;
      };
    };
    naturePlaylist: {
      include: {
        natureSounds: true;
      };
    };
  };
}>;

// Helper function to convert Prisma Video to app Video type
export function mapPrismaVideoToVideo(prismaVideo: PrismaVideoWithIncludes): Video {
  return {
    id: prismaVideo.id,
    title: prismaVideo.title,
    src: prismaVideo.src,
    thumbnail: prismaVideo.thumbnail || '',
    description: prismaVideo.description || undefined,
    isPublic: prismaVideo.isPublic || undefined,
    isPremium: prismaVideo.isPremium || undefined,
    userId: prismaVideo.userId || undefined,
    creatorType: prismaVideo.creatorType || undefined,
    videoGenre: prismaVideo.videoGenre as string[] || undefined,
    playlistId: prismaVideo.musicPlaylistId || prismaVideo.naturePlaylistId || undefined,
    playlist: createCombinedPlaylist(prismaVideo),
    isFavorite: false, // Default to false as we don't know favorite status
    order: prismaVideo.order,
  };
}

// Helper function to create a combined playlist from music and nature playlists
function createCombinedPlaylist(prismaVideo: PrismaVideoWithIncludes): Video['playlist'] | undefined {
  const musicPlaylist = prismaVideo.musicPlaylist;
  const naturePlaylist = prismaVideo.naturePlaylist;

  // If neither playlist exists, return undefined
  if (!musicPlaylist && !naturePlaylist) {
    return undefined;
  }

  // Use music playlist as the primary if available, otherwise use nature playlist
  const primaryPlaylist = musicPlaylist || naturePlaylist;

  if (!primaryPlaylist) {
    return undefined;
  }

  // Helper function to sort items by order array
  const sortByOrder = <T extends { id: string }>(items: T[], orderArray: string[]): T[] => {
    if (!orderArray || orderArray.length === 0) {
      return items; // Return original order if no order array
    }

    // Create a map for quick lookup of order indices
    const orderMap = new Map(orderArray.map((id, index) => [id, index]));

    return [...items].sort((a, b) => {
      const aIndex = orderMap.get(a.id) ?? Number.MAX_SAFE_INTEGER;
      const bIndex = orderMap.get(b.id) ?? Number.MAX_SAFE_INTEGER;
      return aIndex - bIndex;
    });
  };

  return {
    id: primaryPlaylist.id,
    name: primaryPlaylist.name,
    description: primaryPlaylist.description || undefined,
    imageUrl: primaryPlaylist.imageUrl || undefined,
    isPublic: primaryPlaylist.isPublic,
    isDefault: primaryPlaylist.isDefault,
    creatorType: primaryPlaylist.creatorType,
    userId: primaryPlaylist.userId,
    musicOrder: musicPlaylist?.musicOrder || [],

    // Map and sort music tracks if available
    musics: musicPlaylist?.musics ? sortByOrder(
      musicPlaylist.musics.map(music => ({
        id: music.id,
        title: music.title,
        src: music.src || null,
        isPublic: music.isPublic,
        creatorType: music.creatorType,
        userId: music.userId,
        genres: music.genres || [],
        createdAt: music.createdAt,
        updatedAt: music.updatedAt
      })),
      musicPlaylist.musicOrder
    ) : [],

    // Map and sort nature sounds if available
    natureSounds: naturePlaylist?.natureSounds ? sortByOrder(
      naturePlaylist.natureSounds.map(sound => ({
        id: sound.id,
        title: sound.title,
        src: sound.src || null,
        category: sound.category || undefined,
        isPublic: sound.isPublic,
        creatorType: sound.creatorType,
        userId: sound.userId,
        createdAt: sound.createdAt,
        updatedAt: sound.updatedAt
      })),
      naturePlaylist.natureSoundOrder
    ) : []
  };
}