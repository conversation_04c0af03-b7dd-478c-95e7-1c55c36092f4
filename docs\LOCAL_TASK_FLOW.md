# Local Task Management Flow

This document explains the comprehensive flow for unauthenticated users to create and manage tasks locally, with seamless transition to database storage upon authentication.

## Architecture Overview

The local task management system mirrors the pomodoro session approach, providing:

1. **Local Storage** - Tasks stored in browser localStorage for unauthenticated users
2. **Hybrid Management** - Single hook that manages both local and remote tasks
3. **Bulk Transfer** - Automatic migration of local tasks to database on authentication
4. **Unified Interface** - Components work transparently with both local and remote data

## Key Components

### 1. Local Task Store (`src/lib/local-task-store.ts`)

```typescript
interface LocalTask {
  id: string;           // Generated with 'local_task_' prefix
  title: string;        // Task title
  completed: boolean;   // Completion status
  createdAt: string;    // ISO string timestamp
  updatedAt: string;    // ISO string timestamp
}
```

**Features:**
- Zustand store with persistence
- CRUD operations for local tasks
- Task filtering and search
- Statistics calculation
- Bulk export for transfer

### 2. Hybrid Task Management Hook (`src/hooks/useTaskManagement.ts`)

```typescript
interface UnifiedTask {
  id: string;
  title: string;
  completed: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
  isLocal: boolean;              // Distinguishes local vs remote
  userId?: string;               // Only for remote tasks
  pomodoroSessions?: any[];      // Only for remote tasks
}
```

**Responsibilities:**
- Manages both local and remote tasks through single interface
- Handles authentication state changes
- Automatically transfers local tasks on sign-in
- Provides unified CRUD operations

### 3. Bulk Transfer System

**Backend Route:** `POST /api/tasks/bulk-transfer`
```typescript
interface BulkTransferTask {
  title: string;
  completed: string;    // "true" or "false"
  createdAt: string;    // ISO string
  updatedAt: string;    // ISO string
}
```

**Client Hook:** `useBulkTransferTasks()`
- Converts local tasks to API format
- Handles transfer errors gracefully
- Shows success/failure notifications
- Clears local storage on success

## User Flow

### Phase 1: Unauthenticated User

1. **Creating Tasks**
   ```typescript
   // User creates task through TaskInlineForm
   const { createTask } = useTaskManagement();
   await createTask({ title: "My first task", completed: false });
   
   // Automatically saved to localStorage
   ```

2. **Managing Tasks**
   - View tasks in TaskList component
   - Edit task titles
   - Toggle completion status
   - Delete tasks
   - All operations work on localStorage

3. **Pomodoro Integration**
   ```typescript
   // Tasks can be linked to pomodoro sessions
   const { startFocusSession } = usePomodoroStore();
   startFocusSession({ id: "local_task_123", title: "My task" });
   
   // Local pomodoro sessions reference local task IDs
   ```

### Phase 2: Authentication

1. **User Signs In**
   ```typescript
   // useTaskManagement detects authentication change
   useEffect(() => {
     if (isAuthenticated && !hasTransferredTasks) {
       transferLocalTasks();
     }
   }, [isAuthenticated]);
   ```

2. **Automatic Transfer**
   ```typescript
   const transferLocalTasks = async () => {
     const localTasks = exportTasks();
     const tasksToTransfer = localTasks.map(task => ({
       title: task.title,
       completed: task.completed.toString(),
       createdAt: task.createdAt,
       updatedAt: task.updatedAt,
     }));
     
     await bulkTransferTasks.mutateAsync({ tasks: tasksToTransfer });
     clearLocalTasks(); // Clear localStorage after success
   };
   ```

3. **Seamless Transition**
   - UI automatically switches to remote data
   - User sees their tasks preserved
   - No data loss or duplication

### Phase 3: Authenticated User

1. **Remote Task Management**
   - All operations now use database
   - Real-time synchronization
   - Pomodoro sessions linked to database tasks

2. **Cross-Device Sync**
   - Tasks available on all devices
   - Consistent state across sessions

## Component Integration

### TaskSheet Component

```typescript
export function TaskSheet({ isOpen, onClose, onTaskFocusStart }: TaskSheetProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'completed'>('all');
  const { isAuthenticated } = useUserStore();
  
  // Single hook manages everything
  const { taskStats } = useTaskManagement({
    filters: {
      completed: filterStatus === 'all' ? undefined : filterStatus === 'completed',
      searchTerm
    }
  });

  return (
    // Shows authentication prompt for unauthenticated users
    // Shows task list for both local and remote scenarios
    <TaskList
      searchTerm={searchTerm}
      showNewTaskForm={showNewTaskForm}
      onCreateTaskSuccess={handleCreateTaskSuccess}
      onCancelNewTask={() => setShowNewTaskForm(false)}
      onShowNewTaskForm={() => setShowNewTaskForm(true)}
      onTaskFocusStart={onTaskFocusStart}
      filterStatus={filterStatus}
    />
  );
}
```

### TaskList Component

```typescript
export function TaskList({ searchTerm, filterStatus, ... }: TaskListProps) {
  const { 
    tasks,          // Unified tasks (local or remote)
    isLoading, 
    error, 
    updateTask,     // Works for both local and remote
    deleteTask,     // Works for both local and remote
  } = useTaskManagement({
    filters: {
      completed: filterStatus === 'all' ? undefined : filterStatus === 'completed',
      searchTerm
    }
  });

  // Component works identically for both scenarios
  const handleToggleTask = async (task: UnifiedTask) => {
    await updateTask(task.id, { completed: !task.completed });
  };

  return (
    // Renders tasks transparently
    <div>
      {tasks.map(task => (
        <TaskItem
          key={task.id}
          task={task}
          onToggle={() => handleToggleTask(task)}
          onDelete={() => deleteTask(task.id)}
          // ... other props
        />
      ))}
    </div>
  );
}
```

## Data Consistency

### ID Management
- **Local Tasks:** `local_task_${timestamp}_${random}`
- **Remote Tasks:** Database-generated cuid
- **No Conflicts:** Different prefixes prevent ID collisions

### Timestamp Handling
- **Local:** ISO strings stored in localStorage
- **Remote:** Database DateTime converted to ISO strings
- **Unified:** Both treated as Date objects in components

### State Synchronization
- **Authentication Change:** Immediate switch between local/remote
- **Transfer Process:** Atomic operation with error handling
- **UI Updates:** React Query handles cache invalidation

## Error Handling

### Transfer Failures
```typescript
try {
  await bulkTransferTasks.mutateAsync({ tasks: tasksToTransfer });
  clearLocalTasks();
  setHasTransferredTasks(true);
} catch (error) {
  console.error('Failed to transfer local tasks:', error);
  // Keep local tasks, retry on next authentication
}
```

### Network Issues
- Local tasks continue working offline
- Remote operations show appropriate loading states
- Graceful degradation to local storage

### Data Validation
- Backend validates all transferred tasks
- Skips invalid entries (empty titles, etc.)
- Reports transfer statistics to user

## Benefits

1. **Immediate Usability** - Users can start using the app without signing up
2. **No Data Loss** - Seamless preservation of work during authentication
3. **Offline Capability** - Local tasks work without internet connection
4. **Consistent UX** - Same interface for both authenticated and unauthenticated users
5. **Scalable Architecture** - Clean separation between local and remote concerns

## Testing Scenarios

1. **Create Local Tasks → Sign In → Verify Transfer**
2. **Create Remote Tasks → Sign Out → Verify Local Functionality**
3. **Mixed Operations** - Create some local, sign in, create more remote
4. **Transfer Failures** - Network errors, validation failures
5. **Multiple Devices** - Sign in from different devices after creating local tasks

This architecture provides a robust foundation for task management that works seamlessly across authentication states while maintaining data integrity and user experience consistency. 