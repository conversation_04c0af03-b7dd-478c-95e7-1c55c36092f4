import { z } from 'zod';
import { UserWithRelationsSchema, UserPartialWithRelationsSchema } from './UserSchema'
import type { UserWithRelations, UserPartialWithRelations } from './UserSchema'
import { VideoWithRelationsSchema, VideoPartialWithRelationsSchema } from './VideoSchema'
import type { VideoWithRelations, VideoPartialWithRelations } from './VideoSchema'

/////////////////////////////////////////
// FAVORITE VIDEO SCHEMA
/////////////////////////////////////////

export const FavoriteVideoSchema = z.object({
  id: z.string().cuid(),
  userId: z.string(),
  videoId: z.string(),
  createdAt: z.coerce.date(),
})

export type FavoriteVideo = z.infer<typeof FavoriteVideoSchema>

/////////////////////////////////////////
// FAVORITE VIDEO PARTIAL SCHEMA
/////////////////////////////////////////

export const FavoriteVideoPartialSchema = FavoriteVideoSchema.partial()

export type FavoriteVideoPartial = z.infer<typeof FavoriteVideoPartialSchema>

/////////////////////////////////////////
// FAVORITE VIDEO RELATION SCHEMA
/////////////////////////////////////////

export type FavoriteVideoRelations = {
  user: UserWithRelations;
  video: VideoWithRelations;
};

export type FavoriteVideoWithRelations = z.infer<typeof FavoriteVideoSchema> & FavoriteVideoRelations

export const FavoriteVideoWithRelationsSchema: z.ZodType<FavoriteVideoWithRelations> = FavoriteVideoSchema.merge(z.object({
  user: z.lazy(() => UserWithRelationsSchema),
  video: z.lazy(() => VideoWithRelationsSchema),
}))

/////////////////////////////////////////
// FAVORITE VIDEO PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type FavoriteVideoPartialRelations = {
  user?: UserPartialWithRelations;
  video?: VideoPartialWithRelations;
};

export type FavoriteVideoPartialWithRelations = z.infer<typeof FavoriteVideoPartialSchema> & FavoriteVideoPartialRelations

export const FavoriteVideoPartialWithRelationsSchema: z.ZodType<FavoriteVideoPartialWithRelations> = FavoriteVideoPartialSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  video: z.lazy(() => VideoPartialWithRelationsSchema),
})).partial()

export type FavoriteVideoWithPartialRelations = z.infer<typeof FavoriteVideoSchema> & FavoriteVideoPartialRelations

export const FavoriteVideoWithPartialRelationsSchema: z.ZodType<FavoriteVideoWithPartialRelations> = FavoriteVideoSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  video: z.lazy(() => VideoPartialWithRelationsSchema),
}).partial())

export default FavoriteVideoSchema;
