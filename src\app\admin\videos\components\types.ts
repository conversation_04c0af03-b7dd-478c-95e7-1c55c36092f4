export interface Video {
  id: string;
  title: string;
  src: string;
  thumbnail: string;
  description?: string | null;
  isPublic: boolean;
  creatorType: string;
  videoGenre?: string[];
  userId?: string | null;
  playlistId?: string | null;
  isFavorite?: boolean;
  isPremium?: boolean;
  favoriteBy?: { id: string }[];
  playlist?: {
    id: string;
    name: string;
    description?: string | null;
  } | null;
  createdAt: string | Date;
  updatedAt: string | Date;
} 