"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"

interface StatsState {
  todayCompleted: number
  currentStreak: number
  focusTimeToday: number
  lastActiveDate: string
  incrementCompleted: () => void
  addFocusTime: (seconds: number) => void
  resetDailyStats: () => void
}

export const useStatsStore = create<StatsState>()(
  persist(
    (set, get) => ({
      todayCompleted: 0,
      currentStreak: 0,
      focusTimeToday: 0,
      lastActiveDate: new Date().toDateString(),

      incrementCompleted: () => {
        const { todayCompleted, lastActiveDate, currentStreak } = get()
        const today = new Date().toDateString()

        // Check if it's a new day
        if (today !== lastActiveDate) {
          // If yesterday was the last active day, increment streak
          const yesterday = new Date()
          yesterday.setDate(yesterday.getDate() - 1)
          const yesterdayString = yesterday.toDateString()

          if (lastActiveDate === yesterdayString) {
            set({
              todayCompleted: 1,
              currentStreak: currentStreak + 1,
              focusTimeToday: 0,
              lastActiveDate: today,
            })
          } else {
            // Streak broken
            set({
              todayCompleted: 1,
              currentStreak: 1,
              focusTimeToday: 0,
              lastActiveDate: today,
            })
          }
        } else {
          // Same day, just increment
          set({
            todayCompleted: todayCompleted + 1,
            currentStreak: Math.max(1, currentStreak),
            lastActiveDate: today,
          })
        }
      },

      addFocusTime: (seconds) => {
        const { focusTimeToday } = get()
        set({ focusTimeToday: focusTimeToday + seconds })
      },

      resetDailyStats: () => {
        set({
          todayCompleted: 0,
          focusTimeToday: 0,
          lastActiveDate: new Date().toDateString(),
        })
      },
    }),
    {
      name: "pomodoro-stats",
      onRehydrateStorage: () => {
        return (state) => {
          if (state) {
            const today = new Date().toDateString()
            if (today !== state.lastActiveDate) {
              // Reset daily stats if it's a new day
              state.resetDailyStats()
            }
          }
        }
      },
    },
  ),
)
