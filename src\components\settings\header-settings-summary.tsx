'use client';

import { Clock, Settings, Check, ChevronRight, Timer, Target, Coffee, Moon, BarChart3 } from 'lucide-react';
import { TimerSettings as TimerSettingsType } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { usePomodoroStore } from '@/lib/pomodoro-store';
import { useEffect, useState, useMemo, useCallback } from 'react';
import { cn } from '@/lib/utils';
// 
import { StartTimerButton } from './start-timer-button';
import { TimerSettings } from './timer-setting/timer-settings-dialog';
import { QuickStatsDialog } from '@/app/timer/_components/quick-stats-dialog';
import { motion, AnimatePresence, useReducedMotion } from 'framer-motion';
import { useGetPomodoroQuickStats } from '../../../prisma/schema/Pomodoro/pomodoro-query';
import { useUserStore } from '@/store/userStore';

// Animation variants for enhanced UX
const containerVariants = {
  hidden: { opacity: 1, y: 0 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.4, 0.0, 0.2, 1],
      staggerChildren: 0.05
    }
  }
};

const cardVariants = {
  hidden: { opacity: 1, y: 0, scale: 1 },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.2,
      ease: [0.4, 0.0, 0.2, 1]
    }
  }
};

const hoverVariants = {
  hover: {
    scale: 1.02,
    transition: {
      duration: 0.2,
      ease: [0.4, 0.0, 0.2, 1]
    }
  },
  tap: {
    scale: 0.98,
    transition: {
      duration: 0.1,
      ease: [0.4, 0.0, 0.2, 1]
    }
  }
};

const iconVariants = {
  hover: {
    rotate: 5,
    scale: 1.1,
    transition: {
      duration: 0.2,
      ease: [0.4, 0.0, 0.2, 1]
    }
  }
};

const chevronVariants = {
  hover: {
    x: 4,
    transition: {
      duration: 0.2,
      ease: [0.4, 0.0, 0.2, 1]
    }
  }
};

interface HeaderSettingsSummaryProps extends TimerSettingsType {
  className?: string;
}

export function HeaderSettingsSummary({
  pomodoroMinutes,
  shortBreakMinutes,
  longBreakMinutes,
  sessionsBeforeLongBreak,
  className
}: HeaderSettingsSummaryProps) {
  const { updateSettings, timerSettings } = usePomodoroStore();
  const [initialRender, setInitialRender] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isStatsDialogOpen, setIsStatsDialogOpen] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isClient, setIsClient] = useState(false);
  const shouldReduceMotion = useReducedMotion();

  const { isAuthenticated } = useUserStore();

  // Calculate date ranges in user's timezone for API calls
  const calculateDateRanges = () => {
    const now = new Date();

    // Today's range
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const todayEnd = new Date(todayStart);
    todayEnd.setDate(todayEnd.getDate() + 1);

    // Week range (Monday to Sunday)
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1; // If Sunday, go back 6 days to Monday
    const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    weekStart.setDate(weekStart.getDate() - daysFromMonday);

    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekEnd.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);

    // Month range (1st to last day of current month)
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

    return {
      todayStart: todayStart.toISOString(),
      todayEnd: todayEnd.toISOString(),
      weekStart: weekStart.toISOString(),
      weekEnd: weekEnd.toISOString(),
      monthStart: monthStart.toISOString(),
      monthEnd: monthEnd.toISOString(),
    };
  };

  const dateRanges = calculateDateRanges();

  // Prefetch stats data for authenticated users
  const { data: quickStatsData, isLoading: statsLoading, error: statsError } = useGetPomodoroQuickStats(isAuthenticated, dateRanges);

  // Get timer mode from store - use client-side only to avoid hydration issues
  const timerMode = isClient ? (timerSettings.timerMode || 'countDown') : 'countDown';

  // Memoized calculations for performance - only for countdown mode
  const totalMinutes = useMemo(() => {
    if (timerMode === 'countUp') {
      // For count-up mode, we don't have a predetermined total time
      return 0;
    }
    return (
      pomodoroMinutes * sessionsBeforeLongBreak +
      shortBreakMinutes * (sessionsBeforeLongBreak - 1) +
      longBreakMinutes
    );
  }, [pomodoroMinutes, shortBreakMinutes, longBreakMinutes, sessionsBeforeLongBreak, timerMode]);

  const { hours, minutes } = useMemo(() => ({
    hours: Math.floor(totalMinutes / 60),
    minutes: totalMinutes % 60
  }), [totalMinutes]);

  // Calculate end time - only for countdown mode
  const endTime = useMemo(() => {
    if (timerMode === 'countUp') {
      // For count-up mode, we can't predict end time
      return null;
    }
    const time = new Date(currentTime);
    time.setMinutes(time.getMinutes() + totalMinutes);
    return time;
  }, [currentTime, totalMinutes, timerMode]);

  // Format the end time as HH:MM
  const formatEndTime = useCallback((date: Date): string => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }, []);

  // Prevent hydration issues and update time every minute
  useEffect(() => {
    setInitialRender(false);
    setIsClient(true); // Set client flag to enable timer mode rendering

    // Update current time every minute
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  // Optimized handler functions with useCallback
  const handleFocusTimeChange = useCallback(() => {
    // Options: 25, 30, 45, 60, 90, 120, 180, 240 minutes
    const focusTimeOptions = [25, 30, 45, 60, 90, 120, 180, 240];

    if (timerMode === 'countUp') {
      // If currently in count-up mode, switch back to countdown mode starting at 25 minutes
      updateSettings({
        pomodoroMinutes: 25,
        timerMode: 'countDown'
      });
    } else {
      // In countdown mode, cycle through options
      const currentIndex = focusTimeOptions.indexOf(pomodoroMinutes);
      const nextIndex = (currentIndex + 1) % focusTimeOptions.length;
      const nextValue = focusTimeOptions[nextIndex];

      if (pomodoroMinutes === 240) {
        // At 240 minutes (4 hours), switch to count-up mode
        updateSettings({
          timerMode: 'countUp'
        });
      } else {
        // Normal cycling through time options
        updateSettings({ pomodoroMinutes: nextValue });
      }
    }
  }, [pomodoroMinutes, timerMode, updateSettings]);

  const handleShortBreakChange = useCallback(() => {
    // Options: 0, 5, 10, 15, 20, 30 minutes
    const shortBreakOptions = [0, 5, 10, 15, 20, 30];
    const currentIndex = shortBreakOptions.indexOf(shortBreakMinutes);
    const nextIndex = (currentIndex + 1) % shortBreakOptions.length;
    updateSettings({ shortBreakMinutes: shortBreakOptions[nextIndex] });
  }, [shortBreakMinutes, updateSettings]);

  const handleLongBreakChange = useCallback(() => {
    // Options: 0, 15, 20, 30, 45, 60 minutes
    const longBreakOptions = [0, 15, 20, 30, 45, 60];
    const currentIndex = longBreakOptions.indexOf(longBreakMinutes);
    const nextIndex = (currentIndex + 1) % longBreakOptions.length;
    updateSettings({ longBreakMinutes: longBreakOptions[nextIndex] });
  }, [longBreakMinutes, updateSettings]);

  const handleSessionCountChange = useCallback(() => {
    // Options: 1, 2, 3, 4, 5, 6, 8 sessions
    const sessionOptions = [1, 2, 3, 4, 5, 6, 8];
    const currentIndex = sessionOptions.indexOf(sessionsBeforeLongBreak);
    const nextIndex = (currentIndex + 1) % sessionOptions.length;
    updateSettings({ sessionsBeforeLongBreak: sessionOptions[nextIndex] });
  }, [sessionsBeforeLongBreak, updateSettings]);

  // Optimized format time display
  const formatTime = useCallback((minutes: number): string => {
    if (minutes === 0) return "0m";
    if (minutes < 60) return `${minutes}m`;
    const hrs = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hrs}h ${mins}m` : `${hrs}h`;
  }, []);

    // Generate visual representation of timer cycle
  const generateTimerCycle = () => {
    const cycle = [];

    // Limit the display to a maximum of 4 sessions to prevent UI expansion
    const displaySessions = Math.min(sessionsBeforeLongBreak, 4);

    for (let i = 0; i < displaySessions; i++) {
      // Add focus session with click handler
      cycle.push(
        <motion.button
          key={`focus-${i}`}
          className="flex items-center justify-center gap-1 px-2 sm:px-1.5 py-1.5 sm:py-1 rounded-lg bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 text-blue-600 dark:text-blue-300 flex-shrink-0 border border-transparent hover:border-blue-200 dark:hover:border-blue-800/30 transition-colors whitespace-nowrap cursor-pointer touch-manipulation min-w-[3rem] sm:min-w-[2.5rem]"
          onClick={handleFocusTimeChange}
          aria-label={`Change focus time`}
          layout
          initial={{ opacity: 1, scale: 1 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.2, ease: [0.4, 0.0, 0.2, 1] }}
        >
          <Clock className="h-3 w-3 sm:h-2.5 sm:w-2.5 flex-shrink-0" suppressHydrationWarning/>
          <span className="text-xs sm:text-[10px] font-medium text-center truncate" suppressHydrationWarning>
            {timerMode === 'countUp'
              ? (initialRender ? "∞" : "∞")
              : (initialRender ? "25m" : formatTime(pomodoroMinutes))
            }
          </span>
        </motion.button>
      );

      // Add arrow after focus session if there's a next element
      if (i < displaySessions - 1 && shortBreakMinutes > 0) {
        // Arrow between focus and short break
        cycle.push(
          <motion.div
            key={`arrow-focus-${i}`}
            className="flex items-center justify-center text-muted-foreground/60 flex-shrink-0"
            layout
            initial={{ opacity: 1, scale: 1 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2, ease: [0.4, 0.0, 0.2, 1] }}
          >
            <ChevronRight className="h-3 w-3 sm:h-2.5 sm:w-2.5" />
          </motion.div>
        );
      }

      // Add short break if not the last session and short break > 0
      if (i < displaySessions - 1 && shortBreakMinutes > 0) {
        cycle.push(
          <motion.button
            key={`short-${i}`}
            className="flex items-center justify-center gap-1 px-2 sm:px-1.5 py-1.5 sm:py-1 rounded-lg bg-emerald-50 hover:bg-emerald-100 dark:bg-emerald-900/20 dark:hover:bg-emerald-900/30 text-emerald-600 dark:text-emerald-300 flex-shrink-0 border border-transparent hover:border-emerald-200 dark:hover:border-emerald-800/30 transition-colors whitespace-nowrap cursor-pointer touch-manipulation min-w-[3rem] sm:min-w-[2.5rem]"
            onClick={handleShortBreakChange}
            aria-label={`Change short break time`}
            layout
            initial={{ opacity: 1, scale: 1 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2, ease: [0.4, 0.0, 0.2, 1] }}
          >
            <Coffee className="h-3 w-3 sm:h-2.5 sm:w-2.5 flex-shrink-0"  suppressHydrationWarning/>
            <span className="text-xs sm:text-[10px] font-medium text-center truncate" suppressHydrationWarning>
              {initialRender ? "5m" : formatTime(shortBreakMinutes)}
            </span>
          </motion.button>
        );

        // Arrow after short break if there's a next focus session
        if (i < displaySessions - 1) {
          cycle.push(
            <motion.div
              key={`arrow-short-${i}`}
              className="flex items-center justify-center text-muted-foreground/60 flex-shrink-0"
              layout
              initial={{ opacity: 1, scale: 1 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2, ease: [0.4, 0.0, 0.2, 1] }}
            >
              <ChevronRight className="h-3 w-3 sm:h-2.5 sm:w-2.5" />
            </motion.div>
          );
        }
      }

      // Add long break after the last focus session, but only if sessions > 1 and long break > 0
      if (i === displaySessions - 1 && sessionsBeforeLongBreak > 1 && longBreakMinutes > 0) {
        // Arrow before long break
        cycle.push(
          <motion.div
            key="arrow-long"
            className="flex items-center justify-center text-muted-foreground/60 flex-shrink-0"
            layout
            initial={{ opacity: 1, scale: 1 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2, ease: [0.4, 0.0, 0.2, 1] }}
          >
            <ChevronRight className="h-3 w-3 sm:h-2.5 sm:w-2.5" />
          </motion.div>
        );

        cycle.push(
          <motion.button
            key="long-break"
            className="flex items-center justify-center gap-1 px-2 sm:px-1.5 py-1.5 sm:py-1 rounded-lg bg-purple-50 hover:bg-purple-100 dark:bg-purple-900/20 dark:hover:bg-purple-900/30 text-purple-600 dark:text-purple-300 flex-shrink-0 border border-transparent hover:border-purple-200 dark:hover:border-purple-800/30 transition-colors whitespace-nowrap cursor-pointer touch-manipulation min-w-[3rem] sm:min-w-[2.5rem]"
            onClick={handleLongBreakChange}
            aria-label={`Change long break time`}
            layout
            initial={{ opacity: 1, scale: 1 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2, ease: [0.4, 0.0, 0.2, 1] }}
          >
            <Moon className="h-3 w-3 sm:h-2.5 sm:w-2.5 flex-shrink-0" />
            <span className="text-xs sm:text-[10px] font-medium text-center truncate" suppressHydrationWarning>
              {initialRender ? "15m" : formatTime(longBreakMinutes)}
            </span>
          </motion.button>
        );
      }
    }

    // If there are more than 4 sessions, add ellipsis indicator
    if (sessionsBeforeLongBreak > 4) {
      cycle.push(
        <motion.div
          key="more-sessions"
          suppressHydrationWarning
          className="flex items-center justify-center px-2 sm:px-1.5 py-1.5 sm:py-1 rounded-lg bg-gray-50 dark:bg-gray-800/30 text-gray-500 dark:text-gray-400 flex-shrink-0 whitespace-nowrap min-w-[3rem] sm:min-w-[2.5rem]"
          layout
          initial={{ opacity: 1, scale: 1 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.2, ease: [0.4, 0.0, 0.2, 1] }}
        >
          <span className="text-xs sm:text-[10px]" suppressHydrationWarning>
            {initialRender ? "+1" : `+${sessionsBeforeLongBreak - 4}`}
          </span>
        </motion.div>
      );
    }

    return cycle;
  };

  return (
    <motion.div
      className={cn(
        "rounded-xl border border-border/50 bg-card/95 backdrop-blur-sm overflow-hidden",
        "flex flex-col shadow-lg shadow-black/5",
        "ring-1 ring-black/5 dark:ring-white/5",
        "w-full max-w-full", // Ensure full width on mobile
        className
      )}
      variants={shouldReduceMotion ? {} : containerVariants}
      initial={false}
      animate="visible"
      layout
    >
      {/* Enhanced Header with title and controls - Mobile Optimized */}
      <div className="bg-gradient-to-r from-muted/40 to-muted/20 py-3 sm:py-3 px-3 sm:px-4 border-b border-border/30">
        <div className="flex justify-between items-center gap-2">
          <div className="flex-1 min-w-0">
            <h1 className="text-lg sm:text-lg font-semibold text-foreground flex items-center gap-2" suppressHydrationWarning>
              <div>
                <Timer className="h-4 w-4 text-primary flex-shrink-0" />
              </div>
              <span className="truncate">Timer Settings</span>
              {timerMode === 'countUp' && (
                <div className="flex items-center">
                  <Badge
                    variant="outline"
                    className="h-4 px-1.5 text-[9px] font-medium border transition-colors bg-emerald-50/80 text-emerald-600 border-emerald-200/60 dark:bg-emerald-950/15 dark:text-emerald-400 dark:border-emerald-800/40"
                  >
                    Count Up
                  </Badge>
                </div>
              )}
            </h1>
            <p className="text-xs text-muted-foreground mt-0.5 hidden sm:block" suppressHydrationWarning>
              {timerMode === 'countUp'
                ? 'Configure your focus sessions and break intervals'
                : 'Configure your focus and break intervals'
              }
            </p>
          </div>

          <div className="flex items-center gap-1.5 sm:gap-2 flex-shrink-0">
            {/* Stats with label inside border */}
            <Button
              variant="outline"
              size="default"
              className="h-9 sm:h-8 px-3 sm:px-2 hover:bg-primary/10 hover:border-primary/20 transition-colors cursor-pointer touch-manipulation flex items-center gap-1.5 sm:gap-1"
              onClick={() => setIsStatsDialogOpen(true)}
              aria-label="View focus statistics"
            >
              <BarChart3 className="h-4 w-4 flex-shrink-0" />
              <span className="text-xs sm:text-[10px] font-medium hidden sm:inline">
                Stats
              </span>
            </Button>

            {/* <div className="cursor-pointer">
              <AudioSettings />
            </div> */}

            {/* Settings with label inside border */}
            <Button
              variant="outline"
              size="default"
              className="h-9 sm:h-8 px-3 sm:px-2 hover:bg-primary/10 hover:border-primary/20 transition-colors cursor-pointer touch-manipulation flex items-center gap-1.5 sm:gap-1"
              onClick={() => setIsDialogOpen(true)}
              aria-label="Open detailed settings"
            >
              <Settings className="h-4 w-4 flex-shrink-0" />
              <span className="text-xs sm:text-[10px] font-medium hidden sm:inline">
                Settings
              </span>
            </Button>

            {/* Settings Dialog */}
            <AnimatePresence>
              {isDialogOpen && (
                <TimerSettings
                  isOpen={isDialogOpen}
                  onOpenChange={setIsDialogOpen}
                />
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Enhanced Main content area - Mobile Optimized */}
      <motion.div
        className="px-3 sm:px-4 py-4"
        variants={shouldReduceMotion ? {} : cardVariants}
      >
        <motion.div
          className="space-y-4"
          variants={shouldReduceMotion ? {} : containerVariants}
        >
          {/* Enhanced Focus Time Card - Mobile Optimized */}
          <motion.button
            className={`group relative p-4 rounded-xl text-left w-full overflow-hidden focus:outline-none cursor-pointer backdrop-blur-sm touch-manipulation transition-all duration-300 ${
              timerMode === 'countUp'
                ? 'bg-gradient-to-br from-emerald-50 via-emerald-50/80 to-emerald-100/60 dark:from-emerald-950/30 dark:via-emerald-950/20 dark:to-emerald-900/10 border border-emerald-200/60 dark:border-emerald-800/30 focus-visible:ring-2 focus-visible:ring-emerald-400 dark:focus-visible:ring-emerald-500'
                : 'bg-gradient-to-br from-blue-50 via-blue-50/80 to-blue-100/60 dark:from-blue-950/30 dark:via-blue-950/20 dark:to-blue-900/10 border border-blue-200/60 dark:border-blue-800/30 focus-visible:ring-2 focus-visible:ring-blue-400 dark:focus-visible:ring-blue-500'
            }`}
            onClick={handleFocusTimeChange}
            aria-label={timerMode === 'countUp' ? 'Switch to countdown mode' : 'Change focus time'}
            variants={shouldReduceMotion ? {} : cardVariants}
            whileHover={shouldReduceMotion ? {} : hoverVariants.hover}
            whileTap={shouldReduceMotion ? {} : hoverVariants.tap}
            suppressHydrationWarning
          >
            <motion.div
              className={`absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${
                timerMode === 'countUp'
                  ? 'bg-gradient-to-br from-emerald-500/5 to-emerald-600/10'
                  : 'bg-gradient-to-br from-blue-500/5 to-blue-600/10'
              }`}
              layoutId="focus-bg"
              suppressHydrationWarning
            />
            <div className="flex items-center justify-between relative z-10">
              <div className="flex items-center gap-3">
                <motion.div
                  className={`p-2 rounded-xl ring-1 transition-colors duration-300 ${
                    timerMode === 'countUp'
                      ? 'bg-emerald-500/15 ring-emerald-500/20'
                      : 'bg-blue-500/15 ring-blue-500/20'
                  }`}
                  variants={shouldReduceMotion ? {} : iconVariants}
                  whileHover="hover"
                  suppressHydrationWarning
                >
                  <Target className={`h-5 w-5 transition-colors duration-300 ${
                    timerMode === 'countUp'
                      ? 'text-emerald-600 dark:text-emerald-400'
                      : 'text-blue-600 dark:text-blue-400'
                  }`} suppressHydrationWarning />
                </motion.div>
                <div>
                  <h3 className={`text-sm font-semibold transition-colors duration-300 ${
                    timerMode === 'countUp'
                      ? 'text-emerald-700 dark:text-emerald-300'
                      : 'text-blue-700 dark:text-blue-300'
                  }`} suppressHydrationWarning>
                    {timerMode === 'countUp' ? 'Focus Session' : 'Focus Time'}
                  </h3>
                  <p className={`text-xs hidden sm:block transition-colors duration-300 ${
                    timerMode === 'countUp'
                      ? 'text-emerald-600/70 dark:text-emerald-400/70'
                      : 'text-blue-600/70 dark:text-blue-400/70'
                  }`} suppressHydrationWarning>
                    {timerMode === 'countUp' ? 'Count Up Mode - Manual stop when ready' : 'Deep work session'}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <motion.span
                  className={`text-2xl sm:text-2xl font-bold min-w-[4rem] sm:min-w-[5rem] text-right transition-colors duration-300 ${
                    timerMode === 'countUp'
                      ? 'text-emerald-700 dark:text-emerald-300'
                      : 'text-blue-700 dark:text-blue-300'
                  }`}
                  key={timerMode === 'countUp' ? 'infinity' : pomodoroMinutes}
                  initial={{ scale: 1, opacity: 1 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.2 }}
                  suppressHydrationWarning
                >
                  {timerMode === 'countUp'
                    ? (initialRender ? "∞" : "∞")
                    : (initialRender ? "25m" : formatTime(pomodoroMinutes))
                  }
                </motion.span>
                <motion.div
                  variants={shouldReduceMotion ? {} : chevronVariants}
                  whileHover="hover"
                >
                  <ChevronRight className={`h-5 w-5 opacity-60 group-hover:opacity-100 transition-all duration-300 ${
                    timerMode === 'countUp'
                      ? 'text-emerald-500'
                      : 'text-blue-500'
                  }`} suppressHydrationWarning />
                </motion.div>
              </div>
            </div>
            <motion.div
              className={`absolute bottom-0 left-0 right-0 h-1 rounded-b-xl transition-all duration-300 ${
                timerMode === 'countUp'
                  ? 'bg-gradient-to-r from-emerald-400/40 to-emerald-500/40 dark:from-emerald-500/30 dark:to-emerald-400/30'
                  : 'bg-gradient-to-r from-blue-400/40 to-blue-500/40 dark:from-blue-500/30 dark:to-blue-400/30'
              }`}
              layoutId="focus-indicator"
              suppressHydrationWarning
            />
          </motion.button>

          {/* Break Times and Sessions - Mobile Responsive Grid */}
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-2"
            variants={shouldReduceMotion ? {} : containerVariants}
          >
            {/* Short Break - Mobile Optimized */}
            <motion.button
              className="group relative bg-gradient-to-br from-emerald-50/80 to-emerald-100/40 dark:from-emerald-950/15 dark:to-emerald-900/10 p-3 sm:p-2.5 rounded-lg border border-emerald-200/50 dark:border-emerald-800/20 text-left overflow-hidden focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-400 dark:focus-visible:ring-emerald-500 cursor-pointer backdrop-blur-sm touch-manipulation"
              onClick={handleShortBreakChange}
              aria-label={`Change short break time`}
              variants={shouldReduceMotion ? {} : cardVariants}
              whileHover={shouldReduceMotion ? {} : hoverVariants.hover}
              whileTap={shouldReduceMotion ? {} : hoverVariants.tap}
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-emerald-600/8 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                layoutId="short-break-bg"
              />
              <div className="flex items-center justify-between relative z-10">
                <div className="flex items-center gap-2 sm:gap-1.5">
                  <motion.div
                    className="bg-emerald-500/15 p-1.5 sm:p-1 rounded-md ring-1 ring-emerald-500/20"
                    variants={shouldReduceMotion ? {} : iconVariants}
                    whileHover="hover"
                  >
                    <Coffee className="h-4 w-4 sm:h-3 sm:w-3 text-emerald-600 dark:text-emerald-400" />
                  </motion.div>
                  <div className="text-sm sm:text-xs font-medium text-emerald-700 dark:text-emerald-300">Short Break</div>
                </div>
                <div className="flex items-center gap-1.5 sm:gap-1">
                  <motion.span
                    className="text-lg sm:text-sm font-bold text-emerald-700 dark:text-emerald-300"
                    key={shortBreakMinutes}
                    initial={{ scale: 1, opacity: 1 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    {initialRender ? "5m" : formatTime(shortBreakMinutes)}
                  </motion.span>
                  <motion.div
                    variants={shouldReduceMotion ? {} : chevronVariants}
                    whileHover="hover"
                  >
                    <ChevronRight className="h-4 w-4 sm:h-3 sm:w-3 text-emerald-500 opacity-60 group-hover:opacity-100 transition-opacity" />
                  </motion.div>
                </div>
              </div>
              <motion.div
                className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-emerald-400/30 to-emerald-500/30 dark:from-emerald-500/20 dark:to-emerald-400/20 rounded-b-lg"
                layoutId="short-break-indicator"
              />
            </motion.button>

            {/* Long Break - Mobile Optimized */}
            <motion.button
              className="group relative bg-gradient-to-br from-purple-50/80 to-purple-100/40 dark:from-purple-950/15 dark:to-purple-900/10 p-3 sm:p-2.5 rounded-lg border border-purple-200/50 dark:border-purple-800/20 text-left overflow-hidden focus:outline-none focus-visible:ring-2 focus-visible:ring-purple-400 dark:focus-visible:ring-purple-500 cursor-pointer backdrop-blur-sm touch-manipulation"
              onClick={handleLongBreakChange}
              aria-label={`Change long break time`}
              variants={shouldReduceMotion ? {} : cardVariants}
              whileHover={shouldReduceMotion ? {} : hoverVariants.hover}
              whileTap={shouldReduceMotion ? {} : hoverVariants.tap}
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-600/8 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                layoutId="long-break-bg"
              />
              <div className="flex items-center justify-between relative z-10">
                <div className="flex items-center gap-2 sm:gap-1.5">
                  <motion.div
                    className="bg-purple-500/15 p-1.5 sm:p-1 rounded-md ring-1 ring-purple-500/20"
                    variants={shouldReduceMotion ? {} : iconVariants}
                    whileHover="hover"
                  >
                    <Clock className="h-4 w-4 sm:h-3 sm:w-3 text-purple-600 dark:text-purple-400" />
                  </motion.div>
                  <div className="text-sm sm:text-xs font-medium text-purple-700 dark:text-purple-300">Long Break</div>
                </div>
                <div className="flex items-center gap-1.5 sm:gap-1">
                  <motion.span
                    className="text-lg sm:text-sm font-bold text-purple-700 dark:text-purple-300"
                    key={longBreakMinutes}
                    initial={{ scale: 1, opacity: 1 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    {initialRender ? "15m" : formatTime(longBreakMinutes)}
                  </motion.span>
                  <motion.div
                    variants={shouldReduceMotion ? {} : chevronVariants}
                    whileHover="hover"
                  >
                    <ChevronRight className="h-4 w-4 sm:h-3 sm:w-3 text-purple-500 opacity-60 group-hover:opacity-100 transition-opacity" />
                  </motion.div>
                </div>
              </div>
              <motion.div
                className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-purple-400/30 to-purple-500/30 dark:from-purple-500/20 dark:to-purple-400/20 rounded-b-lg"
                layoutId="long-break-indicator"
              />
            </motion.button>

            {/* Sessions Count - Mobile Optimized */}
            <motion.button
              className="group relative bg-gradient-to-br from-amber-50/80 to-amber-100/40 dark:from-amber-950/15 dark:to-amber-900/10 p-3 sm:p-2.5 rounded-lg border border-amber-200/50 dark:border-amber-800/20 text-left overflow-hidden focus:outline-none focus-visible:ring-2 focus-visible:ring-amber-400 dark:focus-visible:ring-amber-500 cursor-pointer backdrop-blur-sm touch-manipulation"
              onClick={handleSessionCountChange}
              aria-label={`Change session count`}
              variants={shouldReduceMotion ? {} : cardVariants}
              whileHover={shouldReduceMotion ? {} : hoverVariants.hover}
              whileTap={shouldReduceMotion ? {} : hoverVariants.tap}
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-br from-amber-500/5 to-amber-600/8 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                layoutId="sessions-bg"
              />
              <div className="flex items-center justify-between relative z-10">
                <div className="flex items-center gap-2 sm:gap-1.5">
                  <motion.div
                    className="bg-amber-500/15 p-1.5 sm:p-1 rounded-md ring-1 ring-amber-500/20"
                    variants={shouldReduceMotion ? {} : iconVariants}
                    whileHover="hover"
                  >
                    <Clock className="h-4 w-4 sm:h-3 sm:w-3 text-amber-600 dark:text-amber-400" />
                  </motion.div>
                  <div className="text-sm sm:text-xs font-medium text-amber-700 dark:text-amber-300">Sessions</div>
                </div>
                <div className="flex items-center gap-1.5 sm:gap-1">
                  <motion.span
                    className="text-lg sm:text-sm font-bold text-amber-700 dark:text-amber-300"
                    key={sessionsBeforeLongBreak}
                    initial={{ scale: 1, opacity: 1 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    {initialRender ? "4" : sessionsBeforeLongBreak}
                  </motion.span>
                  <motion.div
                    variants={shouldReduceMotion ? {} : chevronVariants}
                    whileHover="hover"
                  >
                    <ChevronRight className="h-4 w-4 sm:h-3 sm:w-3 text-amber-500 opacity-60 group-hover:opacity-100 transition-opacity" />
                  </motion.div>
                </div>
              </div>
              <motion.div
                className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-amber-400/30 to-amber-500/30 dark:from-amber-500/20 dark:to-amber-400/20 rounded-b-lg"
                layoutId="sessions-indicator"
              />
            </motion.button>
          </motion.div>

          {/* Timer Cycle Visualization - Mobile Enhanced */}
          <motion.div
            className="relative group"
            variants={shouldReduceMotion ? {} : cardVariants}
            layout
          >
            <div className="overflow-x-auto overflow-y-hidden h-[2.5rem] sm:h-[2rem] w-full
              [&::-webkit-scrollbar]:h-1.5
              [&::-webkit-scrollbar]:opacity-0
              [&::-webkit-scrollbar]:transition-opacity
              [&::-webkit-scrollbar]:duration-300
              [&::-webkit-scrollbar]:ease-in-out
              [&::-webkit-scrollbar-track]:bg-transparent
              [&::-webkit-scrollbar-track]:rounded-full
              [&::-webkit-scrollbar-thumb]:bg-gray-400/60
              [&::-webkit-scrollbar-thumb]:rounded-full
              [&::-webkit-scrollbar-thumb]:transition-colors
              [&::-webkit-scrollbar-thumb]:duration-200
              dark:[&::-webkit-scrollbar-thumb]:bg-gray-500/60
              group-hover:[&::-webkit-scrollbar]:opacity-100
              [&::-webkit-scrollbar-thumb:hover]:bg-gray-500/80
              dark:[&::-webkit-scrollbar-thumb:hover]:bg-gray-400/80
              [scrollbar-width:none]
              hover:[scrollbar-width:thin]
              hover:[scrollbar-color:rgb(156_163_175_/_0.6)_transparent]
              dark:hover:[scrollbar-color:rgb(107_114_128_/_0.6)_transparent]">
              <motion.div
                className="flex gap-1 sm:gap-0.5 py-1.5 sm:py-1 w-max pr-4"
                suppressHydrationWarning
                variants={shouldReduceMotion ? {} : containerVariants}
                layout
                transition={{
                  layout: {
                    duration: 0.3,
                    ease: [0.4, 0.0, 0.2, 1]
                  }
                }}
              >
                <AnimatePresence mode="popLayout">
                  {generateTimerCycle()}
                </AnimatePresence>
              </motion.div>
            </div>
          </motion.div>

          {/* Session Summary Stats - Mobile Enhanced Layout */}
          <motion.div
            className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 pt-3 mt-2 border-t border-border/30"
            variants={shouldReduceMotion ? {} : containerVariants}
          >
            {/* Mobile: justify-between for Total and End Time only, Desktop: Row layout with justify-between for all */}
            <div className="flex justify-between sm:gap-0 sm:justify-between w-full" suppressHydrationWarning>
              {/* Total Time - Only show for countdown mode */}
              {timerMode === 'countDown' && (
                <motion.div
                  className="flex items-center gap-2 sm:gap-1.5"
                  variants={shouldReduceMotion ? {} : cardVariants}
                >
                  <motion.div
                    className="flex items-center justify-center w-6 h-6 sm:w-5 sm:h-5 bg-emerald-50 dark:bg-emerald-950/30 rounded-md ring-1 ring-emerald-500/20"
                    whileHover={shouldReduceMotion ? {} : { scale: 1.1 }}
                  >
                    <Check className="h-3.5 w-3.5 sm:h-2.5 sm:w-2.5 text-emerald-500" />
                  </motion.div>
                  <div className="flex items-center gap-1">
                    <span className="text-xs sm:text-[10px] text-muted-foreground">Total:</span>
                    <motion.span
                      className="text-sm sm:text-xs font-bold text-foreground"
                      suppressHydrationWarning
                      key={totalMinutes}
                      initial={{ scale: 1, opacity: 1 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ duration: 0.2 }}
                    >
                      {initialRender ? "2h 10m" : (hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`)}
                    </motion.span>
                  </div>
                </motion.div>
              )}

              {/* Timer Mode Info - Show for count-up mode */}
              {timerMode === 'countUp' && (
                <motion.div
                  className="flex items-center gap-2 sm:gap-1.5"
                  variants={shouldReduceMotion ? {} : cardVariants}
                >
                  <motion.div
                    className="flex items-center justify-center w-6 h-6 sm:w-5 sm:h-5 bg-emerald-50 dark:bg-emerald-950/30 rounded-md ring-1 ring-emerald-500/20"
                    whileHover={shouldReduceMotion ? {} : { scale: 1.1 }}
                  >
                    <Timer className="h-3.5 w-3.5 sm:h-2.5 sm:w-2.5 text-emerald-500" />
                  </motion.div>
                  <div className="flex items-center gap-1">
                    <span className="text-xs sm:text-[10px] text-muted-foreground">Mode:</span>
                    <motion.span
                      className="text-sm sm:text-xs font-bold text-foreground"
                      suppressHydrationWarning
                      initial={{ scale: 1, opacity: 1 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ duration: 0.2 }}
                    >
                      Count Up
                    </motion.span>
                  </div>
                </motion.div>
              )}

              {/* End Time - Only show for countdown mode */}
              {timerMode === 'countDown' && endTime && (
                <motion.div
                  className="flex items-center gap-2 sm:gap-1.5"
                  variants={shouldReduceMotion ? {} : cardVariants}
                  suppressHydrationWarning
                >
                  <motion.div
                    className="flex items-center justify-center w-6 h-6 sm:w-5 sm:h-5 bg-blue-50 dark:bg-blue-950/30 rounded-md ring-1 ring-blue-500/20"
                    whileHover={shouldReduceMotion ? {} : { scale: 1.1 }}
                  >
                    <Clock className="h-3.5 w-3.5 sm:h-2.5 sm:w-2.5 text-blue-500" />
                  </motion.div>
                  <div className="flex items-center gap-1">
                    <span className="text-xs sm:text-[10px] text-muted-foreground">Ends:</span>
                    <motion.span
                      className="text-sm sm:text-xs font-bold text-foreground"
                      suppressHydrationWarning
                      key={endTime.getTime()}
                      initial={{ scale: 1, opacity: 1 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ duration: 0.2 }}
                    >
                      {initialRender ? (
                        <span className="inline-flex items-center">
                          00:00 PM
                        </span>
                      ) : formatEndTime(endTime)}
                    </motion.span>
                  </div>
                </motion.div>
              )}

              {/* Flexible Duration Info - Show for count-up mode */}
              {timerMode === 'countUp' && (
                <motion.div
                  className="flex items-center gap-2 sm:gap-1.5"
                  variants={shouldReduceMotion ? {} : cardVariants}
                  suppressHydrationWarning
                >
                  <motion.div
                    className="flex items-center justify-center w-6 h-6 sm:w-5 sm:h-5 bg-blue-50 dark:bg-blue-950/30 rounded-md ring-1 ring-blue-500/20"
                    whileHover={shouldReduceMotion ? {} : { scale: 1.1 }}
                  >
                    <Clock className="h-3.5 w-3.5 sm:h-2.5 sm:w-2.5 text-blue-500" />
                  </motion.div>
                  <div className="flex items-center gap-1">
                    <span className="text-xs sm:text-[10px] text-muted-foreground">Duration:</span>
                    <motion.span
                      className="text-sm sm:text-xs font-bold text-foreground"
                      suppressHydrationWarning
                      initial={{ scale: 1, opacity: 1 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ duration: 0.2 }}
                    >
                      Flexible
                    </motion.span>
                  </div>
                </motion.div>
              )}

              {/* Sessions Count - Hidden on mobile, visible on desktop */}
              <motion.div
                className="hidden sm:flex items-center gap-2 sm:gap-1.5"
                variants={shouldReduceMotion ? {} : cardVariants}
              >
                <motion.div
                  className="flex items-center justify-center w-6 h-6 sm:w-5 sm:h-5 bg-amber-50 dark:bg-amber-950/30 rounded-md ring-1 ring-amber-500/20"
                  whileHover={shouldReduceMotion ? {} : { scale: 1.1 }}
                >
                  <Clock className="h-3.5 w-3.5 sm:h-2.5 sm:w-2.5 text-amber-500" />
                </motion.div>
                <div className="flex items-center gap-1">
                  <motion.span
                    className="text-sm sm:text-xs font-bold text-foreground"
                    suppressHydrationWarning
                    key={sessionsBeforeLongBreak}
                    initial={{ scale: 1, opacity: 1 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    {initialRender ? "4" : sessionsBeforeLongBreak}
                  </motion.span>
                  <span className="text-xs sm:text-[10px] text-muted-foreground">sessions</span>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Enhanced Start Timer Button - Mobile Optimized */}
      <motion.div
        className="px-3 sm:px-4 pb-4 sm:pb-6 pt-3 bg-gradient-to-b from-transparent via-muted/10 to-muted/20"
        variants={shouldReduceMotion ? {} : cardVariants}
      >
        <motion.div
          variants={shouldReduceMotion ? {} : cardVariants}
          whileHover={shouldReduceMotion ? {} : { scale: 1.02 }}
          whileTap={shouldReduceMotion ? {} : { scale: 0.98 }}
          className="touch-manipulation"
        >
          <StartTimerButton isDisabled={false} />
        </motion.div>
      </motion.div>

      {/* Quick Stats Dialog */}
      <QuickStatsDialog
        isOpen={isStatsDialogOpen}
        onOpenChange={setIsStatsDialogOpen}
        quickStatsData={quickStatsData}
        isLoading={statsLoading}
        error={statsError}
      />
    </motion.div>
  );
}