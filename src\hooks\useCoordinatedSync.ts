import { useEffect, useState, useCallback } from 'react';
import { useUserStore } from '@/store/userStore';
import { useLocalTaskStore, type LocalTask } from '@/lib/local-task-store';
import { useLocalPomodoroStore, type LocalPomodoroSession } from '@/lib/local-pomodoro-store';
import { 
  useBulkTransferTasks,
  type BulkTransferTask,
} from '@schemas/Tasks/task-query';
import { 
  useBulkTransferSessions,
  type BulkTransferSession 
} from '@schemas/Pomodoro/pomodoro-query';
import { toast } from 'sonner';

interface SyncStatus {
  isTaskSyncComplete: boolean;
  isSessionSyncComplete: boolean;
  isSyncing: boolean;
  hasError: boolean;
  errorMessage?: string;
}

interface SyncResult {
  tasksTransferred: number;
  sessionsTransferred: number;
  taskIdMapping: Record<string, string>; // local_id -> database_id
}

// Track sync status globally to prevent multiple sync attempts
let globalSyncInProgress = false;

export function useCoordinatedSync() {
  const { isAuthenticated } = useUserStore();
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isTaskSyncComplete: false,
    isSessionSyncComplete: false,
    isSyncing: false,
    hasError: false,
  });

  // Local storage hooks
  const {
    exportTasks,
    clearAllTasks: clearLocalTasks,
  } = useLocalTaskStore();

  const {
    exportSessions,
    clearAllSessions: clearLocalSessions,
  } = useLocalPomodoroStore();

  // API hooks
  const bulkTransferTasks = useBulkTransferTasks();
  const bulkTransferSessions = useBulkTransferSessions();

  // Enhanced bulk transfer tasks with return mapping
  const transferTasksWithMapping = useCallback(async (tasks: LocalTask[]): Promise<Record<string, string>> => {
    if (tasks.length === 0) return {};

    console.log(`Starting task transfer: ${tasks.length} tasks`);

    const tasksToTransfer: BulkTransferTask[] = tasks.map(task => ({
      localId: task.id, // Include the local task ID
      title: task.title,
      completed: task.completed.toString(),
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
    }));

    const result = await bulkTransferTasks.mutateAsync({ tasks: tasksToTransfer });
    
    console.log(`Task transfer completed: ${result.data.transferred} transferred, ${result.data.skipped} skipped`);
    
    // Return the direct mapping from backend if available
    return result.data.localToDbMapping || {};
  }, [bulkTransferTasks]);

  // Transfer pomodoro sessions with local task IDs (backend will handle mapping)
  const transferSessions = useCallback(async (
    sessions: LocalPomodoroSession[], 
    taskIdMapping: Record<string, string> // Keep for logging but not used in mapping
  ): Promise<number> => {
    if (sessions.length === 0) return 0;

    console.log(`Starting session transfer: ${sessions.length} sessions`);

    const sessionsToTransfer: BulkTransferSession[] = sessions.map(session => {
      // Send the original local task ID to backend for mapping
      const localTaskId = session.taskId && session.taskId.startsWith('local_task_') 
        ? session.taskId 
        : undefined;

      if (localTaskId) {
        console.log(`Session ${session.id} linked to local task: ${localTaskId}`);
      } else if (session.taskId) {
        console.log(`Session ${session.id} has non-local task ID: ${session.taskId}`);
      } else {
        console.log(`Session ${session.id} has no task association`);
      }

      return {
        startTime: session.startTime,
        endTime: session.endTime,
        totalDuration: session.totalDuration.toString(),
        focusDuration: session.focusDuration?.toString(),
        breakDuration: session.breakDuration?.toString(),
        intervalType: session.intervalType,
        completed: session.completed.toString(),
        interrupted: session.interrupted.toString(),
        note: session.note || undefined,
        interruptedSessions: session.interruptedSessions ? JSON.stringify(session.interruptedSessions) : undefined,
        localTaskId: localTaskId, // Send local task ID for backend mapping
      };
    });

    const result = await bulkTransferSessions.mutateAsync({ sessions: sessionsToTransfer });
    
    console.log(`Session transfer completed: ${result.data.transferred} transferred, ${result.data.skipped} skipped`);
    
    return result.data.transferred;
  }, [bulkTransferSessions]);

  // Main coordinated sync function
  const performCoordinatedSync = useCallback(async (): Promise<SyncResult | null> => {
    if (!isAuthenticated || globalSyncInProgress) {
      return null;
    }

    globalSyncInProgress = true;
    
    setSyncStatus({
      isTaskSyncComplete: false,
      isSessionSyncComplete: false,
      isSyncing: true,
      hasError: false,
    });

    try {
      // Step 1: Get local data
      const localTasks = exportTasks();
      const localSessions = exportSessions();

      console.log(`Starting coordinated sync: ${localTasks.length} tasks, ${localSessions.length} sessions`);

      let taskIdMapping: Record<string, string> = {};
      let tasksTransferred = 0;

      // Step 2: Transfer tasks first (if any)
      if (localTasks.length > 0) {
        console.log('Phase 1: Transferring tasks...');
        taskIdMapping = await transferTasksWithMapping(localTasks);
        tasksTransferred = localTasks.length; // Assuming all tasks transfer successfully
        
        setSyncStatus(prev => ({
          ...prev,
          isTaskSyncComplete: true,
        }));
        
        console.log('Phase 1 complete: Tasks transferred');
      } else {
        setSyncStatus(prev => ({
          ...prev,
          isTaskSyncComplete: true,
        }));
      }

      // Step 3: Transfer pomodoro sessions with mapped task IDs
      let sessionsTransferred = 0;
      if (localSessions.length > 0) {
        console.log('Phase 2: Transferring pomodoro sessions...');
        sessionsTransferred = await transferSessions(localSessions, taskIdMapping);
        
        setSyncStatus(prev => ({
          ...prev,
          isSessionSyncComplete: true,
        }));
        
        console.log('Phase 2 complete: Sessions transferred');
      } else {
        setSyncStatus(prev => ({
          ...prev,
          isSessionSyncComplete: true,
        }));
      }

      // Step 4: Clear local storage after successful transfer
      if (localTasks.length > 0) {
        clearLocalTasks();
        console.log('Local tasks cleared');
      }
      
      if (localSessions.length > 0) {
        clearLocalSessions();
        console.log('Local sessions cleared');
      }

      // Step 5: Show success notification
      if (tasksTransferred > 0 || sessionsTransferred > 0) {
        toast.success(
          `Successfully synced your data to your account`,
          {
            description: `${tasksTransferred} tasks and ${sessionsTransferred} pomodoro sessions transferred`,
            duration: 5000,
          }
        );
      }

      setSyncStatus({
        isTaskSyncComplete: true,
        isSessionSyncComplete: true,
        isSyncing: false,
        hasError: false,
      });

      const result: SyncResult = {
        tasksTransferred,
        sessionsTransferred,
        taskIdMapping,
      };

      console.log('Coordinated sync completed successfully:', result);
      return result;

    } catch (error) {
      console.error('Coordinated sync failed:', error);
      
      setSyncStatus({
        isTaskSyncComplete: false,
        isSessionSyncComplete: false,
        isSyncing: false,
        hasError: true,
        errorMessage: error instanceof Error ? error.message : 'Unknown sync error',
      });

      toast.error('Failed to sync your data', {
        description: 'Your local data is safe. We\'ll try again when you reload the page.',
        duration: 8000,
      });

      return null;
    } finally {
      globalSyncInProgress = false;
    }
  }, [isAuthenticated, exportTasks, exportSessions, transferTasksWithMapping, transferSessions, clearLocalTasks, clearLocalSessions]);

  // Auto-trigger sync when user authenticates
  useEffect(() => {
    const runSync = async () => {
      if (isAuthenticated && !syncStatus.isTaskSyncComplete && !syncStatus.isSyncing) {
        const localTasks = exportTasks();
        const localSessions = exportSessions();
        
        // Only sync if we have local data
        if (localTasks.length > 0 || localSessions.length > 0) {
          await performCoordinatedSync();
        } else {
          // No local data to sync
          setSyncStatus({
            isTaskSyncComplete: true,
            isSessionSyncComplete: true,
            isSyncing: false,
            hasError: false,
          });
        }
      }
    };

    runSync();
  }, [isAuthenticated, syncStatus.isTaskSyncComplete, syncStatus.isSyncing, exportTasks, exportSessions, performCoordinatedSync]);

  // Reset sync status when user logs out
  useEffect(() => {
    if (!isAuthenticated) {
      setSyncStatus({
        isTaskSyncComplete: false,
        isSessionSyncComplete: false,
        isSyncing: false,
        hasError: false,
      });
      globalSyncInProgress = false;
    }
  }, [isAuthenticated]);

  return {
    syncStatus,
    performCoordinatedSync,
    isAuthenticated,
  };
} 