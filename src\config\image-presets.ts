/**
 * Image presets configuration for playlist forms
 * All images are optimized for 640x393 resolution from Pexels
 */

export interface ImagePreset {
  id: string
  url: string
  alt: string
  category: 'music' | 'nature' | 'general'
}

export const MUSIC_IMAGE_PRESETS: ImagePreset[] = [
  {
    id: 'music-studio',
    url: 'https://images.pexels.com/photos/2325447/pexels-photo-2325447.jpeg?auto=compress&cs=tinysrgb&w=640&h=393&dpr=1',
    alt: 'Music Studio',
    category: 'music'
  },
  {
    id: 'vinyl-records',
    url: 'https://images.pexels.com/photos/886521/pexels-photo-886521.jpeg?auto=compress&cs=tinysrgb&w=640&h=393&dpr=1',
    alt: 'Vinyl Records',
    category: 'music'
  },
  {
    id: 'music-equipment',
    url: 'https://images.pexels.com/photos/351265/pexels-photo-351265.jpeg?auto=compress&cs=tinysrgb&w=640&h=393&dpr=1',
    alt: 'Music Equipment',
    category: 'music'
  }
]

export const NATURE_IMAGE_PRESETS: ImagePreset[] = [
  {
    id: 'forest-nature',
    url: 'https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg?auto=compress&cs=tinysrgb&w=640&h=393&dpr=1',
    alt: 'Forest Nature',
    category: 'nature'
  },
  {
    id: 'ocean-waves',
    url: 'https://images.pexels.com/photos/1001682/pexels-photo-1001682.jpeg?auto=compress&cs=tinysrgb&w=640&h=393&dpr=1',
    alt: 'Ocean Waves',
    category: 'nature'
  },
  {
    id: 'mountain-landscape',
    url: 'https://images.pexels.com/photos/1366630/pexels-photo-1366630.jpeg?auto=compress&cs=tinysrgb&w=640&h=393&dpr=1',
    alt: 'Mountain Landscape',
    category: 'nature'
  }
]

// Combined presets for general use
export const ALL_IMAGE_PRESETS: ImagePreset[] = [
  ...MUSIC_IMAGE_PRESETS,
  ...NATURE_IMAGE_PRESETS
]

// Helper function to get presets by category
export const getImagePresetsByCategory = (category: 'music' | 'nature' | 'all' = 'all'): ImagePreset[] => {
  switch (category) {
    case 'music':
      return MUSIC_IMAGE_PRESETS
    case 'nature':
      return NATURE_IMAGE_PRESETS
    case 'all':
    default:
      return ALL_IMAGE_PRESETS
  }
}
