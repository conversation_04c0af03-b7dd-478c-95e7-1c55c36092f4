"use client";

import { useState } from "react";
import { useToggleFavorite } from "@schemas/Video/video-query";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { Video } from "../../components/types";
import { Heart, Share2, Crown } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

interface VideoActionsProps {
  video: Video;
}

export function VideoActions({ video }: VideoActionsProps) {
  const router = useRouter();
  const [isFavorited, setIsFavorited] = useState<boolean>(!!video.isFavorite);
  const toggleFavoriteMutation = useToggleFavorite();

  const handleToggleFavorite = async () => {
    try {
      await toggleFavoriteMutation.mutateAsync({ id: video.id });
      setIsFavorited(!isFavorited);
      toast.success(isFavorited ? "Removed from favorites" : "Added to favorites");
    } catch {
      toast.error("Failed to update favorite status");
    }
  };

  const handleShare = async () => {
    try {
      await navigator.clipboard.writeText(`${window.location.origin}/videos/${video.id}`);
      toast.success("Link copied to clipboard");
    } catch {
      toast.error("Failed to copy link");
    }
  };

  return (
    <div className="flex flex-wrap items-center justify-between gap-4">
      <div className="flex items-center gap-2">
        {/* Video status badges */}
        {!video.isPublic && (
          <Badge variant="outline" className="text-xs">
            Private
          </Badge>
        )}
        
        {video.isPremium && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge className="bg-amber-500 hover:bg-amber-600 text-xs gap-1">
                  <Crown className="h-3 w-3" />
                  Premium
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>This is premium content</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
        
        {video.playlist && (
          <Badge 
            variant="secondary" 
            className="text-xs cursor-pointer"
            onClick={() => router.push(`/admin/music-playlists/${video.playlist?.id}`)}
          >
            {video.playlist.name}
          </Badge>
        )}
      </div>
      
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          className={isFavorited ? "text-red-500" : ""}
          onClick={handleToggleFavorite}
          disabled={toggleFavoriteMutation.isPending}
        >
          <Heart className={cn("h-5 w-5 mr-1", isFavorited ? "fill-red-500" : "")} />
          {isFavorited ? "Favorited" : "Favorite"}
        </Button>
        
        <Button variant="ghost" size="sm" onClick={handleShare}>
          <Share2 className="h-5 w-5 mr-1" />
          Share
        </Button>
      </div>
    </div>
  );
} 