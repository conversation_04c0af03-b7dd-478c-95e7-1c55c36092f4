import { Check, ChevronsUpDown, ExternalLink, Loader2, Music2, PlaySquare, Plus, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useState } from "react";
import { useGetAdminMusicPlaylists } from "@schemas/MusicPlaylist/music-playlist-admin-query";
import { useGetNaturePlaylists } from "@schemas/NaturalPlaylist/nature-playlist-query";
import { useUpdateVideoMusicPlaylist, useUpdateVideoNaturePlaylist } from "@schemas/Video/video-query";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface Music {
  id: string;
  title: string;
}

interface NatureSound {
  id: string;
  title: string;
}

interface MusicPlaylist {
  id: string;
  name: string;
  isPublic: boolean;
  musics: Music[];
}

interface NaturePlaylist {
  id: string;
  name: string;
  isPublic: boolean;
  natureSounds: NatureSound[];
}

interface PlaylistSelectorProps {
  videoId: string;
  currentMusicPlaylistId: string | null;
  currentNaturePlaylistId: string | null;
}

export function PlaylistSelector({ videoId, currentMusicPlaylistId, currentNaturePlaylistId }: PlaylistSelectorProps) {
  const [openMusic, setOpenMusic] = useState(false);
  const [openNature, setOpenNature] = useState(false);
  const { data: musicPlaylists, isLoading: isLoadingMusicPlaylists } = useGetAdminMusicPlaylists();
  const { data: naturePlaylists, isLoading: isLoadingNaturePlaylists } = useGetNaturePlaylists();
  const { mutate: updateMusicPlaylist, isPending: isUpdatingMusic } = useUpdateVideoMusicPlaylist();
  const { mutate: updateNaturePlaylist, isPending: isUpdatingNature } = useUpdateVideoNaturePlaylist();

  const handleSelectMusic = (musicPlaylistId: string | null) => {
    updateMusicPlaylist(
      { videoId, musicPlaylistId },
      {
        onSuccess: () => {
          setOpenMusic(false);
        },
      }
    );
  };

  const handleSelectNature = (naturePlaylistId: string | null) => {
    updateNaturePlaylist(
      { videoId, naturePlaylistId },
      {
        onSuccess: () => {
          setOpenNature(false);
        },
      }
    );
  };

  const currentMusicPlaylist = musicPlaylists?.find((playlist: MusicPlaylist) => playlist.id === currentMusicPlaylistId);
  const currentNaturePlaylist = naturePlaylists?.find((playlist: NaturePlaylist) => playlist.id === currentNaturePlaylistId);

  return (
    <div className="flex flex-col gap-3">
      <Tabs defaultValue="music" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="music">Music Playlist</TabsTrigger>
          <TabsTrigger value="nature">Nature Playlist</TabsTrigger>
        </TabsList>
        
        <TabsContent value="music" className="mt-4">
          <TooltipProvider>
            <Popover open={openMusic} onOpenChange={setOpenMusic}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={openMusic}
                  aria-label="Select a music playlist"
                  className={cn(
                    "w-full justify-between",
                    "transition-colors duration-200",
                    currentMusicPlaylist && "border-primary"
                  )}
                  disabled={isLoadingMusicPlaylists || isUpdatingMusic}
                >
                  {isLoadingMusicPlaylists ? (
                    <span className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Loading music playlists...
                    </span>
                  ) : currentMusicPlaylist ? (
                    <span className="flex items-center gap-2">
                      <Music2 className="h-4 w-4 text-primary" />
                      {currentMusicPlaylist.name}
                    </span>
                  ) : (
                    <span className="flex items-center gap-2">
                      <Plus className="h-4 w-4" />
                      Add to music playlist
                    </span>
                  )}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[300px] p-0" align="start">
                <Command>
                  <CommandInput placeholder="Search music playlists..." />
                  <CommandEmpty className="py-6 text-center text-sm">
                    No music playlists found.
                  </CommandEmpty>
                  <CommandGroup>
                    <ScrollArea className="h-[300px]">
                      {/* Option to remove playlist */}
                      {currentMusicPlaylistId && (
                        <>
                          <CommandItem
                            key="remove"
                            onSelect={() => handleSelectMusic(null)}
                            className="flex items-center gap-2 text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                            Remove from music playlist
                          </CommandItem>
                          <CommandSeparator />
                        </>
                      )}
                      
                      {/* List all music playlists */}
                      {musicPlaylists?.map((playlist: MusicPlaylist) => (
                        <Tooltip key={playlist.id}>
                          <TooltipTrigger asChild>
                            <CommandItem
                              onSelect={() => handleSelectMusic(playlist.id)}
                              className="flex items-center justify-between"
                            >
                              <div className="flex items-center gap-2">
                                <Music2 className={cn(
                                  "h-4 w-4",
                                  playlist.id === currentMusicPlaylistId && "text-primary"
                                )} />
                                <span>{playlist.name}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                {playlist.musics?.length > 0 && (
                                  <Badge variant="secondary" className="ml-2">
                                    <Music2 className="mr-1 h-3 w-3" />
                                    {playlist.musics.length}
                                  </Badge>
                                )}
                                {playlist.id === currentMusicPlaylistId && (
                                  <Check className="h-4 w-4 text-primary" />
                                )}
                              </div>
                            </CommandItem>
                          </TooltipTrigger>
                          <TooltipContent side="right" align="start">
                            <p>{playlist.isPublic ? "Public" : "Private"} music playlist</p>
                            <p className="text-xs text-muted-foreground">
                              {playlist.musics.length} tracks
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      ))}
                    </ScrollArea>
                  </CommandGroup>
                </Command>
              </PopoverContent>
            </Popover>
          </TooltipProvider>

          {/* Display current music playlist details if one is selected */}
          {currentMusicPlaylist && (
            <div className="rounded-lg border bg-card p-4 space-y-4 animate-in fade-in-50 slide-in-from-top-2 mt-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <h3 className="font-semibold">Current Music Playlist</h3>
                  <p className="text-sm text-muted-foreground">
                    {currentMusicPlaylist.musics.length} total tracks
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={currentMusicPlaylist.isPublic ? "default" : "secondary"}>
                    {currentMusicPlaylist.isPublic ? "Public" : "Private"}
                  </Badge>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    className="h-8 w-8 text-primary hover:text-primary hover:bg-primary/10"
                    onClick={() => window.open(`/admin/music-playlists/${currentMusicPlaylist.id}`, '_blank')}
                    title="Open music playlist in new tab"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="icon"
                        className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Remove from Music Playlist</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to remove this video from &quot;{currentMusicPlaylist.name}&quot;?
                          This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          onClick={() => handleSelectMusic(null)}
                          disabled={isUpdatingMusic}
                        >
                          {isUpdatingMusic ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Removing...
                            </>
                          ) : (
                            "Remove"
                          )}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
              
              {currentMusicPlaylist.musics?.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium flex items-center gap-2">
                    <Music2 className="h-4 w-4" />
                    Music Tracks ({currentMusicPlaylist.musics.length})
                  </h4>
                  <ScrollArea className="h-[100px]">
                    <div className="flex flex-wrap gap-2">
                      {currentMusicPlaylist.musics.map((music: Music) => (
                        <Badge key={music.id} variant="secondary" className="animate-in fade-in-50">
                          {music.title}
                        </Badge>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="nature" className="mt-4">
          <TooltipProvider>
            <Popover open={openNature} onOpenChange={setOpenNature}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={openNature}
                  aria-label="Select a nature playlist"
                  className={cn(
                    "w-full justify-between",
                    "transition-colors duration-200",
                    currentNaturePlaylist && "border-primary"
                  )}
                  disabled={isLoadingNaturePlaylists || isUpdatingNature}
                >
                  {isLoadingNaturePlaylists ? (
                    <span className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Loading nature playlists...
                    </span>
                  ) : currentNaturePlaylist ? (
                    <span className="flex items-center gap-2">
                      <PlaySquare className="h-4 w-4 text-primary" />
                      {currentNaturePlaylist.name}
                    </span>
                  ) : (
                    <span className="flex items-center gap-2">
                      <Plus className="h-4 w-4" />
                      Add to nature playlist
                    </span>
                  )}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[300px] p-0" align="start">
                <Command>
                  <CommandInput placeholder="Search nature playlists..." />
                  <CommandEmpty className="py-6 text-center text-sm">
                    No nature playlists found.
                  </CommandEmpty>
                  <CommandGroup>
                    <ScrollArea className="h-[300px]">
                      {/* Option to remove playlist */}
                      {currentNaturePlaylistId && (
                        <>
                          <CommandItem
                            key="remove"
                            onSelect={() => handleSelectNature(null)}
                            className="flex items-center gap-2 text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                            Remove from nature playlist
                          </CommandItem>
                          <CommandSeparator />
                        </>
                      )}
                      
                      {/* List all nature playlists */}
                      {naturePlaylists?.map((playlist: NaturePlaylist) => (
                        <Tooltip key={playlist.id}>
                          <TooltipTrigger asChild>
                            <CommandItem
                              onSelect={() => handleSelectNature(playlist.id)}
                              className="flex items-center justify-between"
                            >
                              <div className="flex items-center gap-2">
                                <PlaySquare className={cn(
                                  "h-4 w-4",
                                  playlist.id === currentNaturePlaylistId && "text-primary"
                                )} />
                                <span>{playlist.name}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                {playlist.natureSounds?.length > 0 && (
                                  <Badge variant="secondary" className="ml-2">
                                    <PlaySquare className="mr-1 h-3 w-3" />
                                    {playlist.natureSounds.length}
                                  </Badge>
                                )}
                                {playlist.id === currentNaturePlaylistId && (
                                  <Check className="h-4 w-4 text-primary" />
                                )}
                              </div>
                            </CommandItem>
                          </TooltipTrigger>
                          <TooltipContent side="right" align="start">
                            <p>{playlist.isPublic ? "Public" : "Private"} nature playlist</p>
                            <p className="text-xs text-muted-foreground">
                              {playlist.natureSounds.length} nature sounds
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      ))}
                    </ScrollArea>
                  </CommandGroup>
                </Command>
              </PopoverContent>
            </Popover>
          </TooltipProvider>

          {/* Display current nature playlist details if one is selected */}
          {currentNaturePlaylist && (
            <div className="rounded-lg border bg-card p-4 space-y-4 animate-in fade-in-50 slide-in-from-top-2 mt-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <h3 className="font-semibold">Current Nature Playlist</h3>
                  <p className="text-sm text-muted-foreground">
                    {currentNaturePlaylist.natureSounds.length} total sounds
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={currentNaturePlaylist.isPublic ? "default" : "secondary"}>
                    {currentNaturePlaylist.isPublic ? "Public" : "Private"}
                  </Badge>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    className="h-8 w-8 text-primary hover:text-primary hover:bg-primary/10"
                    onClick={() => window.open(`/admin/nature-playlists/${currentNaturePlaylist.id}`, '_blank')}
                    title="Open nature playlist in new tab"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="icon"
                        className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Remove from Nature Playlist</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to remove this video from &quot;{currentNaturePlaylist.name}&quot;?
                          This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          onClick={() => handleSelectNature(null)}
                          disabled={isUpdatingNature}
                        >
                          {isUpdatingNature ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Removing...
                            </>
                          ) : (
                            "Remove"
                          )}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
              
              {currentNaturePlaylist.natureSounds?.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium flex items-center gap-2">
                    <PlaySquare className="h-4 w-4" />
                    Nature Sounds ({currentNaturePlaylist.natureSounds.length})
                  </h4>
                  <ScrollArea className="h-[100px]">
                    <div className="flex flex-wrap gap-2">
                      {currentNaturePlaylist.natureSounds.map((sound: NatureSound) => (
                        <Badge key={sound.id} variant="secondary" className="animate-in fade-in-50">
                          {sound.title}
                        </Badge>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
} 