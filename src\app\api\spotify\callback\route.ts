import { NextRequest, NextResponse } from 'next/server';
import { spotifyAuth } from '@/lib/spotify-auth';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const code = searchParams.get('code');
  const error = searchParams.get('error');

  // Handle OAuth error
  if (error) {
    return NextResponse.redirect(
      new URL(`/timer?spotify_error=${encodeURIComponent(error)}`, request.url)
    );
  }

  // Handle missing code
  if (!code) {
    return NextResponse.redirect(
      new URL('/timer?spotify_error=missing_code', request.url)
    );
  }

  try {
    // Exchange code for tokens
    await spotifyAuth.exchangeCodeForTokens(code);
    
    // Redirect back to timer with success
    return NextResponse.redirect(
      new URL('/timer?spotify_success=true', request.url)
    );
  } catch (error) {
    console.error('Failed to exchange code for tokens:', error);
    return NextResponse.redirect(
      new URL('/timer?spotify_error=token_exchange_failed', request.url)
    );
  }
} 