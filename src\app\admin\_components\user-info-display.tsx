"use client";

import { User } from "@/server/private/get-user-server";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { CalendarDays, Shield, User as UserIcon } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface UserInfoDisplayProps {
  user: User;
}

export function UserInfoDisplay({ user }: UserInfoDisplayProps) {
  // Calculate when the user was created
  const createdAt = user.createdAt ? new Date(user.createdAt) : null;
  const timeAgo = createdAt ? formatDistanceToNow(createdAt, { addSuffix: true }) : "Unknown";

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserIcon className="h-5 w-5 text-primary" />
          User Information
        </CardTitle>
      </CardHeader>
      <CardContent className="grid gap-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Name</p>
            <p>{user.name || "Not set"}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Email</p>
            <p>{user.email || "Not set"}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">ID</p>
            <p className="font-mono text-xs">{user.id}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Role</p>
            <div className="flex items-center">
              <Shield className="mr-2 h-4 w-4 text-primary" />
              <p className="font-medium capitalize">{user.role || "user"}</p>
            </div>
          </div>
          <div className="space-y-1 flex items-center">
            <CalendarDays className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Joined {timeAgo}</span>
          </div>
          {user.premium !== undefined && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Subscription</p>
              <p className="capitalize">{user.premium ? "Premium" : "Free"}</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 