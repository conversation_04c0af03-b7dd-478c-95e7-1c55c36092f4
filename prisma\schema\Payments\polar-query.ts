import { useMutation } from "@tanstack/react-query";
import client from "@/lib/trpc";
import { toast } from "sonner";

// Type for the general checkout response
type PolarCheckoutResponse = {
  checkout_url: string;
  checkout_id: string;
};

// Root endpoint - create general checkout session
export const useCreatePolarCheckout = () => {
  return useMutation<PolarCheckoutResponse, Error>({
    mutationFn: async () => {
      // Access the API using proper method instead of using string indexing
      const response = await fetch('/api/polar');
      
      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to create Polar checkout session");
      }
      
      return await response.json() as PolarCheckoutResponse;
    },
    onSuccess: (data) => {
      // Open the checkout URL in a new tab if available
      if (data.checkout_url) {
        window.open(data.checkout_url, "_blank");
      }
      toast.success("Polar checkout session created successfully");
    },
    onError: (error) => {
      toast.error(`Failed to create Polar checkout session: ${error.message}`);
    },
  });
};

// Create Polar product checkout
export const usePolarProductCheckout = () => {
  return useMutation<PolarCheckoutResponse, Error, { productId: string }>({
    mutationFn: async ({ productId }) => {
      if (!productId) throw new Error("Product ID is required");
      
      const response = await client.api.polar.checkout["$get"]({
        query: { productId }
      });
      
      if (!response.ok) {
        const errorData = await response.json() as { error?: string, details?: string };
        throw new Error(errorData.details || errorData.error || "Failed to create checkout session");
      }
      
      return await response.json() as PolarCheckoutResponse;
    },
    onSuccess: (data) => {
      // Redirect to the checkout URL
      if (data.checkout_url) {
        window.location.href = data.checkout_url;
      } else {
        toast.error("No checkout URL returned from the server");
      }
    },
    onError: (error) => {
      toast.error(`Checkout failed: ${error.message}`);
    },
  });
};

// Webhook event handler (client-side notification)
// This won't directly call the webhook endpoint as it's server-to-server,
// but can be used to display notification of a processed webhook if needed
type WebhookNotificationType = "checkout.completed" | "subscription.created" | 
  "subscription.updated" | "subscription.canceled" | "payment.succeeded";

export type WebhookEventData = {
  eventName: WebhookNotificationType;
  status: number;
  data?: Record<string, unknown>;
};

// This mutation would be used if you need client to trigger some webhook-related action
// e.g., checking status after receiving a webhook notification via WebSocket
export const useHandlePolarWebhookEvent = () => {
  return useMutation<void, Error, { eventType: WebhookNotificationType; data?: Record<string, unknown> }>({
    mutationFn: async ({ eventType }) => {
      // This would normally connect to some client-side endpoint 
      // that provides info about webhook events that were processed
      // For example, fetching subscription status after a webhook was processed
      
      switch (eventType) {
        case "checkout.completed":
          toast.success("Your purchase was completed successfully!");
          break;
        case "subscription.created":
          toast.success("Your subscription has been created successfully!");
          break;
        case "subscription.updated":
          toast.success("Your subscription has been updated!");
          break;
        case "subscription.canceled":
          toast.info("Your subscription has been canceled");
          break;
        case "payment.succeeded":
          toast.success("Payment processed successfully!");
          break;
      }
    },
    onError: (error) => {
      toast.error(`Failed to process event: ${error.message}`);
    },
  });
};