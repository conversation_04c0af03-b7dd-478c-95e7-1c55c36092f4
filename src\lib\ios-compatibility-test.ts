'use client';

/**
 * iOS Compatibility Test Suite
 * Tests various iOS-specific issues that might cause client-side exceptions
 */

import { getDeviceInfo, checkAutoplaySupport, preloadImage } from './ios-safe-video';

export interface CompatibilityTestResult {
  test: string;
  passed: boolean;
  error?: string;
  details?: any;
}

export interface CompatibilityReport {
  deviceInfo: ReturnType<typeof getDeviceInfo>;
  tests: CompatibilityTestResult[];
  overallScore: number;
  recommendations: string[];
}

/**
 * Run comprehensive iOS compatibility tests
 */
export async function runIOSCompatibilityTests(): Promise<CompatibilityReport> {
  const deviceInfo = getDeviceInfo();
  const tests: CompatibilityTestResult[] = [];
  const recommendations: string[] = [];

  // Test 1: localStorage availability
  tests.push(await testLocalStorage());

  // Test 2: Video element creation
  tests.push(await testVideoElementCreation());

  // Test 3: Image preloading
  tests.push(await testImagePreloading());

  // Test 4: requestAnimationFrame availability
  tests.push(testRequestAnimationFrame());

  // Test 5: Autoplay support
  tests.push(await testAutoplaySupport());

  // Test 6: Touch event support
  tests.push(testTouchEventSupport());

  // Test 7: CSS features
  tests.push(testCSSFeatures());

  // Test 8: Memory management
  tests.push(testMemoryManagement());

  // Test 9: Notification API
  tests.push(testNotificationAPI());

  // Calculate overall score
  const passedTests = tests.filter(t => t.passed).length;
  const overallScore = Math.round((passedTests / tests.length) * 100);

  // Generate recommendations based on failed tests
  tests.forEach(test => {
    if (!test.passed) {
      switch (test.test) {
        case 'localStorage':
          recommendations.push('Enable non-private browsing mode for better experience');
          break;
        case 'autoplay':
          recommendations.push('Videos will be muted by default due to autoplay restrictions');
          break;
        case 'video-creation':
          recommendations.push('Video playback may be limited on this device');
          break;
        case 'image-preloading':
          recommendations.push('Image loading may be slower than expected');
          break;
        case 'notification-api':
          recommendations.push('Browser notifications are not available - only sound alerts will work');
          break;
      }
    }
  });

  if (deviceInfo.isIOS) {
    recommendations.push('iOS device detected - using iOS-optimized video loading');
  }

  return {
    deviceInfo,
    tests,
    overallScore,
    recommendations
  };
}

async function testLocalStorage(): Promise<CompatibilityTestResult> {
  try {
    if (typeof localStorage === 'undefined') {
      return {
        test: 'localStorage',
        passed: false,
        error: 'localStorage not available'
      };
    }

    // Test write/read
    const testKey = 'ios-compat-test';
    const testValue = 'test-value';
    
    localStorage.setItem(testKey, testValue);
    const retrieved = localStorage.getItem(testKey);
    localStorage.removeItem(testKey);

    return {
      test: 'localStorage',
      passed: retrieved === testValue,
      details: { canWrite: true, canRead: true }
    };
  } catch (error) {
    return {
      test: 'localStorage',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

async function testVideoElementCreation(): Promise<CompatibilityTestResult> {
  try {
    const video = document.createElement('video');
    video.playsInline = true;
    video.muted = true;
    video.preload = 'metadata';

    // Test if we can set basic properties
    const canSetSrc = typeof video.src === 'string';
    const canSetMuted = typeof video.muted === 'boolean';
    const canSetPlaysInline = typeof video.playsInline === 'boolean';

    return {
      test: 'video-creation',
      passed: canSetSrc && canSetMuted && canSetPlaysInline,
      details: {
        canSetSrc,
        canSetMuted,
        canSetPlaysInline,
        supportsPlaysInline: 'playsInline' in video
      }
    };
  } catch (error) {
    return {
      test: 'video-creation',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

async function testImagePreloading(): Promise<CompatibilityTestResult> {
  try {
    // Use a small data URL image for testing
    const testImageSrc = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
    
    await preloadImage(testImageSrc);
    
    return {
      test: 'image-preloading',
      passed: true,
      details: { method: 'data-url' }
    };
  } catch (error) {
    return {
      test: 'image-preloading',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

function testRequestAnimationFrame(): CompatibilityTestResult {
  try {
    const hasRAF = typeof requestAnimationFrame !== 'undefined';
    const hasCAF = typeof cancelAnimationFrame !== 'undefined';

    return {
      test: 'requestAnimationFrame',
      passed: hasRAF,
      details: {
        hasRequestAnimationFrame: hasRAF,
        hasCancelAnimationFrame: hasCAF
      }
    };
  } catch (error) {
    return {
      test: 'requestAnimationFrame',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

async function testAutoplaySupport(): Promise<CompatibilityTestResult> {
  try {
    const supportsAutoplay = await checkAutoplaySupport();
    
    return {
      test: 'autoplay',
      passed: supportsAutoplay,
      details: { supportsAutoplay }
    };
  } catch (error) {
    return {
      test: 'autoplay',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

function testTouchEventSupport(): CompatibilityTestResult {
  try {
    const hasTouchEvents = 'ontouchstart' in window;
    const hasPointerEvents = 'onpointerdown' in window;
    const hasTouch = 'TouchEvent' in window;

    return {
      test: 'touch-events',
      passed: hasTouchEvents || hasPointerEvents,
      details: {
        hasTouchEvents,
        hasPointerEvents,
        hasTouch,
        maxTouchPoints: navigator.maxTouchPoints || 0
      }
    };
  } catch (error) {
    return {
      test: 'touch-events',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

function testCSSFeatures(): CompatibilityTestResult {
  try {
    const testElement = document.createElement('div');
    
    // Test CSS features that might cause issues on iOS
    const supportsWillChange = 'willChange' in testElement.style;
    const supportsTransform = 'transform' in testElement.style;
    const supportsBackdropFilter = 'backdropFilter' in testElement.style || 'webkitBackdropFilter' in testElement.style;

    return {
      test: 'css-features',
      passed: supportsTransform, // Transform is essential
      details: {
        supportsWillChange,
        supportsTransform,
        supportsBackdropFilter
      }
    };
  } catch (error) {
    return {
      test: 'css-features',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

function testMemoryManagement(): CompatibilityTestResult {
  try {
    // Test if we can create and clean up multiple elements
    const elements: HTMLElement[] = [];

    for (let i = 0; i < 10; i++) {
      const div = document.createElement('div');
      elements.push(div);
    }

    // Clean up
    elements.length = 0;

    return {
      test: 'memory-management',
      passed: true,
      details: { elementsCreated: 10 }
    };
  } catch (error) {
    return {
      test: 'memory-management',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

function testNotificationAPI(): CompatibilityTestResult {
  try {
    const hasNotificationAPI = typeof window !== 'undefined' && 'Notification' in window;

    if (!hasNotificationAPI) {
      return {
        test: 'notification-api',
        passed: false,
        error: 'Notification API not available',
        details: { hasNotificationAPI: false }
      };
    }

    // Test if we can access Notification.permission without throwing
    let permissionAccessible = false;
    let currentPermission: NotificationPermission = 'default';

    try {
      currentPermission = Notification.permission;
      permissionAccessible = true;
    } catch (error) {
      console.warn('Cannot access Notification.permission:', error);
    }

    return {
      test: 'notification-api',
      passed: permissionAccessible,
      details: {
        hasNotificationAPI,
        permissionAccessible,
        currentPermission,
        canRequestPermission: typeof Notification.requestPermission === 'function'
      }
    };
  } catch (error) {
    return {
      test: 'notification-api',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Log compatibility report to console
 */
export function logCompatibilityReport(report: CompatibilityReport): void {
  console.group('🔍 iOS Compatibility Report');
  
  console.log('📱 Device Info:', report.deviceInfo);
  console.log(`📊 Overall Score: ${report.overallScore}%`);
  
  console.group('🧪 Test Results:');
  report.tests.forEach(test => {
    const icon = test.passed ? '✅' : '❌';
    console.log(`${icon} ${test.test}:`, test.passed ? 'PASSED' : `FAILED - ${test.error}`);
    if (test.details) {
      console.log('   Details:', test.details);
    }
  });
  console.groupEnd();
  
  if (report.recommendations.length > 0) {
    console.group('💡 Recommendations:');
    report.recommendations.forEach(rec => console.log(`• ${rec}`));
    console.groupEnd();
  }
  
  console.groupEnd();
}
