'use client';

import { useState, useEffect } from 'react';
import { Bell, Volume2, <PERSON>otateCcw, PlayCircle } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { <PERSON>lider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import {
  useNotificationStore,
  SoundType
} from '@/lib/notification-store';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

export const NotificationSettings = () => {
  const {
    settings,
    setSoundType,
    setVolume,
    setRepeat,
    setEnabled,
    playTestSound,
    preloadSound
  } = useNotificationStore();

  // For input validation
  const [repeatInput, setRepeatInput] = useState<string>(settings.repeat.toString());

  // Update input field when settings change
  useEffect(() => {
    setRepeatInput(settings.repeat.toString());
  }, [settings.repeat]);

  // Preload sound when component mounts
  useEffect(() => {
    if (settings.enabled) {
      preloadSound();
    }
  }, [preloadSound, settings.enabled]);

  // Preload sound when sound type changes
  useEffect(() => {
    if (settings.enabled) {
      preloadSound();
    }
  }, [settings.soundType, preloadSound, settings.enabled]);

  // Handle repeat input change
  const handleRepeatChange = (value: string) => {
    setRepeatInput(value);

    // Convert to number and validate
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue) && numValue >= 1 && numValue <= 10) {
      setRepeat(numValue);
    }
  };

  // Handle sound type change with automatic playback
  const handleSoundTypeChange = (value: string) => {
    const soundType = value as SoundType;

    // Set the new sound type
    setSoundType(soundType);

    // Only auto-play if notifications are enabled
    if (settings.enabled) {
      // Play the sound after a brief delay to ensure it's loaded
      setTimeout(() => {
        playTestSound();
      }, 100);
    }
  };

  // Sound options for the select dropdown
  const soundOptions: {value: SoundType; label: string}[] = [
    { value: 'bell', label: 'Bell' },
    { value: 'check', label: 'Check' },
    { value: 'elevator', label: 'Elevator' },
    { value: 'funny', label: 'Funny' },
    { value: 'level', label: 'Level Up' },
    { value: 'ping', label: 'Ping' },
    { value: 'positive', label: 'Positive' },
    { value: 'simple', label: 'Simple' }
  ];

  return (
    <div className="space-y-4">
      {/* Enable Notifications Toggle */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Bell className="h-3.5 w-3.5 text-primary/70" />
          <Label htmlFor="notifications-enabled" className="text-xs font-medium">
            Enable Notifications
          </Label>
        </div>
        <Switch
          id="notifications-enabled"
          checked={settings.enabled}
          onCheckedChange={setEnabled}
          className="data-[state=checked]:bg-primary/70"
        />
      </div>

      {/* Sound Type Selection */}
      <div className="space-y-1.5">
        <Label className="text-xs font-medium text-foreground">
          Notification Sound
        </Label>
        <div className="flex items-center gap-2">
          <Select
            value={settings.soundType}
            onValueChange={handleSoundTypeChange}
            disabled={!settings.enabled}
          >
            <SelectTrigger className="h-8 text-xs bg-muted/50 border-border flex-1">
              <SelectValue placeholder="Select a sound" />
            </SelectTrigger>
            <SelectContent className="max-h-60">
              {soundOptions.map((option) => (
                <SelectItem
                  key={option.value}
                  value={option.value}
                  className="text-xs"
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 bg-muted/50"
            onClick={playTestSound}
            disabled={!settings.enabled}
            title="Play test sound"
          >
            <PlayCircle className="h-3.5 w-3.5 text-primary/80" />
          </Button>
        </div>
      </div>

      {/* Volume Control */}
      <div className="space-y-1.5">
        <div className="flex items-center justify-between">
          <Label className="text-xs font-medium flex items-center gap-1.5">
            <Volume2 className="h-3 w-3 text-primary/70" />
            <span>Volume</span>
          </Label>
          <div className="text-xs text-muted-foreground font-medium">
            {settings.volume}
          </div>
        </div>
        <Slider
          value={[settings.volume]}
          min={0}
          max={100}
          step={1}
          disabled={!settings.enabled}
          onValueChange={([value]) => setVolume(value)}
          className="py-1"
        />
      </div>

      {/* Repeat Count */}
      <div className="space-y-1.5">
        <Label className="text-xs font-medium text-foreground flex items-center gap-1.5">
          <RotateCcw className="h-3 w-3 text-primary/70" />
          <span>Repeat Count (1-10)</span>
        </Label>
        <div className="flex items-center gap-2">
          <Input
            type="number"
            min={1}
            max={10}
            value={repeatInput}
            onChange={(e) => handleRepeatChange(e.target.value)}
            disabled={!settings.enabled}
            className="h-8 text-xs bg-muted/50 border-border"
          />
        </div>
      </div>
    </div>
  );
};