'use client';

import { useCallback, useState, useRef, useEffect } from 'react';

interface TimerInteractionManagerProps {
  showControls: boolean;
  setShowControls: (show: boolean) => void;
  isDragging: boolean;
  isResizing: boolean;
  showHandles: () => void;
  hideHandles: () => void;
  timerRef: React.RefObject<HTMLDivElement | null>;
  children: (handlers: InteractionHandlers) => React.ReactNode;
}

export interface InteractionHandlers {
  handleMouseEnter: (e: React.MouseEvent) => void;
  handleMouseLeave: (e: React.MouseEvent) => void;
  handleTouchStartControls: (e: React.TouchEvent) => void;
  handleTouchEnd: (e: React.TouchEvent) => void;
  updateControlVisibility: (shouldShow: boolean, forceUpdate?: boolean) => void;
}

export function TimerInteractionManager({
  showControls,
  setShowControls,
  isDragging,
  isResizing,
  showHandles,
  hideHandles,
  timerRef,
  children
}: TimerInteractionManagerProps) {
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isHoveringRef = useRef(false);
  const [isMobile, setIsMobile] = useState(false);
  const [touchStartTime, setTouchStartTime] = useState(0);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
                           window.innerWidth <= 768;
      setIsMobile(isMobileDevice);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Memoized function to update control visibility
  const updateControlVisibility = useCallback((shouldShow: boolean, forceUpdate = false) => {
    if (shouldShow === showControls && !forceUpdate) return; // No change needed unless forced

    // Clean up existing timeout if any
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }

    if (shouldShow) {
      setShowControls(true);
      showHandles();
    } else {
      // Only hide controls if we're not dragging or resizing, or if force update
      if (forceUpdate || (!isDragging && !isResizing)) {
        setShowControls(false);
        hideHandles();
      }
    }
  }, [showControls, showHandles, hideHandles, isDragging, isResizing, setShowControls]);

  // Handle mouse enter with debounced approach
  const handleMouseEnter = useCallback(() => {
    isHoveringRef.current = true;

    // Skip if already showing controls
    if (showControls) return;

    // Add a small delay to prevent flashes during quick mouse movements
    hoverTimeoutRef.current = setTimeout(() => {
      if (isHoveringRef.current && timerRef.current?.matches(':hover')) {
        updateControlVisibility(true);
      }
    }, 40);
  }, [showControls, updateControlVisibility, timerRef]);

  const handleMouseLeave = useCallback((e: React.MouseEvent) => {
    isHoveringRef.current = false;

    // Don't hide controls if we're dragging or resizing
    if (isDragging || isResizing) return;

    // Skip if already hiding controls
    if (!showControls) return;

    // Get the event related target (what we're moving to)
    const relatedTarget = e.relatedTarget;

    // Check if we're moving to a child element or outside the timer
    if (relatedTarget instanceof Node && timerRef.current?.contains(relatedTarget)) {
      // We're still inside the timer component, don't hide controls
      return;
    }

    // Add a short delay to prevent flickering when moving near edges
    hoverTimeoutRef.current = setTimeout(() => {
      // Double-check we're not hovering before hiding
      if (!isHoveringRef.current && timerRef.current && !timerRef.current.matches(':hover')) {
        updateControlVisibility(false);
      }
    }, 100);
  }, [isDragging, isResizing, showControls, updateControlVisibility, timerRef]);

  // Enhanced touch start handler to prevent page refresh and improve controls
  const handleTouchStartControls = useCallback((e: React.TouchEvent) => {
    const currentTime = Date.now();
    setTouchStartTime(currentTime);

    // Check if this is a drag start by looking for button/resize handle targets
    const target = e.target as HTMLElement;
    const isDragTarget = !target.closest('button') && !target.closest('[data-resize-handle]');
    
    if (isDragTarget) {
      // Add timer-interacting class to enable strict touch restrictions
      if (timerRef.current) {
        timerRef.current.classList.add('timer-interacting');
      }
      
      // For potential drag operations, prevent default to avoid page refresh
      // This specifically prevents pull-to-refresh when dragging from top
      e.preventDefault();
    }

    // Handle control visibility
    if (!showControls) {
      // Show controls immediately on touch
      updateControlVisibility(true);

      // Clear any existing timeout
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }

      // Set timeout to hide controls after touch interaction
      hoverTimeoutRef.current = setTimeout(() => {
        if (!isDragging && !isResizing) {
          updateControlVisibility(false);
        }
      }, 4000);
    }
  }, [showControls, updateControlVisibility, isDragging, isResizing, timerRef]);

  // Add touch end handler to ensure proper control hiding
  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    const touchDuration = Date.now() - touchStartTime;
    
    // Remove timer-interacting class when touch ends
    if (timerRef.current) {
      timerRef.current.classList.remove('timer-interacting');
    }
    
    // If it was a quick tap (not a drag), handle control visibility
    if (touchDuration < 300 && !isDragging && !isResizing) {
      const target = e.target as HTMLElement;
      
      // Don't hide controls if touching buttons or resize handles
      if (!target.closest('button') && !target.closest('[data-resize-handle]')) {
        // Clear existing timeout
        if (hoverTimeoutRef.current) {
          clearTimeout(hoverTimeoutRef.current);
        }

        // Set new timeout to hide controls
        hoverTimeoutRef.current = setTimeout(() => {
          if (!isDragging && !isResizing) {
            updateControlVisibility(false);
          }
        }, 3000);
      }
    }
  }, [touchStartTime, isDragging, isResizing, updateControlVisibility, timerRef]);

  // Global touch event handler for mobile to hide controls when touching outside
  useEffect(() => {
    if (!isMobile) return;

    const handleGlobalTouch = (e: TouchEvent) => {
      // Only handle if controls are currently visible
      if (!showControls || isDragging || isResizing) return;

      const target = e.target as HTMLElement;
      
      // Check if touch is outside timer component
      if (timerRef.current && !timerRef.current.contains(target)) {
        // Clear any existing timeout
        if (hoverTimeoutRef.current) {
          clearTimeout(hoverTimeoutRef.current);
        }

        // Hide controls immediately when touching outside
        updateControlVisibility(false);
      }
    };

    // Add passive listener for better performance
    document.addEventListener('touchstart', handleGlobalTouch, { passive: true });

    return () => {
      document.removeEventListener('touchstart', handleGlobalTouch);
    };
  }, [isMobile, showControls, isDragging, isResizing, updateControlVisibility, timerRef]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  const handlers: InteractionHandlers = {
    handleMouseEnter,
    handleMouseLeave,
    handleTouchStartControls,
    handleTouchEnd,
    updateControlVisibility
  };

  return <>{children(handlers)}</>;
}
