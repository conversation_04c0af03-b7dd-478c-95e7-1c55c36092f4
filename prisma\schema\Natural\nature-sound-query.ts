import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { InferRequestType, InferResponseType } from "hono";
import client from "@/lib/trpc";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

// Get all nature sounds
export type GetNatureSounds_ResponseType = InferResponseType<
  (typeof client.api.natureSounds)["$get"],
  200
>;

export type GetNatureSounds_ResponseTypeSuccess = Extract<
  GetNatureSounds_ResponseType,
  { data: object }
>["data"];

export const useGetNatureSounds = (filters?: {
  isPublic?: boolean;
  category?: string;
  playlistId?: string;
}) => {
  return useQuery({
    queryKey: ["natureSounds", filters],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      
      if (filters?.isPublic !== undefined) {
        queryParams.append("isPublic", String(filters.isPublic));
      }
      
      if (filters?.category) {
        queryParams.append("category", filters.category);
      }
      
      if (filters?.playlistId) {
        queryParams.append("playlistId", filters.playlistId);
      }
      
      const queryString = queryParams.toString();
      
      let response;
      if (queryString) {
        response = await client.api.natureSounds.$get({
          query: { 
            isPublic: filters?.isPublic !== undefined ? String(filters.isPublic) : undefined,
            category: filters?.category,
            playlistId: filters?.playlistId
          }
        });
      } else {
        response = await client.api.natureSounds.$get();
      }
      
      if (!response.ok) {
        throw new Error("Failed to fetch nature sounds");
      }
      const { data } = await response.json();
      return data;
    },
  });
};

// Get single nature sound
type GetNatureSound_ResponseType = InferResponseType<
  (typeof client.api.natureSounds)[":id"]["$get"],
  200
>;

export type GetNatureSound_ResponseTypeSuccess = Extract<
  GetNatureSound_ResponseType,
  { data: object }
>["data"];

export const useGetNatureSound = (id?: string) => {
  return useQuery({
    queryKey: ["natureSounds", { id }],
    queryFn: async () => {
      if (!id) throw new Error("No nature sound ID provided");

      const response = await client.api.natureSounds[":id"]["$get"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch nature sound");
      }

      const { data } = await response.json();
      return data;
    },
    enabled: !!id,
  });
};

// Create nature sound
interface CreateNatureSoundSuccessResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

type CreateNatureSoundRequest = InferRequestType<
  (typeof client.api.natureSounds)["$post"]
>;

export const useCreateNatureSound = () => {
  const router = useRouter();
  const queryClient = useQueryClient();

  const mutation = useMutation<
    CreateNatureSoundSuccessResponse,
    Error,
    CreateNatureSoundRequest
  >({
    mutationFn: async ({ form }) => {
      const response = await client.api.natureSounds.$post({ form });

      if (!response.ok) {
        throw new Error("Failed to create nature sound");
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Nature sound created successfully");
      router.refresh();
      queryClient.invalidateQueries({ queryKey: ["natureSounds"] });
    },
    onError: (error) => {
      toast.error(`Failed to create nature sound: ${error.message}`);
    },
  });

  return mutation;
};

// Update nature sound
type UpdateNatureSound_ResponseType = InferResponseType<
  (typeof client.api.natureSounds)[":id"]["$patch"],
  200
>;

export type UpdateNatureSound_ResponseTypeSuccess = Extract<
  UpdateNatureSound_ResponseType,
  { data: object }
>["data"];

type UpdateNatureSoundRequest = InferRequestType<
  (typeof client.api.natureSounds)[":id"]["$patch"]
>;

export const useUpdateNatureSound = () => {
  const queryClient = useQueryClient();
  
  return useMutation<
    UpdateNatureSound_ResponseType,
    Error,
    UpdateNatureSoundRequest
  >({
    mutationFn: async (variables) => {
      const { form, param } = variables;

      if (!param?.id) {
        throw new Error("No nature sound ID provided");
      }

      const response = await client.api.natureSounds[":id"]["$patch"]({
        form,
        param: { id: param.id },
      });

      if (!response.ok) {
        throw new Error(`Failed to update nature sound. Status: ${response.status}`);
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Nature sound updated successfully");
      const natureSoundId = data?.id;
      if (natureSoundId) {
        queryClient.invalidateQueries({ queryKey: ["natureSounds", { id: natureSoundId }] });
      }
      queryClient.invalidateQueries({ queryKey: ["natureSounds"] });
    },
    onError: (error) => {
      toast.error(`Failed to update nature sound: ${error.message}`);
    },
  });
};

// Delete nature sound
type DeleteNatureSound_ResponseType = InferResponseType<
  (typeof client.api.natureSounds)[":id"]["$delete"],
  200
>;

export const useDeleteNatureSound = () => {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation<
    DeleteNatureSound_ResponseType,
    Error,
    { id: string }
  >({
    mutationFn: async ({ id }) => {
      if (!id) {
        throw new Error("No nature sound ID provided");
      }

      const response = await client.api.natureSounds[":id"]["$delete"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to delete nature sound");
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Nature sound deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["natureSounds"] });
      router.push("/nature-sounds");
    },
    onError: (error) => {
      toast.error(`Failed to delete nature sound: ${error.message}`);
    },
  });
}; 