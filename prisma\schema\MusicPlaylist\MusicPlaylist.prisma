// MusicPlaylist.prisma
model MusicPlaylist {
    id          String   @id @default(cuid())
    name        String ///  @zod.custom.use(z.string().min(1))
    description String?  @db.Text
    imageUrl    String? /// @zod.custom.use(z.union([z.instanceof(File), z.string().nullable()]))
    isPublic    Boolean  @default(false)
    isDefault   Boolean  @default(false) // Indicates if this is a default admin-created playlist
    creatorType UserRole @default(USER) // To distinguish admin vs user created content

    // User who created this playlist (optional)
    userId String
    user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

    // Relationship with music tracks (many-to-many)
    genres MusicGenre[]

    musics Music[]

    // Relationship with videos
    videos Video[]

    // Store the order of tracks in the playlist
    musicOrder String[]

    createdAt DateTime @default(now()) /// @zod.custom.use(z.union([z.date(), z.string().datetime()]))
    updatedAt DateTime @updatedAt /// @zod.custom.use(z.union([z.date(), z.string().datetime()]))

    @@index([userId])
    @@index([isPublic])
    @@index([isDefault])
    @@index([creatorType])
}
