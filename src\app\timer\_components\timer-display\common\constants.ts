import { TimerColorPreset } from '@/lib/pomodoro-store';
import { usePomodoroStore } from '@/lib/pomodoro-store';

// Position classes for the timer
export const positionClasses = {
  'top-left': 'top-4 left-4',
  'top-right': 'top-4 right-4',
  'bottom-left': 'bottom-4 left-4',
  'bottom-right': 'bottom-4 right-4',
  'center': 'top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2',
  'top-center': 'top-4 left-1/2 -translate-x-1/2',
  'bottom-center': 'bottom-4 left-1/2 -translate-x-1/2',
  'middle-left': 'top-1/2 left-4 -translate-y-1/2',
  'middle-right': 'top-1/2 right-4 -translate-y-1/2',
  
  // Simplified bottom positions (5 positions)
  'bottom-left-center': 'bottom-4 left-[25%]',
  'bottom-right-center': 'bottom-4 left-[75%]',
};

// Bottom edge padding constant
export const BOTTOM_EDGE_PADDING = 16; // 16px padding from bottom edge

// Enhanced color utility functions for glass compatibility
export const getButtonTextColor = (timerColor: TimerColorPreset) => {
  switch (timerColor) {
    case 'white': return 'text-white';
    case 'blue': return 'text-blue-200'; // Lighter for better glass contrast
    case 'green': return 'text-green-200'; // Lighter for better glass contrast
    case 'yellow': return 'text-amber-200'; // Lighter for better glass contrast
    case 'red': return 'text-red-200'; // Lighter for better glass contrast
    case 'purple': return 'text-purple-200'; // Lighter for better glass contrast
    case 'indigo': return 'text-indigo-200'; // Lighter for better glass contrast
    case 'orange': return 'text-orange-200'; // Lighter for better glass contrast
    case 'pink': return 'text-pink-200'; // Lighter for better glass contrast
    default: return 'text-white';
  }
};

export const getButtonHoverEffect = (timerColor: TimerColorPreset) => {
  switch (timerColor) {
    case 'white': return 'hover:bg-white/15';
    case 'blue': return 'hover:bg-blue-500/15';
    case 'green': return 'hover:bg-green-500/15';
    case 'yellow': return 'hover:bg-amber-400/15';
    case 'red': return 'hover:bg-rose-500/15';
    case 'purple': return 'hover:bg-purple-500/15';
    case 'indigo': return 'hover:bg-indigo-500/15';
    case 'orange': return 'hover:bg-orange-500/15';
    case 'pink': return 'hover:bg-pink-500/15';
    default: return 'hover:bg-white/15';
  }
};

export const getButtonActiveColor = (timerColor: TimerColorPreset, isRunning: boolean) => {
  switch (timerColor) {
    case 'white': return isRunning ? 'bg-white/10' : '';
    case 'blue': return isRunning ? 'bg-blue-500/10' : '';
    case 'green': return isRunning ? 'bg-green-500/10' : '';
    case 'yellow': return isRunning ? 'bg-amber-400/10' : '';
    case 'red': return isRunning ? 'bg-rose-500/10' : '';
    case 'purple': return isRunning ? 'bg-purple-500/10' : '';
    case 'indigo': return isRunning ? 'bg-indigo-500/10' : '';
    case 'orange': return isRunning ? 'bg-orange-500/10' : '';
    case 'pink': return isRunning ? 'bg-pink-500/10' : '';
    default: return isRunning ? 'bg-white/10' : '';
  }
};

export const getProgressBarColor = (timerColor: TimerColorPreset) => {
  const currentPhase = usePomodoroStore.getState().currentPhase;

  // Base on timer color setting first
  switch (timerColor) {
    case 'blue':
      return 'bg-gradient-to-r from-blue-500 to-blue-400';
    case 'green':
      return 'bg-gradient-to-r from-emerald-500 to-emerald-400';
    case 'yellow':
      return 'bg-gradient-to-r from-amber-400 to-amber-300';
    case 'red':
      return 'bg-gradient-to-r from-rose-500 to-rose-400';
    case 'purple':
      return 'bg-gradient-to-r from-purple-500 to-purple-400';
    case 'indigo':
      return 'bg-gradient-to-r from-indigo-500 to-indigo-400';
    case 'orange':
      return 'bg-gradient-to-r from-orange-500 to-orange-400';
    case 'pink':
      return 'bg-gradient-to-r from-pink-500 to-pink-400';
    case 'white':
    default:
      // White uses phase-specific colors for better visual feedback
      switch (currentPhase) {
        case 'pomodoro':
          return 'bg-gradient-to-r from-blue-500 to-blue-400';
        case 'shortBreak':
          return 'bg-gradient-to-r from-emerald-500 to-emerald-400';
        case 'longBreak':
          return 'bg-gradient-to-r from-purple-500 to-purple-400';
        default:
          return 'bg-gradient-to-r from-blue-500 to-blue-400';
      }
  }
};

export const getTimerTextColor = (timerColor: TimerColorPreset) => {
  switch (timerColor) {
    case 'blue':
      return 'text-blue-300'; // Lighter shade for better glass contrast
    case 'green':
      return 'text-emerald-300'; // Lighter shade for better glass contrast
    case 'yellow':
      return 'text-amber-300'; // Lighter shade for better glass contrast
    case 'red':
      return 'text-red-300'; // Lighter shade for better glass contrast
    case 'purple':
      return 'text-purple-300'; // Lighter shade for better glass contrast
    case 'indigo':
      return 'text-indigo-300'; // Lighter shade for better glass contrast
    case 'orange':
      return 'text-orange-300'; // Lighter shade for better glass contrast
    case 'pink':
      return 'text-pink-300'; // Lighter shade for better glass contrast
    case 'white':
    default:
      // For white mode, keep countdown text consistently WHITE
      return 'text-white';
  }
};

// Enhanced function for phase label colors - optimized for glass backgrounds
export const getPhaseLabelColor = (timerColor: TimerColorPreset) => {
  switch (timerColor) {
    case 'blue':
      return 'text-blue-300'; // Lighter shade for better glass contrast
    case 'green':
      return 'text-emerald-300'; // Lighter shade for better glass contrast
    case 'yellow':
      return 'text-amber-300'; // Lighter shade for better glass contrast
    case 'red':
      return 'text-red-300'; // Lighter shade for better glass contrast
    case 'purple':
      return 'text-purple-300'; // Lighter shade for better glass contrast
    case 'indigo':
      return 'text-indigo-300'; // Lighter shade for better glass contrast
    case 'orange':
      return 'text-orange-300'; // Lighter shade for better glass contrast
    case 'pink':
      return 'text-pink-300'; // Lighter shade for better glass contrast
    case 'white':
    default:
      // For white mode, use pure white for all labels (monochromatic glass-friendly)
      return 'text-white';
  }
};

export const getTimerTextColorValue = (timerColor: TimerColorPreset) => {
  // Full opacity (1.0) for optimal readability on glass backgrounds
  switch (timerColor) {
    case 'blue': return 'rgba(96, 165, 250, 1.0)'; // blue-400 with enhanced brightness
    case 'green': return 'rgba(52, 211, 153, 1.0)'; // emerald-400 with enhanced brightness
    case 'yellow': return 'rgba(251, 191, 36, 1.0)'; // amber-400
    case 'red': return 'rgba(248, 113, 113, 1.0)'; // red-400 with enhanced brightness
    case 'purple': return 'rgba(196, 181, 253, 1.0)'; // purple-300 for better glass contrast
    case 'indigo': return 'rgba(129, 140, 248, 1.0)'; // indigo-400 with enhanced brightness
    case 'orange': return 'rgba(251, 146, 60, 1.0)'; // orange-400
    case 'pink': return 'rgba(244, 114, 182, 1.0)'; // pink-400
    case 'white':
    default:
      // For white mode, keep countdown text consistently WHITE with full opacity
      return 'rgba(255, 255, 255, 1.0)'; // pure white for glass compatibility
  }
};

export const getTimerTextShadow = (timerColor: TimerColorPreset) => {
  const isRunning = usePomodoroStore.getState().isRunning;

  // Enhanced base shadow for glass backgrounds - stronger contrast
  const baseShadow = '0 2px 4px rgba(0, 0, 0, 0.4), 0 1px 2px rgba(0, 0, 0, 0.3)';

  // Enhanced shadow based on state and color for glass compatibility
  if (isRunning) {
    switch (timerColor) {
      case 'blue':
        return `${baseShadow}, 0 0 12px rgba(96, 165, 250, 0.4), 0 0 6px rgba(59, 130, 246, 0.2)`;
      case 'green':
        return `${baseShadow}, 0 0 12px rgba(52, 211, 153, 0.4), 0 0 6px rgba(16, 185, 129, 0.2)`;
      case 'yellow':
        return `${baseShadow}, 0 0 12px rgba(251, 191, 36, 0.4), 0 0 6px rgba(245, 158, 11, 0.2)`;
      case 'red':
        return `${baseShadow}, 0 0 12px rgba(248, 113, 113, 0.4), 0 0 6px rgba(239, 68, 68, 0.2)`;
      case 'purple':
        return `${baseShadow}, 0 0 12px rgba(196, 181, 253, 0.4), 0 0 6px rgba(168, 85, 247, 0.2)`;
      case 'indigo':
        return `${baseShadow}, 0 0 12px rgba(129, 140, 248, 0.4), 0 0 6px rgba(99, 102, 241, 0.2)`;
      case 'orange':
        return `${baseShadow}, 0 0 12px rgba(251, 146, 60, 0.4), 0 0 6px rgba(249, 115, 22, 0.2)`;
      case 'pink':
        return `${baseShadow}, 0 0 12px rgba(244, 114, 182, 0.4), 0 0 6px rgba(236, 72, 153, 0.2)`;
      case 'white':
      default:
        // For white mode, enhanced white shadow for glass backgrounds
        return `${baseShadow}, 0 0 12px rgba(255, 255, 255, 0.3), 0 0 6px rgba(255, 255, 255, 0.15)`;
    }
  }

  // Default enhanced shadow for glass compatibility
  return baseShadow;
};

export const getBackgroundOpacity = (timerOpacity: number) => {
  // Return the exact opacity value set by user
  // No minimum threshold - allow full transparency if user wants it
  return timerOpacity;
};

export function getPhaseGradient() {
  const currentPhase = usePomodoroStore.getState().currentPhase;
  
  switch (currentPhase) {
    case 'pomodoro':
      return 'linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.06))';
    case 'shortBreak':
      return 'linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(5, 150, 105, 0.06))';
    case 'longBreak':
      return 'linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(124, 58, 237, 0.06))';
    default:
      return 'linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.06))';
  }
} 