import { But<PERSON> } from "@/components/ui/button";
import { TimerSizePreset } from "./use-timer-resize";
import { cn } from "@/lib/utils";
import { Minus, Square, Maximize2, CheckCircle2 } from "lucide-react";

interface SizePresetsProps {
  onApplyPreset: (preset: TimerSizePreset) => void;
  currentSize: TimerSizePreset | null;
}

export function SizePresets({ onApplyPreset, currentSize = 'small' }: SizePresetsProps) {
  // Helper to determine current preset based on size
  const presets: Array<{ id: TimerSizePreset; label: string; icon: React.ReactNode }> = [
    { 
      id: 'small', 
      label: 'Small', 
      icon: <Minus className="h-3 w-3 text-primary/70" />
    },
    { 
      id: 'medium', 
      label: 'Medium', 
      icon: <Square className="h-3 w-3 text-primary/70" />
    },
    { 
      id: 'large', 
      label: 'Large', 
      icon: <Maximize2 className="h-3 w-3 text-primary/70" />
    },
  ];

  return (
    <div className="grid grid-cols-3 gap-1.5">
      {presets.map((preset) => (
        <Button
          key={preset.id}
          variant={currentSize === preset.id ? "default" : "outline"}
          className={cn(
            "h-[45px] py-1.5 text-[10px] justify-center rounded-md transition-all flex-col relative",
            currentSize === preset.id 
              ? "border-primary/30 bg-primary/10 text-primary shadow-sm hover:bg-primary/20 hover:text-primary hover:border-primary/40" 
              : "bg-background border-border/60 hover:bg-muted/50 hover:text-foreground hover:border-border"
          )}
          onClick={() => onApplyPreset(preset.id)}
          title={`Set timer to ${preset.label} size`}
        >
          <div className="flex flex-col items-center gap-0.5">
            {preset.icon}
            <span className="font-medium">{preset.label}</span>
          </div>
          {currentSize === preset.id && (
            <div 
              className="absolute -top-1 -right-1 text-primary"
            >
              <CheckCircle2 className="h-3 w-3 fill-primary/20" />
            </div>
          )}
        </Button>
      ))}
    </div>
  );
} 