'use client';

import { spotifyAuth } from './spotify-auth';

// Event listener type
type EventListener = (...args: any[]) => void;

// Declare Spotify global type
declare global {
  interface Window {
    onSpotifyWebPlaybackSDKReady: () => void;
    Spotify: {
      Player: new (options: SpotifyPlayerOptions) => SpotifyPlayer;
    };
  }
}

export interface SpotifyPlayerOptions {
  name: string;
  getOAuthToken: (cb: (token: string) => void) => void;
  volume?: number;
}

export interface SpotifyPlayerState {
  context: {
    uri: string;
    metadata: Record<string, any>;
  };
  disallows: {
    pausing: boolean;
    peeking_next: boolean;
    peeking_prev: boolean;
    resuming: boolean;
    seeking: boolean;
    skipping_next: boolean;
    skipping_prev: boolean;
  };
  paused: boolean;
  position: number;
  repeat_mode: number;
  shuffle: boolean;
  track_window: {
    current_track: SpotifyTrack;
    next_tracks: SpotifyTrack[];
    previous_tracks: SpotifyTrack[];
  };
}

export interface SpotifyTrack {
  album: {
    images: Array<{ url: string; width: number; height: number }>;
    name: string;
    uri: string;
  };
  artists: Array<{ name: string; uri: string }>;
  duration_ms: number;
  id: string;
  is_playable: boolean;
  name: string;
  track_number: number;
  type: string;
  uri: string;
}

export interface SpotifyPlayer {
  addListener: (event: string, cb: (data: any) => void) => void;
  removeListener: (event: string, cb?: (data: any) => void) => void;
  connect: () => Promise<boolean>;
  disconnect: () => void;
  getCurrentState: () => Promise<SpotifyPlayerState | null>;
  setName: (name: string) => Promise<void>;
  getVolume: () => Promise<number>;
  setVolume: (volume: number) => Promise<void>;
  pause: () => Promise<void>;
  resume: () => Promise<void>;
  togglePlay: () => Promise<void>;
  seek: (position_ms: number) => Promise<void>;
  previousTrack: () => Promise<void>;
  nextTrack: () => Promise<void>;
  activateElement: () => Promise<void>;
}

export class SpotifySDK {
  private player: SpotifyPlayer | null = null;
  private deviceId: string | null = null;
  private isSDKReady: boolean = false;
  private isPlayerReady: boolean = false;
  private listeners: Map<string, Set<EventListener>> = new Map();

  constructor() {
    this.initializeSDK();
  }

  private async initializeSDK(): Promise<void> {
    // Load Spotify SDK script if not already loaded
    if (!window.Spotify) {
      await this.loadSpotifySDK();
    }

    // Set up the ready callback
    window.onSpotifyWebPlaybackSDKReady = () => {
      this.isSDKReady = true;
      this.emit('sdk-ready');
    };

    // If SDK is already loaded, trigger ready immediately
    if (window.Spotify && !this.isSDKReady) {
      this.isSDKReady = true;
      this.emit('sdk-ready');
    }
  }

  private loadSpotifySDK(): Promise<void> {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://sdk.scdn.co/spotify-player.js';
      script.async = true;
      
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Spotify SDK'));
      
      document.body.appendChild(script);
    });
  }

  public async initializePlayer(playerName: string = 'Pomodoro 365 Player'): Promise<void> {
    if (!this.isSDKReady) {
      await new Promise<void>((resolve) => {
        this.once('sdk-ready', () => resolve());
      });
    }

    if (!spotifyAuth.isAuthenticated()) {
      throw new Error('User not authenticated with Spotify');
    }

    this.player = new window.Spotify.Player({
      name: playerName,
      getOAuthToken: async (cb) => {
        try {
          const token = await spotifyAuth.getValidAccessToken();
          cb(token);
        } catch (error) {
          console.error('Failed to get access token:', error);
          this.emit('error', { type: 'authentication_error', message: 'Failed to get access token' });
        }
      },
      volume: 0.5,
    });

    this.setupPlayerListeners();
    
    const connected = await this.player.connect();
    if (!connected) {
      throw new Error('Failed to connect Spotify player');
    }
  }

  private setupPlayerListeners(): void {
    if (!this.player) return;

    this.player.addListener('ready', ({ device_id }) => {
      console.log('Spotify player ready with Device ID:', device_id);
      this.deviceId = device_id;
      this.isPlayerReady = true;
      this.emit('ready', { device_id });
    });

    this.player.addListener('not_ready', ({ device_id }) => {
      console.log('Spotify player not ready, Device ID:', device_id);
      this.isPlayerReady = false;
      this.emit('not-ready', { device_id });
    });

    this.player.addListener('player_state_changed', (state) => {
      console.log('Player state changed:', state);
      this.emit('state-change', state);
    });

    this.player.addListener('initialization_error', ({ message }) => {
      console.error('Spotify initialization error:', message);
      this.emit('error', { type: 'initialization_error', message });
    });

    this.player.addListener('authentication_error', ({ message }) => {
      console.error('Spotify authentication error:', message);
      this.emit('error', { type: 'authentication_error', message });
    });

    this.player.addListener('account_error', ({ message }) => {
      console.error('Spotify account error:', message);
      this.emit('error', { type: 'account_error', message });
    });

    this.player.addListener('playback_error', ({ message }) => {
      console.error('Spotify playback error:', message);
      this.emit('error', { type: 'playback_error', message });
    });
  }

  // Play a track, album, or playlist by URI
  public async playContent(spotifyUri: string, position_ms: number = 0): Promise<void> {
    if (!this.isPlayerReady || !this.deviceId) {
      throw new Error('Player not ready');
    }

    try {
      const token = await spotifyAuth.getValidAccessToken();
      
      const body: any = {
        device_id: this.deviceId,
        position_ms,
      };

      // Determine if it's a track or context (album/playlist)
      if (spotifyUri.includes(':track:')) {
        body.uris = [spotifyUri];
      } else {
        body.context_uri = spotifyUri;
      }

      const response = await fetch('https://api.spotify.com/v1/me/player/play', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to play content: ${errorData.error?.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error playing content:', error);
      throw error;
    }
  }

  // Transfer playback to this device
  public async transferPlayback(): Promise<void> {
    if (!this.deviceId) {
      throw new Error('No device ID available');
    }

    try {
      const token = await spotifyAuth.getValidAccessToken();
      
      const response = await fetch('https://api.spotify.com/v1/me/player', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          device_ids: [this.deviceId],
          play: false,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to transfer playback');
      }
    } catch (error) {
      console.error('Error transferring playback:', error);
      throw error;
    }
  }

  // Player controls
  public async play(): Promise<void> {
    if (!this.player) throw new Error('Player not initialized');
    await this.player.resume();
  }

  public async pause(): Promise<void> {
    if (!this.player) throw new Error('Player not initialized');
    await this.player.pause();
  }

  public async togglePlay(): Promise<void> {
    if (!this.player) throw new Error('Player not initialized');
    await this.player.togglePlay();
  }

  public async seek(position_ms: number): Promise<void> {
    if (!this.player) throw new Error('Player not initialized');
    await this.player.seek(position_ms);
  }

  public async previousTrack(): Promise<void> {
    if (!this.player) throw new Error('Player not initialized');
    await this.player.previousTrack();
  }

  public async nextTrack(): Promise<void> {
    if (!this.player) throw new Error('Player not initialized');
    await this.player.nextTrack();
  }

  public async setVolume(volume: number): Promise<void> {
    if (!this.player) throw new Error('Player not initialized');
    await this.player.setVolume(volume);
  }

  public async getCurrentState(): Promise<SpotifyPlayerState | null> {
    if (!this.player) throw new Error('Player not initialized');
    return await this.player.getCurrentState();
  }

  // Event management
  public on(event: string, listener: EventListener): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(listener);
  }

  public off(event: string, listener?: EventListener): void {
    const eventListeners = this.listeners.get(event);
    if (!eventListeners) return;

    if (listener) {
      eventListeners.delete(listener);
    } else {
      eventListeners.clear();
    }
  }

  public once(event: string, listener: EventListener): void {
    const onceWrapper = (...args: any[]) => {
      this.off(event, onceWrapper);
      listener(...args);
    };
    this.on(event, onceWrapper);
  }

  private emit(event: string, ...args: any[]): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => listener(...args));
    }
  }

  // Cleanup
  public disconnect(): void {
    if (this.player) {
      this.player.disconnect();
      this.player = null;
    }
    this.deviceId = null;
    this.isPlayerReady = false;
    this.listeners.clear();
  }

  // Getters
  public get ready(): boolean {
    return this.isPlayerReady;
  }

  public get device(): string | null {
    return this.deviceId;
  }
}

// Singleton instance
let spotifySDK: SpotifySDK | null = null;

export const getSpotifySDK = (): SpotifySDK => {
  if (!spotifySDK) {
    spotifySDK = new SpotifySDK();
  }
  return spotifySDK;
}; 