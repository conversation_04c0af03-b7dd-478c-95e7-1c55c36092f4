// Export Payments-related types for use across the application
export type {
  SubscriptionStatus,
  SubscriptionInterval,
  PaymentStatus,
} from "@prisma/client";

// Manual export of OrderStatus enum since it's new
export enum OrderStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  PAID = 'PAID',
  FAILED = 'FAILED',
  CANCELED = 'CANCELED',
  REFUNDED = 'REFUNDED'
}

// Type definitions for Polar API integrations
export interface PolarCheckoutData {
  id: string;
  customer_id: string;
  price?: {
    id: string;
  };
  status?: string;
}

export interface PolarSubscriptionData {
  id: string;
  customer_id: string;
  price: {
    id: string;
  };
  status: string;
  current_period_start: number;
  current_period_end: number;
  cancel_at_period_end: boolean;
  canceled_at?: number;
  checkout_id?: string;
}

export interface PolarPaymentData {
  id: string;
  subscription_id: string;
  customer_id: string;
  amount: number;
  currency: string;
  checkout_id?: string;
  price?: {
    id: string;
  };
  net_amount?: number;
  discount_amount?: number;
  tax_amount?: number;
  total_amount?: number;
  country?: string;
  customer_metadata?: Record<string, unknown>;
  error?: string;
}

export interface PolarInvoiceData {
  id: string;
  subscription_id: string;
  customer_id: string;
  amount: number;
  currency: string;
  status: string;
  due_date?: number;
  paid_at?: number;
  url?: string;
  items?: Record<string, unknown>;
  tax_rate?: number;
  tax_amount?: number;
  country?: string;
}

export interface PolarOrderData {
  id: string;
  customer_id: string;
  status: string;
  amount: number;
  currency: string;
  checkout_id?: string;
  items?: Array<{
    price: {
      id: string;
    };
    product: {
      id: string;
    };
    quantity?: number;
  }>;
  subscription_id?: string;
  payment_intent_id?: string;
  metadata?: Record<string, unknown>;
  created_at: number;
  updated_at: number;
} 