"use client"

import { Menu } from "lucide-react"
import { usePathname } from "next/navigation"

import { But<PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { cn } from "@/lib/utils"

import { AdminSidebar } from "./admin-sidebar"
import { useSidebar } from "./sidebar-context"

interface AdminHeaderProps {
  className?: string
}

export function AdminHeader({ className }: AdminHeaderProps) {
  const pathname = usePathname()
  const { expandSidebar } = useSidebar()
  
  // Get current page title from path
  const getPageTitle = () => {
    if (pathname === "/admin") return "Dashboard"
    if (pathname.startsWith("/admin/musics")) return "Musics"
    if (pathname.startsWith("/admin/playlists")) return "Playlists"
    if (pathname.startsWith("/admin/videos")) return "Videos"
    return "Admin"
  }

  return (
    <header className={cn("flex h-14 items-center border-b px-4 md:hidden", className)}>
      <Sheet onOpenChange={(open) => open && expandSidebar()}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="md:hidden">
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0 w-[250px]">
          <AdminSidebar />
        </SheetContent>
      </Sheet>
      <h1 className="ml-4 text-xl font-semibold">{getPageTitle()}</h1>
    </header>
  )
} 