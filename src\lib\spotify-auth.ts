// Spotify OAuth configuration
const SPOTIFY_CLIENT_ID = process.env.NEXT_PUBLIC_SPOTIFY_CLIENT_ID;
const SPOTIFY_CLIENT_SECRET = process.env.SPOTIFY_CLIENT_SECRET;
const REDIRECT_URI = process.env.NODE_ENV === 'production' 
  ? `${process.env.NEXTAUTH_URL}/api/spotify/callback`
  : 'http://127.0.0.1:2604//api/spotify/callback';

// Spotify scopes required for Web Playback SDK
const SCOPES = [
  'streaming',
  'user-read-email',
  'user-read-private',
  'user-read-playback-state',
  'user-modify-playback-state',
  'user-read-currently-playing',
  'playlist-read-private',
  'playlist-read-collaborative'
].join(' ');

export interface SpotifyTokens {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  token_type: string;
}

export class SpotifyAuth {
  private static instance: SpotifyAuth;
  private tokens: SpotifyTokens | null = null;

  private constructor() {}

  public static getInstance(): SpotifyAuth {
    if (!SpotifyAuth.instance) {
      SpotifyAuth.instance = new SpotifyAuth();
    }
    return SpotifyAuth.instance;
  }

  // Generate Spotify OAuth URL
  public getAuthUrl(): string {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: SPOTIFY_CLIENT_ID!,
      scope: SCOPES,
      redirect_uri: REDIRECT_URI!,
      state: this.generateRandomString(16),
    });

    return `https://accounts.spotify.com/authorize?${params}`;
  }

  // Exchange authorization code for tokens
  public async exchangeCodeForTokens(code: string): Promise<SpotifyTokens> {
    const response = await fetch('https://accounts.spotify.com/api/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${btoa(`${SPOTIFY_CLIENT_ID}:${SPOTIFY_CLIENT_SECRET}`)}`,
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: REDIRECT_URI!,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to exchange code for tokens');
    }

    const tokens = await response.json();
    this.tokens = tokens;
    this.storeTokens(tokens);
    return tokens;
  }

  // Refresh access token
  public async refreshAccessToken(): Promise<SpotifyTokens> {
    const storedTokens = this.getStoredTokens();
    if (!storedTokens?.refresh_token) {
      throw new Error('No refresh token available');
    }

    const response = await fetch('https://accounts.spotify.com/api/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${btoa(`${SPOTIFY_CLIENT_ID}:${SPOTIFY_CLIENT_SECRET}`)}`,
      },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: storedTokens.refresh_token,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to refresh access token');
    }

    const newTokens = await response.json();
    const updatedTokens = {
      ...newTokens,
      refresh_token: newTokens.refresh_token || storedTokens.refresh_token,
    };

    this.tokens = updatedTokens;
    this.storeTokens(updatedTokens);
    return updatedTokens;
  }

  // Get current valid access token
  public async getValidAccessToken(): Promise<string> {
    const storedTokens = this.getStoredTokens();
    
    if (!storedTokens) {
      throw new Error('No tokens available. Please authenticate first.');
    }

    // Check if token is expired (with 5 minute buffer)
    const expirationTime = parseInt(localStorage.getItem('spotify_token_expiry') || '0');
    const now = Date.now();
    const fiveMinutes = 5 * 60 * 1000;

    if (now >= (expirationTime - fiveMinutes)) {
      try {
        const refreshedTokens = await this.refreshAccessToken();
        return refreshedTokens.access_token;
      } catch (error) {
        console.error('Failed to refresh token:', error);
        this.clearTokens();
        throw new Error('Token expired and refresh failed. Please re-authenticate.');
      }
    }

    return storedTokens.access_token;
  }

  // Store tokens in localStorage
  private storeTokens(tokens: SpotifyTokens): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('spotify_tokens', JSON.stringify(tokens));
      const expiryTime = Date.now() + (tokens.expires_in * 1000);
      localStorage.setItem('spotify_token_expiry', expiryTime.toString());
    }
  }

  // Get stored tokens from localStorage
  private getStoredTokens(): SpotifyTokens | null {
    if (typeof window === 'undefined') return null;
    
    const stored = localStorage.getItem('spotify_tokens');
    return stored ? JSON.parse(stored) : null;
  }

  // Clear stored tokens
  public clearTokens(): void {
    this.tokens = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('spotify_tokens');
      localStorage.removeItem('spotify_token_expiry');
    }
  }

  // Check if user is authenticated
  public isAuthenticated(): boolean {
    const tokens = this.getStoredTokens();
    return !!tokens?.access_token;
  }

  // Generate random string for state parameter
  private generateRandomString(length: number): string {
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let text = '';
    for (let i = 0; i < length; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }
}

export const spotifyAuth = SpotifyAuth.getInstance(); 