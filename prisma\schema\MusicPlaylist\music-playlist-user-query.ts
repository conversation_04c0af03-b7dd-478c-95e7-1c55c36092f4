import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { InferRequestType, InferResponseType } from "hono";
import client from "@/lib/trpc";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useUserStore } from "@/store/userStore";

// Get user music playlists
export type GetMusicPlaylistsUser_ResponseType = InferResponseType<
  (typeof client.api.musicPlaylistsUser)["$get"],
  200
>;

export type GetMusicPlaylistsUser_ResponseTypeSuccess = Extract<
  GetMusicPlaylistsUser_ResponseType,
  { data: object }
>["data"];

export const useGetMusicPlaylistsUser = (filters?: {
  isAuthenticated?: boolean;
}) => {
  // Get authentication status from userStore if not provided
  const { isAuthenticated: storeIsAuthenticated } = useUserStore();
  const isUserAuthenticated = filters?.isAuthenticated ?? storeIsAuthenticated;

  return useQuery({
    queryKey: ["musicPlaylistsUser", filters],
    queryFn: async () => {
      const response = await client.api.musicPlaylistsUser.$get();

      if (!response.ok) {
        throw new Error("Failed to fetch user music playlists");
      }
      const { data } = await response.json();
      return data;
    },
    // Only enable the query if user is authenticated
    enabled: isUserAuthenticated,
  });
};

// Get single user music playlist
type GetMusicPlaylistUser_ResponseType = InferResponseType<
  (typeof client.api.musicPlaylistsUser)[":music-playlist-user-id"]["$get"],
  200
>;

export type GetMusicPlaylistUser_ResponseTypeSuccess = Extract<
  GetMusicPlaylistUser_ResponseType,
  { data: object }
>["data"];

export const useGetMusicPlaylistUser = (id?: string, isAuthenticated?: boolean) => {
  // Get authentication status from userStore if not provided
  const { isAuthenticated: storeIsAuthenticated } = useUserStore();
  const isUserAuthenticated = isAuthenticated ?? storeIsAuthenticated;

  return useQuery({
    queryKey: ["musicPlaylistsUser", { id }],
    queryFn: async () => {
      if (!id) throw new Error("No music playlist user ID provided");

      const response = await client.api.musicPlaylistsUser[":music-playlist-user-id"]["$get"]({
        param: { "music-playlist-user-id": id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch user music playlist");
      }

      const { data } = await response.json();
      return data;
    },
    // Only enable the query if user is authenticated and ID is provided
    enabled: !!id && isUserAuthenticated,
  });
};

// Create user music playlist
interface CreateMusicPlaylistUserSuccessResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

type CreateMusicPlaylistUserRequest = InferRequestType<
  (typeof client.api.musicPlaylistsUser)["$post"]
>;

export const useCreateMusicPlaylistUser = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { user, isAuthenticated } = useUserStore();

  const mutation = useMutation<
    CreateMusicPlaylistUserSuccessResponse,
    Error,
    CreateMusicPlaylistUserRequest
  >({
    mutationFn: async ({ form }) => {
      // Check authentication before executing mutation
      if (!isAuthenticated || !user) {
        throw new Error("Authentication required to create music playlist");
      }

      const response = await client.api.musicPlaylistsUser.$post({ form });

      if (!response.ok) {
        throw new Error("Failed to create user music playlist");
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Music playlist created successfully");
      router.refresh();
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser"] });
    },
    onError: (error) => {
      toast.error(`Failed to create music playlist: ${error.message}`);
    },
  });

  return mutation;
};

// Update user music playlist
type UpdateMusicPlaylistUser_ResponseType = InferResponseType<
  (typeof client.api.musicPlaylistsUser)[":music-playlist-user-id"]["$patch"],
  200
>;

export type UpdateMusicPlaylistUser_ResponseTypeSuccess = Extract<
  UpdateMusicPlaylistUser_ResponseType,
  { data: object }
>["data"];

interface UpdateMusicPlaylistUserParams {
  form: Record<string, any>;
  param: {
    id: string;
  };
}

export const useUpdateMusicPlaylistUser = () => {
  const queryClient = useQueryClient();
  const { user, isAuthenticated } = useUserStore();

  return useMutation<
    UpdateMusicPlaylistUser_ResponseType,
    Error,
    UpdateMusicPlaylistUserParams
  >({
    mutationFn: async (variables) => {
      // Check authentication before executing mutation
      if (!isAuthenticated || !user) {
        throw new Error("Authentication required to update music playlist");
      }

      const { form, param } = variables;

      if (!param?.id) {
        throw new Error("No music playlist user ID provided");
      }

      const response = await client.api.musicPlaylistsUser[":music-playlist-user-id"]["$patch"]({
        form,
        param: { "music-playlist-user-id": param.id },
      });

      if (!response.ok) {
        throw new Error(`Failed to update user music playlist. Status: ${response.status}`);
      }

      return await response.json();
    },
    onSuccess: (data) => {
      toast.success("Music playlist updated successfully");
      const musicPlaylistUserId = typeof data === 'object' && data !== null && 'data' in data
        ? (data.data as { id?: string })?.id
        : undefined;

      if (musicPlaylistUserId) {
        queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser", { id: musicPlaylistUserId }] });
      }
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser"] });
    },
    onError: (error) => {
      toast.error(`Failed to update music playlist: ${error.message}`);
    },
  });
};

// Delete user music playlist
type DeleteMusicPlaylistUser_ResponseType = InferResponseType<
  (typeof client.api.musicPlaylistsUser)[":music-playlist-user-id"]["$delete"],
  200
>;

export const useDeleteMusicPlaylistUser = () => {
  const queryClient = useQueryClient();
  const { user, isAuthenticated } = useUserStore();

  return useMutation<
    DeleteMusicPlaylistUser_ResponseType,
    Error,
    { id: string }
  >({
    mutationFn: async ({ id }) => {
      // Check authentication before executing mutation
      if (!isAuthenticated || !user) {
        throw new Error("Authentication required to delete music playlist");
      }

      if (!id) {
        throw new Error("No music playlist user ID provided");
      }

      const response = await client.api.musicPlaylistsUser[":music-playlist-user-id"]["$delete"]({
        param: { "music-playlist-user-id": id },
      });

      if (!response.ok) {
        throw new Error(`Failed to delete user music playlist. Status: ${response.status}`);
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Music playlist deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser"] });
    },
    onError: (error) => {
      toast.error(`Failed to delete music playlist: ${error.message}`);
    },
  });
};

// Add music to user playlist
interface MusicUserResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface AddMusicToMusicPlaylistUserRequest {
  musicPlaylistUserId: string;
  musicIds: string[];
}

export const useAddMusicToMusicPlaylistUser = () => {
  const queryClient = useQueryClient();
  const { user, isAuthenticated } = useUserStore();

  return useMutation<
    MusicUserResponse,
    Error,
    AddMusicToMusicPlaylistUserRequest
  >({
    mutationFn: async ({ musicPlaylistUserId, musicIds }) => {
      // Check authentication before executing mutation
      if (!isAuthenticated || !user) {
        throw new Error("Authentication required to add music to playlist");
      }

      console.log('Adding music to playlist:', { musicPlaylistUserId, musicIds });

      const response = await client.api.musicPlaylistsUser[":music-playlist-user-id"]["add-music"]["$patch"]({
        param: { "music-playlist-user-id": musicPlaylistUserId },
        json: { musicIds }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Add music error response:', errorText);
        throw new Error("Failed to add music to user playlist");
      }

      const result = await response.json();
      console.log('Add music success response:', result);
      return result;
    },
    onSuccess: (data, variables) => {
      console.log('Add music onSuccess called:', { data, variables });
      // Only invalidate the specific playlist query to prevent excessive refetching
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser", { id: variables.musicPlaylistUserId }] });
      // Invalidate the list query but don't refetch immediately
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser"] });
      console.log('Cache invalidation completed for playlist:', variables.musicPlaylistUserId);
    },
    onError: (error) => {
      toast.error(`Failed to add music to playlist: ${error.message}`);
    },
  });
};

// Remove music from user playlist
interface RemoveMusicUserResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface RemoveMusicFromMusicPlaylistUserRequest {
  musicPlaylistUserId: string;
  musicId: string;
}

export const useRemoveMusicFromMusicPlaylistUser = () => {
  const queryClient = useQueryClient();
  const { user, isAuthenticated } = useUserStore();

  return useMutation<
    RemoveMusicUserResponse,
    Error,
    RemoveMusicFromMusicPlaylistUserRequest
  >({
    mutationFn: async ({ musicPlaylistUserId, musicId }) => {
      // Check authentication before executing mutation
      if (!isAuthenticated || !user) {
        throw new Error("Authentication required to remove music from playlist");
      }

      const response = await client.api.musicPlaylistsUser[":music-playlist-user-id"]["remove-music"]["$patch"]({
        param: { "music-playlist-user-id": musicPlaylistUserId },
        form: { musicId }
      });

      if (!response.ok) {
        throw new Error("Failed to remove music from user playlist");
      }

      return await response.json();
    },
    onSuccess: (data, variables) => {
      toast.success("Music removed from playlist successfully");
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser", { id: variables.musicPlaylistUserId }] });
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser"] });
    },
    onError: (error) => {
      toast.error(`Failed to remove music from playlist: ${error.message}`);
    },
  });
};

// Reorder music in user playlist
interface ReorderMusicUserResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface ReorderMusicUserRequest {
  musicPlaylistUserId: string;
  musicOrder: string[];
}

export const useReorderMusicInMusicPlaylistUser = () => {
  const queryClient = useQueryClient();
  const { user, isAuthenticated } = useUserStore();

  return useMutation<
    ReorderMusicUserResponse,
    Error,
    ReorderMusicUserRequest
  >({
    mutationFn: async ({ musicPlaylistUserId, musicOrder }) => {
      // Check authentication before executing mutation
      if (!isAuthenticated || !user) {
        throw new Error("Authentication required to reorder music in playlist");
      }

      const response = await client.api.musicPlaylistsUser[":music-playlist-user-id"]["reorder-music"]["$patch"]({
        param: { "music-playlist-user-id": musicPlaylistUserId },
        json: { musicOrder }
      });

      if (!response.ok) {
        throw new Error("Failed to reorder music in user playlist");
      }

      return await response.json();
    },
    onSuccess: (data, variables) => {
      toast.success("Music reordered successfully");
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser", { id: variables.musicPlaylistUserId }] });
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser"] });
    },
    onError: (error) => {
      toast.error(`Failed to reorder music: ${error.message}`);
    },
  });
};

// Add videos to user playlist
interface VideoUserResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface AddVideosToMusicPlaylistUserRequest {
  musicPlaylistUserId: string;
  videoIds: string[];
}

export const useAddVideosToMusicPlaylistUser = () => {
  const queryClient = useQueryClient();
  const { user, isAuthenticated } = useUserStore();

  return useMutation<
    VideoUserResponse,
    Error,
    AddVideosToMusicPlaylistUserRequest
  >({
    mutationFn: async ({ musicPlaylistUserId, videoIds }) => {
      // Check authentication before executing mutation
      if (!isAuthenticated || !user) {
        throw new Error("Authentication required to add videos to playlist");
      }

      const response = await client.api.musicPlaylistsUser[":music-playlist-user-id"]["add-videos"]["$patch"]({
        param: { "music-playlist-user-id": musicPlaylistUserId },
        json: { videoIds }
      });

      if (!response.ok) {
        throw new Error("Failed to add videos to user playlist");
      }

      return await response.json();
    },
    onSuccess: (data, variables) => {
      toast.success("Videos added to playlist successfully");
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser", { id: variables.musicPlaylistUserId }] });
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser"] });
    },
    onError: (error) => {
      toast.error(`Failed to add videos to playlist: ${error.message}`);
    },
  });
};

// Remove video from user playlist
interface RemoveVideoUserResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface RemoveVideoFromMusicPlaylistUserRequest {
  musicPlaylistUserId: string;
  videoId: string;
}

export const useRemoveVideoFromMusicPlaylistUser = () => {
  const queryClient = useQueryClient();
  const { user, isAuthenticated } = useUserStore();

  return useMutation<
    RemoveVideoUserResponse,
    Error,
    RemoveVideoFromMusicPlaylistUserRequest
  >({
    mutationFn: async ({ musicPlaylistUserId, videoId }) => {
      // Check authentication before executing mutation
      if (!isAuthenticated || !user) {
        throw new Error("Authentication required to remove video from playlist");
      }

      const response = await client.api.musicPlaylistsUser[":music-playlist-user-id"]["remove-video"]["$patch"]({
        param: { "music-playlist-user-id": musicPlaylistUserId },
        form: { videoId }
      });

      if (!response.ok) {
        throw new Error("Failed to remove video from user playlist");
      }

      return await response.json();
    },
    onSuccess: (data, variables) => {
      toast.success("Video removed from playlist successfully");
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser", { id: variables.musicPlaylistUserId }] });
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser"] });
    },
    onError: (error) => {
      toast.error(`Failed to remove video from playlist: ${error.message}`);
    },
  });
};

// Add natural sounds to user playlist
interface NatureSoundUserResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface AddNatureSoundsToMusicPlaylistUserRequest {
  musicPlaylistUserId: string;
  natureSoundIds: string[];
}

export const useAddNatureSoundsToMusicPlaylistUser = () => {
  const queryClient = useQueryClient();
  const { user, isAuthenticated } = useUserStore();

  return useMutation<
    NatureSoundUserResponse,
    Error,
    AddNatureSoundsToMusicPlaylistUserRequest
  >({
    mutationFn: async ({ musicPlaylistUserId, natureSoundIds }) => {
      // Check authentication before executing mutation
      if (!isAuthenticated || !user) {
        throw new Error("Authentication required to add nature sounds to playlist");
      }

      console.log('Adding nature sounds to playlist:', { musicPlaylistUserId, natureSoundIds });

      const response = await client.api.musicPlaylistsUser[":music-playlist-user-id"]["add-nature-sounds"]["$patch"]({
        param: { "music-playlist-user-id": musicPlaylistUserId },
        json: { natureSoundIds }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Add nature sounds error response:', errorText);
        throw new Error("Failed to add nature sounds to user playlist");
      }

      const result = await response.json();
      console.log('Add nature sounds success response:', result);
      return result;
    },
    onSuccess: (data, variables) => {
      console.log('Add nature sounds onSuccess called:', { data, variables });
      toast.success("Nature sounds added to playlist successfully");
      // Only invalidate the specific playlist query to prevent excessive refetching
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser", { id: variables.musicPlaylistUserId }] });
      // Invalidate the list query but don't refetch immediately
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser"] });
      console.log('Cache invalidation completed for playlist:', variables.musicPlaylistUserId);
    },
    onError: (error) => {
      toast.error(`Failed to add nature sounds to playlist: ${error.message}`);
    },
  });
};

// Remove natural sound from user playlist
interface RemoveNatureSoundUserResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface RemoveNatureSoundFromMusicPlaylistUserRequest {
  musicPlaylistUserId: string;
  natureSoundId: string;
}

export const useRemoveNatureSoundFromMusicPlaylistUser = () => {
  const queryClient = useQueryClient();
  const { user, isAuthenticated } = useUserStore();

  return useMutation<
    RemoveNatureSoundUserResponse,
    Error,
    RemoveNatureSoundFromMusicPlaylistUserRequest
  >({
    mutationFn: async ({ musicPlaylistUserId, natureSoundId }) => {
      // Check authentication before executing mutation
      if (!isAuthenticated || !user) {
        throw new Error("Authentication required to remove nature sound from playlist");
      }

      const response = await client.api.musicPlaylistsUser[":music-playlist-user-id"]["remove-nature-sound"]["$patch"]({
        param: { "music-playlist-user-id": musicPlaylistUserId },
        form: { natureSoundId }
      });

      if (!response.ok) {
        throw new Error("Failed to remove nature sound from user playlist");
      }

      return await response.json();
    },
    onSuccess: (data, variables) => {
      toast.success("Nature sound removed from playlist successfully");
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser", { id: variables.musicPlaylistUserId }] });
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser"] });
    },
    onError: (error) => {
      toast.error(`Failed to remove nature sound from playlist: ${error.message}`);
    },
  });
};

// Reorder natural sounds in user playlist
interface ReorderNatureSoundsUserResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

interface ReorderNatureSoundsUserRequest {
  musicPlaylistUserId: string;
  natureSoundOrder: string[];
}

export const useReorderNatureSoundsInMusicPlaylistUser = () => {
  const queryClient = useQueryClient();
  const { user, isAuthenticated } = useUserStore();

  return useMutation<
    ReorderNatureSoundsUserResponse,
    Error,
    ReorderNatureSoundsUserRequest
  >({
    mutationFn: async ({ musicPlaylistUserId, natureSoundOrder }) => {
      // Check authentication before executing mutation
      if (!isAuthenticated || !user) {
        throw new Error("Authentication required to reorder nature sounds in playlist");
      }

      const response = await client.api.musicPlaylistsUser[":music-playlist-user-id"]["reorder-nature-sounds"]["$patch"]({
        param: { "music-playlist-user-id": musicPlaylistUserId },
        json: { natureSoundOrder }
      });

      if (!response.ok) {
        throw new Error("Failed to reorder nature sounds in user playlist");
      }

      return await response.json();
    },
    onSuccess: (data, variables) => {
      toast.success("Nature sounds reordered successfully");
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser", { id: variables.musicPlaylistUserId }] });
      queryClient.invalidateQueries({ queryKey: ["musicPlaylistsUser"] });
    },
    onError: (error) => {
      toast.error(`Failed to reorder nature sounds: ${error.message}`);
    },
  });
};