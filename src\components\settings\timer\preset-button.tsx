'use client';

import { motion } from 'framer-motion';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PresetButtonProps {
  label: string;
  description: string;
  isDefault?: boolean;
  isSelected: boolean;
  onClick: () => void;
  colorScheme: 'blue' | 'emerald' | 'purple' | 'indigo';
}

const colorSchemes = {
  blue: {
    active: "bg-blue-500 border-blue-500 text-white shadow-sm",
    inactive: "bg-white dark:bg-slate-800 hover:bg-blue-50 dark:hover:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-300",
    indicator: "bg-blue-500 dark:bg-blue-400"
  },
  emerald: {
    active: "bg-emerald-500 border-emerald-500 text-white shadow-sm",
    inactive: "bg-white dark:bg-slate-800 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 border-emerald-200 dark:border-emerald-800 text-emerald-800 dark:text-emerald-300",
    indicator: "bg-emerald-500 dark:bg-emerald-400"
  },
  purple: {
    active: "bg-purple-500 border-purple-500 text-white shadow-sm",
    inactive: "bg-white dark:bg-slate-800 hover:bg-purple-50 dark:hover:bg-purple-900/20 border-purple-200 dark:border-purple-800 text-purple-800 dark:text-purple-300",
    indicator: "bg-purple-500 dark:bg-purple-400"
  },
  indigo: {
    active: "bg-indigo-500 border-indigo-500 text-white shadow-sm",
    inactive: "bg-white dark:bg-slate-800 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 border-indigo-200 dark:border-indigo-800 text-indigo-800 dark:text-indigo-300",
    indicator: "bg-indigo-500 dark:bg-indigo-400"
  }
};

export function PresetButton({
  label,
  description,
  isDefault,
  isSelected,
  onClick,
  colorScheme
}: PresetButtonProps) {
  const colors = colorSchemes[colorScheme];
  
  return (
    <motion.button
      type="button"
      onClick={onClick}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 2 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.15 }}
      className={cn(
        "relative flex flex-col items-center justify-center p-2 rounded-lg border transition-all",
        isSelected ? colors.active : colors.inactive
      )}
    >
      {isSelected && (
        <motion.div 
          className="absolute top-1 right-1 flex items-center justify-center"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 500, damping: 30 }}
        >
          <Check className="h-3 w-3" />
        </motion.div>
      )}
      
      <span className="text-base font-medium">{label}</span>
      <span className="text-[0.65rem] mt-0.5 opacity-85">{description}</span>
      
      {isDefault && !isSelected && (
        <motion.span 
          className={`absolute top-1 right-1 h-1.5 w-1.5 rounded-full ${colors.indicator}`}
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.05 }}
        />
      )}
    </motion.button>
  );
} 