import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { memo } from 'react';
import { PhaseSwitcherProps } from '../common/types';

const PhaseSwitcher = memo(({
  onSwitchPhase,
  showControls
}: PhaseSwitcherProps) => {
  return (
    <div className={cn(
      "absolute inset-0 flex items-center justify-between z-30 pointer-events-none",
      "timer-control-fade",
      showControls ? "show" : "hide"
    )}>
      <div className="pl-1 pr-4 h-full flex items-center pointer-events-auto">
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "h-7 w-7 flex items-center justify-center p-0 transition-all duration-250 ease-out cursor-pointer",
            "rounded-full bg-black/30 hover:bg-black/40 border-white/10",
            "text-white focus:ring-1 focus:ring-white/30 focus:ring-offset-0 shadow-sm",
            showControls ? "opacity-100 hover:scale-105 active:scale-95" : "opacity-0 pointer-events-none"
          )}
          onClick={() => onSwitchPhase('prev')}
          title="Previous phase"
          aria-label="Switch to previous phase"
        >
          <ChevronLeft className="h-4 w-4 text-white" />
        </Button>
      </div>

      <div className="pr-1 pl-4 h-full flex items-center pointer-events-auto">
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "h-7 w-7 flex items-center justify-center p-0 transition-all duration-250 ease-out cursor-pointer",
            "rounded-full bg-black/30 hover:bg-black/40 border-white/10",
            "text-white focus:ring-1 focus:ring-white/30 focus:ring-offset-0 shadow-sm",
            showControls ? "opacity-100 hover:scale-105 active:scale-95" : "opacity-0 pointer-events-none"
          )}
          onClick={() => onSwitchPhase('next')}
          title="Next phase"
          aria-label="Switch to next phase"
        >
          <ChevronRight className="h-4 w-4 text-white" />
        </Button>
      </div>
    </div>
  );
});
PhaseSwitcher.displayName = 'PhaseSwitcher';

export default PhaseSwitcher;