"use client"

import { useUserStore } from "@/store/userStore"

interface WelcomeHeaderProps {
  userName?: string
}

export function WelcomeHeader({ userName }: WelcomeHeaderProps) {
  const { getDisplayName } = useUserStore()

  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return "Good morning"
    if (hour < 18) return "Good afternoon"
    return "Good evening"
  }

  // Use userName prop if provided, otherwise get from userStore
  const displayName = userName || getDisplayName()

  return (
    <div className="mb-6">
      <h1 className="text-2xl font-bold text-foreground">
        Hi, {displayName} 👋
      </h1>
      <p className="text-muted-foreground mt-1">
        {getGreeting()}! Ready to boost your productivity today?
      </p>
    </div>
  )
}
