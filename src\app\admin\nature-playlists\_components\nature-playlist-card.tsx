"use client";

import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Play, Waves, Video } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import { useDeleteAdminNaturePlaylist } from "@schemas/NaturalPlaylist/natural-playlist-admin-query";
import { toast } from "sonner";
import Image from "next/image";
import { GetAdminNaturePlaylists_ResponseTypeSuccess } from "@schemas/NaturalPlaylist/natural-playlist-admin-query";
import { useState } from "react";
import { NaturePlaylistFormSheet } from "./nature-playlist-form-sheet";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export function NaturePlaylistCard({ playlist }: { playlist: GetAdminNaturePlaylists_ResponseTypeSuccess[0] }) {
  const router = useRouter();
  const deleteNaturePlaylist = useDeleteAdminNaturePlaylist();
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleDelete = async () => {
    try {
      await deleteNaturePlaylist.mutateAsync({ id: playlist.id });
      toast.success("Nature playlist deleted successfully");
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error(error);
      toast.error("Failed to delete nature playlist");
    }
  };

  const handlePlayAll = () => router.push(`/admin/nature-playlists/${playlist.id}`);

  const renderMediaCounts = () => (
    <>
      <div className="flex items-center gap-1">
        <Video className="h-3 w-3" />
        <span className="text-xs text-muted-foreground">{playlist.videos.length}</span>
      </div>
      <div className="flex items-center gap-1">
        <Waves className="h-3 w-3" />
        <span className="text-xs text-muted-foreground">{playlist.natureSounds.length}</span>
      </div>
    </>
  );

  const renderBadges = () => (
    <>
      {playlist.isPublic && (
        <Badge variant="outline" className="text-xs">
          Public
        </Badge>
      )}
      {playlist.isDefault && (
        <Badge variant="outline" className="text-xs">
          Default
        </Badge>
      )}
    </>
  );

  return (
    <>
      <Card className="group relative overflow-hidden transition-all hover:shadow-lg max-w-full sm:max-w-[300px] md:max-w-[320px] lg:max-w-[340px] mx-auto sm:mx-0">
        <div className="relative aspect-video">
          {playlist.imageUrl ? (
            <Image
              src={playlist.imageUrl}
              alt={playlist.name || 'Nature playlist image'}
              fill
              className="object-cover transition-transform group-hover:scale-105"
              sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
              loading="lazy"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <Waves className="h-12 w-12 text-muted-foreground" />
            </div>
          )}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <Button
              variant="secondary"
              size="sm"
              onClick={handlePlayAll}
              className="transform translate-y-4 group-hover:translate-y-0 transition-transform focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              aria-label={`Play all tracks in ${playlist.name}`}
            >
              <Play className="mr-2 h-4 w-4" />
              Show Playlist
            </Button>
          </div>
        </div>

        <CardContent className="p-4">
          <div className="flex items-start justify-between gap-2">
            <div className="space-y-1 flex-1 min-w-0">
              <h3 className="font-semibold line-clamp-1 group-hover:text-primary transition-colors text-base sm:text-lg">
                {playlist.name}
              </h3>
              <p className="text-sm text-muted-foreground line-clamp-2 min-h-[2.5rem] sm:min-h-[3rem]">
                {playlist.description || "No description"}
              </p>
            </div>
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 shrink-0 opacity-0 group-hover:opacity-100 transition-opacity focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  aria-label={`More options for ${playlist.name}`}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem
                  onClick={() => router.push(`/nature-playlists/${playlist.id}`)}
                  className="cursor-pointer focus:outline-none focus:bg-accent"
                >
                  <Play className="mr-2 h-4 w-4" />
                  Show Playlist
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setIsEditSheetOpen(true)}
                  className="cursor-pointer focus:outline-none focus:bg-accent"
                >
                  Edit Details
                </DropdownMenuItem>
                {!playlist.isDefault && (
                  <DropdownMenuItem
                    className="text-destructive cursor-pointer focus:text-destructive focus:bg-accent"
                    onClick={() => setIsDeleteDialogOpen(true)}
                  >
                    Delete Nature Playlist
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardContent>

        <CardFooter className="p-4 pt-0 flex flex-wrap gap-2 text-xs sm:text-sm justify-between">
          <div className="flex gap-3">
            {renderMediaCounts()}
          </div>
          <div className="flex gap-2">
            {renderBadges()}
          </div>
        </CardFooter>
      </Card>

      {/* Edit Nature Playlist Sheet */}
      <NaturePlaylistFormSheet 
        playlistId={playlist.id}
        open={isEditSheetOpen} 
        onOpenChange={setIsEditSheetOpen}
        onSuccess={() => {
          setIsEditSheetOpen(false);
        }}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Nature Playlist</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete &ldquo;{playlist.name}&rdquo;? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-destructive"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 