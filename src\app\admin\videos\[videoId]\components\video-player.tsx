"use client";

import { useRef, useEffect } from "react";
import { cn } from "@/lib/utils";
import { GetVideo_ResponseTypeSuccess } from "@schemas/Video/video-query";

interface VideoPlayerProps {
  video: GetVideo_ResponseTypeSuccess;
  className?: string;
  autoPlay?: boolean;
}

export function VideoPlayer({ video, className, autoPlay = false }: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (videoRef.current && autoPlay) {
      videoRef.current.play().catch((error) => {
        // Autoplay might be blocked by browser
        console.log("Autoplay prevented:", error);
      });
    }
  }, [autoPlay, video.id]);

  return (
    <div className={cn("relative aspect-video w-full bg-black", className)}>
      <video
        ref={videoRef}
        className="h-full w-full"
        src={video.src}
        poster={video.thumbnail}
        controls
        controlsList="nodownload"
        preload="metadata"
      >
        Your browser does not support the video tag.
      </video>
    </div>
  );
} 