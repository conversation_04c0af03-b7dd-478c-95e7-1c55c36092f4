import { useState, useRef, useCallback, RefObject } from 'react';

// Types for the resize functionality
export type TimerSize = {
  width: number;
  timeScale: number;
};

export type FontSize = {
  time: string;
  phase: string;
};

export type ResizeDirection = 'bottom-right';

type ResizeStart = {
  x: number;
  y: number;
  width: number;
  timeScale: number;
};

export type TimerSizePreset = 'small' | 'medium' | 'large';

export function useTimerResize(timerRef: RefObject<HTMLDivElement | null>) {
  // Constants for size constraints - adjusted for new layout approach
  const MIN_WIDTH = 200;
  const MAX_WIDTH = 550; // Reduced max width to avoid layout issues
  const MIN_TIME_SCALE = 0.8;
  const MAX_TIME_SCALE = 1.8; // Reduced for better layout control
  const BASE_WIDTH = 320;

  // States for resizing
  const [isResizing, setIsResizing] = useState(false);
  const [resizeStart, setResizeStart] = useState<ResizeStart | null>(null);
  const [timerSize, setTimerSize] = useState<TimerSize | null>(null);
  const [fontSize, setFontSize] = useState<FontSize | null>(null);
  const [showResizeHandles, setShowResizeHandles] = useState(false);
  const [isResizeHandleHovered, setIsResizeHandleHovered] = useState(false);
  const [isResizePreview, setIsResizePreview] = useState(false);
  
  // Timeout refs
  const resizePreviewTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Calculate font sizes based on timer width and timeScale
  const calculateFontSizes = useCallback((width: number): FontSize => {
    const BASE_TIME_FONT = 3.2; // rem
    const BASE_PHASE_FONT = 0.8; // rem
    
    // Width factor with smoother scaling and better limits for large sizes
    const widthFactor = Math.pow(width / BASE_WIDTH, 0.35); // Reduced exponent for more controlled scaling
    
    // Calculate font sizes with improved limits
    const timeFontSize = `${Math.max(2.4, Math.min(4.2, BASE_TIME_FONT * widthFactor))}rem`;
    
    // Phase font scaling
    const phaseFontSize = `${Math.max(0.68, Math.min(0.9, BASE_PHASE_FONT * widthFactor))}rem`;
    
    return { time: timeFontSize, phase: phaseFontSize };
  }, []);

  // Apply size preset with improved scaling
  const applyPreset = useCallback((preset: TimerSizePreset) => {
    // Adjust the presets for better proportional sizing
    const adjustedPresets: Record<TimerSizePreset, TimerSize> = {
      small: { width: 240, timeScale: 0.9 },
      medium: { width: 320, timeScale: 1.2 },
      large: { width: 400, timeScale: 1.5 }, // Reduced from original 1.6
    };
    
    const newSize = adjustedPresets[preset];
    setTimerSize(newSize);
    setFontSize(calculateFontSizes(newSize.width));
  }, [calculateFontSizes]);

  // Start resize
  const startResize = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Clear any preview timeouts
    if (resizePreviewTimeoutRef.current) {
      clearTimeout(resizePreviewTimeoutRef.current);
      resizePreviewTimeoutRef.current = null;
    }
    
    setIsResizePreview(false);
    
    if (timerRef.current) {
      const rect = timerRef.current.getBoundingClientRect();
      const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
      
      // Get current timer scale or use default
      const currentTimeScale = timerSize?.timeScale || 1;
      
      setResizeStart({ 
        x: clientX, 
        y: 0, // Not used for calculation but needed for the type
        width: rect.width,
        timeScale: currentTimeScale
      });
      
      setIsResizing(true);
      document.body.style.cursor = 'nwse-resize';
    }
  }, [timerRef, timerSize]);

  // Handle resize move with smoother scaling and better limits
  const handleResizeMove = useCallback((clientX: number) => {
    if (!isResizing || !resizeStart || !timerRef.current) return;
    
    // Calculate deltas for width and derive time scale
    const deltaX = clientX - resizeStart.x;
    
    // Calculate new width based on mouse movement with smoother transitions
    const newWidth = Math.max(MIN_WIDTH, Math.min(MAX_WIDTH, resizeStart.width + deltaX));
    
    // Calculate scale factor based on the width change with improved curve
    const widthRatio = newWidth / resizeStart.width;
    const scaleRatio = Math.pow(widthRatio, 0.8); // More conservative scaling
    
    // Calculate new time scale with improved bounds
    const newTimeScale = Math.max(
      MIN_TIME_SCALE, 
      Math.min(MAX_TIME_SCALE, resizeStart.timeScale * scaleRatio)
    );
    
    // Apply non-linear scaling to prevent extreme growth at larger sizes
    const adjustedTimeScale = newTimeScale > 1.4 
      ? 1.4 + (newTimeScale - 1.4) * 0.7 
      : newTimeScale;
    
    // Limit updates by applying change only when there's a meaningful difference
    const roundedWidth = Math.round(newWidth);
    const roundedTimeScale = Number(adjustedTimeScale.toFixed(2)); // Reduce jitter
    
    // Only update state if values have changed
    if (timerSize?.width !== roundedWidth || timerSize?.timeScale !== roundedTimeScale) {
      // Set the new timer size
      setTimerSize({ 
        width: roundedWidth, 
        timeScale: roundedTimeScale
      });
      setFontSize(calculateFontSizes(roundedWidth));
    }
  }, [isResizing, resizeStart, timerRef, calculateFontSizes, MIN_WIDTH, MAX_WIDTH, MIN_TIME_SCALE, MAX_TIME_SCALE, timerSize]);

  // End resize
  const endResize = useCallback(() => {
    setIsResizing(false);
    setResizeStart(null);
    document.body.style.cursor = 'default';
  }, []);

  // Show/hide resize handles with improved behavior
  const showHandles = useCallback(() => {
    setShowResizeHandles(true);
  }, []);

  const hideHandles = useCallback(() => {
    if (!isResizing) {
      setShowResizeHandles(false);
    }
  }, [isResizing]);

  // Resize handle hover effects with enhanced feedback
  const handleResizeHandleMouseEnter = useCallback(() => {
    setIsResizeHandleHovered(true);
    
    if (resizePreviewTimeoutRef.current) {
      clearTimeout(resizePreviewTimeoutRef.current);
    }
    
    // Use a slightly longer delay for more natural interaction
    // This prevents the tooltip from appearing during quick mouse movements
    resizePreviewTimeoutRef.current = setTimeout(() => {
      setIsResizePreview(true);
    }, 400);
  }, []);
  
  const handleResizeHandleMouseLeave = useCallback(() => {
    setIsResizeHandleHovered(false);
    
    if (resizePreviewTimeoutRef.current) {
      clearTimeout(resizePreviewTimeoutRef.current);
      resizePreviewTimeoutRef.current = null;
    }
    
    // Add a slightly longer delay to prevent flickering when moving between elements
    // Improves UX when the mouse briefly leaves and re-enters the resize handle
    setTimeout(() => {
      if (!isResizing) {
        setIsResizePreview(false);
      }
    }, 200);
  }, [isResizing]);

  // Reset size
  const resetSize = useCallback(() => {
    setTimerSize(null);
    setFontSize(null);
  }, []);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (resizePreviewTimeoutRef.current) {
      clearTimeout(resizePreviewTimeoutRef.current);
      resizePreviewTimeoutRef.current = null;
    }
  }, []);

  return {
    // States
    isResizing,
    timerSize,
    fontSize,
    showResizeHandles,
    isResizeHandleHovered,
    isResizePreview,
    
    // Actions
    startResize,
    handleResizeMove,
    endResize,
    showHandles,
    hideHandles,
    handleResizeHandleMouseEnter,
    handleResizeHandleMouseLeave,
    resetSize,
    applyPreset,
    cleanup,
  };
} 