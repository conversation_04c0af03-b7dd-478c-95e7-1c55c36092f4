"use client";

import { useSession } from "@/server/auth/auth-client";
import { useUserStore, userStoreHelpers } from "@/store/userStore";
import { useEffect, useCallback, useRef } from "react";
import React from "react";

/**
 * Custom hook for authentication checking and user store management
 * This hook provides utilities to check auth status and automatically
 * clear the user store when the user is not authenticated
 */
export function useAuthCheck() {
  const { data: session, isPending, error: sessionError } = useSession();
  const {
    user,
    session: storedSession,
    isAuthenticated,
    isSessionValid,
    clearUser,
    setError,
    setLoading,
  } = useUserStore();

  // Enhanced function to clear user store with logging
  const clearUserStore = useCallback(() => {
    console.log("🔄 [useAuthCheck] Clearing user store - authentication invalid");
    clearUser();
    setError(null);
    setLoading(false);
  }, [clearUser, setError, setLoading]);

  // Check if user is currently authenticated with valid session
  const isUserAuthenticated = useCallback(() => {
    return !!(
      isAuthenticated &&
      user &&
      storedSession &&
      isSessionValid()
    );
  }, [isAuthenticated, user, storedSession, isSessionValid]);

  // Force authentication check and clear store if invalid
  const forceAuthCheck = useCallback(() => {
    console.log("🔍 [useAuthCheck] Force checking authentication status");

    // If we have session data from better-auth but it's invalid
    if (session && (!session.user || !session.session)) {
      console.log("❌ [useAuthCheck] Invalid session data from better-auth");
      clearUserStore();
      return false;
    }

    // If we have stored session but it's expired
    if (storedSession && !isSessionValid()) {
      console.log("⏰ [useAuthCheck] Stored session expired");
      clearUserStore();
      return false;
    }

    // If better-auth says no session but we have stored data
    if (!session && (isAuthenticated || user || storedSession)) {
      console.log("🚪 [useAuthCheck] No session from better-auth, clearing stored data");
      clearUserStore();
      return false;
    }

    // If we have session errors
    if (sessionError) {
      console.log("❌ [useAuthCheck] Session error detected:", sessionError);
      setError("Authentication error occurred");
      clearUserStore();
      return false;
    }

    return isUserAuthenticated();
  }, [
    session,
    storedSession,
    isAuthenticated,
    user,
    sessionError,
    isSessionValid,
    isUserAuthenticated,
    clearUserStore,
    setError,
  ]);

  // Initialize or clear user store based on session
  const syncAuthState = useCallback(() => {
    if (isPending) {
      setLoading(true);
      return;
    }

    setLoading(false);

    if (session?.user && session?.session) {
      try {
        // console.log("✅ [useAuthCheck] Syncing valid session to user store");
        userStoreHelpers.initializeFromSessionData(session);
        setError(null);
      } catch (error) {
        console.error("❌ [useAuthCheck] Error syncing session:", error);
        setError("Failed to sync authentication state");
        clearUserStore();
      }
    } else {
      // No valid session, clear if we have stored data
      if (isAuthenticated || user || storedSession) {
        console.log("🧹 [useAuthCheck] No valid session, clearing user store");
        clearUserStore();
      }
    }
  }, [
    session,
    isPending,
    isAuthenticated,
    user,
    storedSession,
    setLoading,
    setError,
    clearUserStore,
  ]);

  // Auto-sync authentication state when session changes
  useEffect(() => {
    syncAuthState();
  }, [syncAuthState]);

  // Require authentication - redirect or show error if not authenticated
  const requireAuth = useCallback((
    redirectTo?: string,
    showError: boolean = true
  ) => {
    const isAuth = forceAuthCheck();

    if (!isAuth) {
      if (showError) {
        setError("Authentication required");
      }

      if (redirectTo && typeof window !== 'undefined') {
        console.log(`🔄 [useAuthCheck] Redirecting to ${redirectTo}`);
        window.location.href = redirectTo;
      }

      return false;
    }

    return true;
  }, [forceAuthCheck, setError]);

  // Logout function that clears everything
  const logout = useCallback(async () => {
    console.log("🚪 [useAuthCheck] Logging out user");

    try {
      // Import signOut dynamically to avoid circular dependencies
      const { signOut } = await import("@/server/auth/auth-client");
      await signOut();
    } catch (error) {
      console.error("❌ [useAuthCheck] Error during signOut:", error);
    }

    // Always clear the user store regardless of signOut success
    clearUserStore();
  }, [clearUserStore]);

  return {
    // State
    isAuthenticated: isUserAuthenticated(),
    user,
    session: storedSession,
    isLoading: isPending,
    error: sessionError,

    // Actions
    forceAuthCheck,
    requireAuth,
    logout,
    clearUserStore,
    syncAuthState,

    // Utilities
    isSessionValid: isSessionValid(),
    hasValidSession: !!(session?.user && session?.session),
  };
}

/**
 * Hook specifically for components that require authentication
 * Automatically redirects to login if not authenticated
 */
export function useRequireAuth(redirectTo: string = "/auth/sign-in") {
  const authCheck = useAuthCheck();

  useEffect(() => {
    if (!authCheck.isLoading && !authCheck.isAuthenticated) {
      console.log("🔒 [useRequireAuth] Authentication required, redirecting");
      authCheck.requireAuth(redirectTo);
    }
  }, [authCheck.isLoading, authCheck.isAuthenticated, redirectTo, authCheck]);

  return authCheck;
}

/**
 * Hook for components that need to react to authentication changes
 * Provides callbacks for login/logout events
 */
export function useAuthEvents(callbacks: {
  onLogin?: (user: any) => void;
  onLogout?: () => void;
  onSessionExpired?: () => void;
  onError?: (error: string) => void;
} = {}) {
  const authCheck = useAuthCheck();
  const { onLogin, onLogout, onSessionExpired, onError } = callbacks;

  // Track previous auth state to detect changes
  const prevAuthRef = useRef(authCheck.isAuthenticated);
  const prevErrorRef = useRef(authCheck.error);

  useEffect(() => {
    // Login event
    if (!prevAuthRef.current && authCheck.isAuthenticated && authCheck.user) {
      console.log("🎉 [useAuthEvents] User logged in");
      onLogin?.(authCheck.user);
    }

    // Logout event
    if (prevAuthRef.current && !authCheck.isAuthenticated) {
      console.log("👋 [useAuthEvents] User logged out");
      onLogout?.();
    }

    // Session expired event
    if (prevAuthRef.current && !authCheck.isAuthenticated && !authCheck.isSessionValid) {
      console.log("⏰ [useAuthEvents] Session expired");
      onSessionExpired?.();
    }

    // Error event
    if (authCheck.error && authCheck.error !== prevErrorRef.current) {
      console.log("❌ [useAuthEvents] Authentication error:", authCheck.error);
      onError?.(authCheck.error.message);
    }

    // Update refs
    prevAuthRef.current = authCheck.isAuthenticated;
    prevErrorRef.current = authCheck.error;
  }, [
    authCheck.isAuthenticated,
    authCheck.user,
    authCheck.error,
    authCheck.isSessionValid,
    onLogin,
    onLogout,
    onSessionExpired,
    onError,
  ]);

  return authCheck;
}
