"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Brain, Flame, Lightbulb, Target, Zap } from "lucide-react"
import { FocusFlowChart } from "./FocusFlowChart"
import { ProductivityScoreGauge } from "./ProductivityScoreGauge"
import { OptimalFocusTimeChart } from "./OptimalFocusTimeChart"

// Define more specific types for chart data
interface FlowStateDataPoint {
  time: string;
  flowScore: number;
}

interface OptimalTimeDataPoint {
  hour: string;
  score: number;
}

interface InnovativeMetricsProps {
  data: {
    productivityScore: number
    focusEfficiency: number
    deepWorkRatio: number
    goalAchievement: number
    flowStateRating: string
    flowStateTime: string
    flowStateDuration: string
    flowStateData: FlowStateDataPoint[]
    flowTriggers: string[]
    flowBlockers: string[]
    optimalTimeData: OptimalTimeDataPoint[]
    optimalDay: string
    optimalDayCompletion: number
    optimalTime: string
    optimalTimeEfficiency: number
    optimalDuration: string
    optimalDurationSuccess: number
  }
}

export function InnovativeMetrics({ data }: InnovativeMetricsProps) {
  return (
    <Card className="border-border bg-card/50 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Lightbulb className="mr-2 h-5 w-5 text-amber-500" /> Innovative Metrics
        </CardTitle>
        <CardDescription>Advanced insights to optimize your productivity</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="productivity" className="w-full">
          <TabsList className="mb-4 grid w-full grid-cols-3 bg-muted/50">
            <TabsTrigger value="productivity" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              <Zap className="mr-2 h-4 w-4" /> Productivity
            </TabsTrigger>
            <TabsTrigger value="flow" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              <Brain className="mr-2 h-4 w-4" /> Flow State
            </TabsTrigger>
            <TabsTrigger value="optimal" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              <Target className="mr-2 h-4 w-4" /> Optimal Times
            </TabsTrigger>
          </TabsList>

          <TabsContent value="productivity" className="space-y-4">
            <div className="flex flex-col items-center justify-center">
              <ProductivityScoreGauge score={data.productivityScore} />
              <p className="mt-2 text-center text-sm text-muted-foreground">
                Your productivity score is calculated based on session completion rate, focus consistency, and daily
                goals achievement.
              </p>
            </div>

            <div className="mt-6 space-y-4">
              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-sm font-medium">Focus Efficiency</span>
                  <span className="text-sm text-muted-foreground">{data.focusEfficiency}%</span>
                </div>
                <Progress value={data.focusEfficiency} className="h-2 bg-muted">
                  <div
                    className="h-full bg-gradient-to-r from-amber-500 to-primary transition-all"
                    style={{ width: `${data.focusEfficiency}%` }}
                  />
                </Progress>
                <p className="mt-1 text-xs text-muted-foreground/70">Ratio of completed focus time to total scheduled time</p>
              </div>

              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-sm font-medium">Deep Work Ratio</span>
                  <span className="text-sm text-muted-foreground">{data.deepWorkRatio}%</span>
                </div>
                <Progress value={data.deepWorkRatio} className="h-2 bg-muted">
                  <div
                    className="h-full bg-gradient-to-r from-blue-500 to-emerald-500 transition-all"
                    style={{ width: `${data.deepWorkRatio}%` }}
                  />
                </Progress>
                <p className="mt-1 text-xs text-muted-foreground/70">Percentage of sessions with 3+ consecutive pomodoros</p>
              </div>

              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-sm font-medium">Goal Achievement</span>
                  <span className="text-sm text-muted-foreground">{data.goalAchievement}%</span>
                </div>
                <Progress value={data.goalAchievement} className="h-2 bg-muted">
                  <div
                    className="h-full bg-gradient-to-r from-violet-500 to-fuchsia-500 transition-all"
                    style={{ width: `${data.goalAchievement}%` }}
                  />
                </Progress>
                <p className="mt-1 text-xs text-muted-foreground/70">How often you meet your daily focus goals</p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="flow" className="space-y-4">
            <div className="rounded-lg bg-accent/30 p-4">
              <div className="mb-2 flex items-center justify-between">
                <div className="flex items-center">
                  <Flame className="mr-2 h-5 w-5 text-orange-500" />
                  <h3 className="font-medium">Flow State Analysis</h3>
                </div>
                <span className="rounded-full bg-orange-500/20 px-2 py-1 text-xs font-medium text-orange-400">
                  {data.flowStateRating}
                </span>
              </div>
              <p className="text-sm text-muted-foreground">
                You achieve flow state most consistently during {data.flowStateTime} sessions, with an average duration
                of {data.flowStateDuration} minutes before interruption.
              </p>
            </div>

            <div className="h-[250px] w-full">
              <FocusFlowChart data={data.flowStateData} />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-1 text-sm font-medium">Flow Triggers</h4>
                <ul className="space-y-1 text-xs text-muted-foreground">
                  {data.flowTriggers.map((trigger: string, index: number) => (
                    <li key={index} className="flex items-center">
                      <span className="mr-2 h-1.5 w-1.5 rounded-full bg-emerald-500"></span>
                      {trigger}
                    </li>
                  ))}
                </ul>
              </div>
              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-1 text-sm font-medium">Flow Blockers</h4>
                <ul className="space-y-1 text-xs text-muted-foreground">
                  {data.flowBlockers.map((blocker: string, index: number) => (
                    <li key={index} className="flex items-center">
                      <span className="mr-2 h-1.5 w-1.5 rounded-full bg-primary"></span>
                      {blocker}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="optimal" className="space-y-4">
            <div className="rounded-lg bg-accent/30 p-4">
              <h3 className="mb-2 font-medium">Your Optimal Focus Times</h3>
              <p className="text-sm text-muted-foreground">
                Based on your historical data, your peak productivity occurs during these times:
              </p>
            </div>

            <div className="h-[250px] w-full">
              <OptimalFocusTimeChart data={data.optimalTimeData} />
            </div>

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-1 text-sm font-medium">Best Day</h4>
                <p className="text-lg font-bold text-amber-500">{data.optimalDay}</p>
                <p className="text-xs text-muted-foreground">{data.optimalDayCompletion}% completion rate</p>
              </div>
              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-1 text-sm font-medium">Best Time</h4>
                <p className="text-lg font-bold text-emerald-500">{data.optimalTime}</p>
                <p className="text-xs text-muted-foreground">{data.optimalTimeEfficiency}% focus efficiency</p>
              </div>
              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-1 text-sm font-medium">Best Duration</h4>
                <p className="text-lg font-bold text-blue-500">{data.optimalDuration}</p>
                <p className="text-xs text-muted-foreground">{data.optimalDurationSuccess}% success rate</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
