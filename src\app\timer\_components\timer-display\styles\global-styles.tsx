import React from 'react';

export const TimerGlobalStyles: React.FC = () => (
  <>
    {/* SVG Filters for Glass Effect */}
    {/* <svg style={{ display: 'none' }}>
      <filter id="container-glass" x="0%" y="0%" width="100%" height="100%">
        <feTurbulence type="fractalNoise" baseFrequency="0.008 0.008" numOctaves="2" seed="92" result="noise" />
        <feGaussianBlur in="noise" stdDeviation="0.02" result="blur" />
        <feDisplacementMap in="SourceGraphic" in2="blur" scale="77" xChannelSelector="R" yChannelSelector="G" />
      </filter>
      <filter id="btn-glass" primitiveUnits="objectBoundingBox">
        <feImage href="data:image/png;base64,iVBORwkSuQmCC" x="0" y="0" width="1" height="1" result="map"></feImage>
        <feGaussianBlur in="SourceGraphic" stdDeviation="0.02" result="blur"></feGaussianBlur>
        <feDisplacementMap id="disp" in="blur" in2="map" scale="1" xChannelSelector="R" yChannelSelector="G" />
      </filter>
    </svg> */}
    <style jsx global>{`
      /* Prevent unwanted scrollbars on timer page */
      body.has-timer {
        overflow-y: auto;
        overflow-x: hidden;
      }
    
    /* Hide scrollbar for dialog containers */
    #settings-dialog-portal-container,
    #prevent-timer-drag-overlay {
      overflow: hidden !important;
    }
    
    /* Prevent page from shifting when dialog opens */
    html.dialog-open {
      overflow: hidden !important;
    }
    
    /* Ensure the timer page doesn't contribute to unwanted scrollbars */
    .timer-page-container {
      overflow: hidden !important;
      height: 100% !important;
      width: 100% !important;
    }

    @keyframes timerScaleIn {
      from { transform: scale3d(1, 1, 1); }
      to { transform: scale3d(1.02, 1.02, 1); }
    }
    
    @keyframes timerScaleOut {
      from { transform: scale3d(1.02, 1.02, 1); }
      to { transform: scale3d(1, 1, 1); }
    }
    
    @keyframes timerFadeIn {
      from { 
        opacity: 0;
        transform: translate3d(0, 8px, 0) scale3d(0.96, 0.96, 1);
      }
      to { 
        opacity: 1;
        transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
      }
    }
    
    @keyframes timerFadeOut {
      from { 
        opacity: 1;
        transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
      }
      to { 
        opacity: 0;
        transform: translate3d(0, 8px, 0) scale3d(0.96, 0.96, 1);
      }
    }
    
    @keyframes timerPulse {
      0% { transform: scale3d(1, 1, 1); }
      50% { transform: scale3d(1.03, 1.03, 1); }
      100% { transform: scale3d(1, 1, 1); }
    }
    
    @keyframes timerGlow {
      0% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.1); }
      50% { box-shadow: 0 0 15px rgba(255, 255, 255, 0.2); }
      100% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.1); }
    }
    
    /* Subtle animation for running timer */
    .timer-running-animation {
      animation: timerPulse 5s infinite ease-in-out, timerGlow 4s infinite ease-in-out;
      animation-delay: 0.5s;
    }
    
    /* Utility class for standardized transitions */
    .transition-properties {
      transition-property: opacity, transform, background-color, box-shadow, color, height, margin, padding;
      transition-duration: 220ms;
      transition-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
    }
    
    .timer-control-fade {
      animation-duration: 250ms;
      animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
      animation-fill-mode: both;
      backface-visibility: hidden;
      transform-style: preserve-3d;
      contain: paint;
    }
    
    .timer-control-fade.show {
      animation-name: timerFadeIn;
    }
    
    .timer-control-fade.hide {
      animation-name: timerFadeOut;
    }
    
    /* Coordinated entrance for UI elements */
    .timer-control-fade[style*="animation-delay"] {
      opacity: 0;
      will-change: opacity, transform;
    }
    
    /* Smoother hover transitions */
    .timer-control-fade.show {
      will-change: opacity, transform;
    }
    
    /* Reset will-change after animation completes to free GPU resources */
    .timer-control-fade.show:not(:hover) {
      will-change: auto;
      transition: will-change 400ms ease;
    }
    
    /* Glass Effect Styles for Timer Container */
    .timer-glass-container {
      /* Don't override position - let Tailwind's fixed positioning work */
      overflow: hidden;
    }

    .timer-glass-container::before {
      content: '';
      position: absolute;
      inset: 0;
      z-index: 0;
      overflow: hidden;
      border-radius: inherit;
      box-shadow: inset 2px 2px 0px -2px rgba(255, 255, 255, 0.7), inset 0 0 3px 1px rgba(255, 255, 255, 0.7);
    }

    .timer-glass-container::after {
      content: '';
      position: absolute;
      z-index: -1;
      inset: 0;
      border-radius: inherit;
      backdrop-filter: blur(0px);
      filter: url(#container-glass);
      overflow: hidden;
      isolation: isolate;
    }

    /* Maintain glass effect during hover and drag states */
    .timer-glass-container:hover::after,
    .timer-glass-container.opacity-90::after,
    .timer-glass-container.cursor-grabbing::after {
      filter: url(#container-glass);
    }

    /* Ensure glass effect persists during all interaction states */
    .timer-glass-container:hover::before,
    .timer-glass-container.opacity-90::before,
    .timer-glass-container.cursor-grabbing::before {
      box-shadow: inset 2px 2px 0px -2px rgba(255, 255, 255, 0.7), inset 0 0 3px 1px rgba(255, 255, 255, 0.7);
    }

    /* Ensure glass effect is maintained during resize operations */
    .timer-glass-container.cursor-pointer::after,
    .timer-glass-container:active::after {
      filter: url(#container-glass);
    }

    .timer-glass-container.cursor-pointer::before,
    .timer-glass-container:active::before {
      box-shadow: inset 2px 2px 0px -2px rgba(255, 255, 255, 0.7), inset 0 0 3px 1px rgba(255, 255, 255, 0.7);
    }

    /* Focus outlines for accessibility */
    .timer-focus-ring:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.7), 0 0 0 4px rgba(59, 130, 246, 0.5);
    }
    
    /* Animation for progress bar during running state */
    @keyframes progressPulse {
      0% { opacity: 0.8; }
      50% { opacity: 1; }
      100% { opacity: 0.8; }
    }
    
    .progress-pulse {
      animation: progressPulse 2s infinite ease-in-out;
    }

    /* Mobile-specific optimizations to prevent pull-to-refresh during timer drag */
    @media (max-width: 768px) {
      html, body {
        overscroll-behavior: none;
        touch-action: manipulation;
      }
      
      /* Specific handling for timer component on mobile - only during hover/interaction */
      .timer-focus-ring:active,
      .timer-focus-ring.timer-interacting {
        touch-action: none; /* Only prevent touch behaviors during active interaction */
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
      }
      
      /* During drag operations, prevent all scroll behaviors */
      body.timer-dragging {
        overflow: hidden !important;
        position: fixed !important;
        width: 100% !important;
        touch-action: none !important;
        overscroll-behavior: none !important;
      }
      
      /* Ensure other buttons remain interactive on mobile */
      button:not(.timer-focus-ring button) {
        touch-action: manipulation;
        -webkit-touch-callout: default;
        -webkit-user-select: auto;
        user-select: auto;
      }
    }
  `}</style>
  </>
);