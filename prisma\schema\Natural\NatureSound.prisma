// NatureSound.prisma
model NatureSound {
    id          String                @id @default(cuid())
    title       String /// @zod.custom.use(z.string().min(1))
    category    NatureSoundCategory[]
    src         String? /// @zod.custom.use(z.string().url())
    source      MediaSource?          @default(OTHER)
    isPublic    Boolean               @default(false)
    creatorType UserRole              @default(ADMIN) // To distinguish admin vs user created content

    isFeatured  Boolean               @default(false)

    // User who created this nature sound (optional)
    userId String
    user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

    // Relationship with nature playlists
    naturePlaylists NaturePlaylist[]

    // Relationship with user music playlists
    musicPlaylistsUser MusicPlaylistUser[]

    createdAt DateTime @default(now()) /// @zod.custom.use(z.union([z.date(), z.string().datetime()]))
    updatedAt DateTime @updatedAt /// @zod.custom.use(z.union([z.date(), z.string().datetime()]))

    @@index([userId])
    @@index([category])
}

enum NatureSoundCategory {
    RAIN
    FOREST
    OCEAN
    THUNDER
    WIND
    FIRE
    BIRDS
    WATERFALL
    STREAM
    WAVES
    NIGHT
    INSECTS
    RIVER
    STORM
    LAKE
    WHITE_NOISE
    AMBIENT
    CITY
    PEOPLE
}
