"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Clock, Calendar, <PERSON><PERSON><PERSON>, TrendingUp, ArrowUp, ArrowDown } from "lucide-react"
import { cn } from "@/lib/utils"

interface StatsOverviewProps {
  todayCompleted: number
  currentStreak: number
  focusTimeToday: number
  historicalData: any
}

export function StatsOverview({ todayCompleted, currentStreak, focusTimeToday, historicalData }: StatsOverviewProps) {
  // Format focus time (in seconds) to hours and minutes
  const formatFocusTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)

    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  // Calculate average daily sessions (from available data)
  const totalDays = historicalData.dailyFocusTime.length || 1
  const averageDaily = historicalData.dailyFocusTime.length
    ? Math.round(historicalData.completionRate.completed / totalDays)
    : 0

  // Calculate average daily minutes
  const averageDailyMinutes = historicalData.dailyFocusTime.length
    ? Math.round(historicalData.dailyFocusTime.reduce((acc: number, day: any) => acc + day.minutes, 0) / totalDays)
    : 0

  // Calculate total focus time from historical data (in hours)
  const totalFocusHours = (historicalData.dailyFocusTime
    .reduce((acc: number, day: any) => acc + day.minutes / 60, 0))
    .toFixed(1)

  // Helper function to render trend indicator
  const renderTrendIndicator = (isAbove: boolean, text: string) => (
    <div className={cn(
      "flex items-center gap-1 text-xs font-medium",
      isAbove ? "text-emerald-600 dark:text-emerald-400" : "text-amber-600 dark:text-amber-400"
    )}>
      {isAbove ? (
        <ArrowUp className="h-3 w-3" />
      ) : (
        <ArrowDown className="h-3 w-3" />
      )}
      <span>{text}</span>
    </div>
  )

  const statsData = [
    {
      id: "sessions",
      title: "Today's Sessions",
      value: todayCompleted.toString(),
      icon: BarChart,
      iconColor: "text-primary",
      bgColor: "bg-primary/8 dark:bg-primary/12",
      borderColor: "border-primary/20 dark:border-primary/30",
      trend: todayCompleted > averageDaily,
      trendText: todayCompleted > averageDaily ? "Above average" : "Below average"
    },
    {
      id: "streak",
      title: "Current Streak",
      value: `${currentStreak} days`,
      icon: Calendar,
      iconColor: "text-amber-600 dark:text-amber-400",
      bgColor: "bg-amber-500/8 dark:bg-amber-500/12",
      borderColor: "border-amber-500/20 dark:border-amber-500/30",
      trend: currentStreak > 0,
      trendText: currentStreak > 0 ? "Keep it going!" : "Start today"
    },
    {
      id: "focus-time",
      title: "Today's Focus Time",
      value: formatFocusTime(focusTimeToday),
      icon: Clock,
      iconColor: "text-emerald-600 dark:text-emerald-400",
      bgColor: "bg-emerald-500/8 dark:bg-emerald-500/12",
      borderColor: "border-emerald-500/20 dark:border-emerald-500/30",
      trend: focusTimeToday / 60 > averageDailyMinutes,
      trendText: focusTimeToday / 60 > averageDailyMinutes ? "Above average" : "Below average"
    },
    {
      id: "total-time",
      title: "Total Focus Time",
      value: `${totalFocusHours} hrs`,
      icon: TrendingUp,
      iconColor: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-500/8 dark:bg-blue-500/12",
      borderColor: "border-blue-500/20 dark:border-blue-500/30",
      trend: true,
      trendText: "Lifetime"
    }
  ]

  return (
    <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-4">
      {statsData.map((stat) => {
        const IconComponent = stat.icon
        return (
          <Card
            key={stat.id}
            className={cn(
              "group relative overflow-hidden border transition-all duration-200 ease-out",
              "bg-gradient-to-br from-background via-background to-muted/20",
              "hover:shadow-md hover:shadow-primary/5 hover:-translate-y-0.5",
              "dark:hover:shadow-primary/10",
              stat.borderColor
            )}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <p className="text-xs font-medium text-muted-foreground/80 uppercase tracking-wide mb-1">
                    {stat.title}
                  </p>
                  <h3 className="text-xl font-bold tracking-tight mb-2 text-foreground">
                    {stat.value}
                  </h3>
                  {stat.id !== "total-time" ? (
                    renderTrendIndicator(stat.trend, stat.trendText)
                  ) : (
                    <div className="text-xs font-medium text-muted-foreground">
                      {stat.trendText}
                    </div>
                  )}
                </div>
                <div className={cn(
                  "flex-shrink-0 rounded-lg p-2.5 transition-all duration-200",
                  stat.bgColor,
                  "group-hover:scale-105"
                )}>
                  <IconComponent className={cn("h-4 w-4", stat.iconColor)} />
                </div>
              </div>

              {/* Subtle gradient overlay for depth */}
              <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/[0.02] pointer-events-none" />
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
