'use client';

import { useState } from 'react';
import { <PERSON>, Headphones, Speaker } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { AudioGrid } from '@/components/audio/audio-grid';
import { useAudioStore } from '@/lib/audio-store';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

interface AudioSettingsProps {
  className?: string;
}

export function AudioSettings({ className }: AudioSettingsProps) {
  const { selectedAudios, useVideoDefaultAudio } = useAudioStore();
  const [isOpen, setIsOpen] = useState(false);
  
  // Total active sounds
  const activeSoundsCount = !useVideoDefaultAudio ? selectedAudios.length : 0;

  return (
    <div className={cn("relative", className)}>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-2 group relative cursor-pointer"
            aria-label={`Audio settings (${activeSoundsCount} tracks active)`}
          >
            <Speaker className="h-4 w-4" />
            <span>Music</span>
            {activeSoundsCount > 0 && !useVideoDefaultAudio && (
              <Badge variant="secondary" className="h-5 min-w-5 px-1.5 flex items-center justify-center text-[10px] leading-none font-medium">
                {activeSoundsCount}
              </Badge>
            )}
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-md md:max-w-lg lg:max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Music className="h-5 w-5" />
              <span>Music Settings</span>
            </DialogTitle>
            <DialogDescription>
              Configure background sounds for your focus sessions. Select multiple tracks to create your perfect soundscape.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <AudioGrid />
          </div>
          
          <DialogFooter className="sm:justify-between">
            <div className="hidden sm:flex items-center text-sm text-muted-foreground">
              {activeSoundsCount > 0 && !useVideoDefaultAudio ? (
                <div className="flex items-center gap-2">
                  <Headphones className="h-4 w-4" />
                  <span>
                    {activeSoundsCount} sound{activeSoundsCount !== 1 ? 's' : ''} active
                  </span>
                </div>
              ) : useVideoDefaultAudio ? (
                <div className="flex items-center">
                  <Speaker className="h-4 w-4 mr-2" />
                  <span>Using video audio</span>
                </div>
              ) : (
                <div className="flex items-center">
                  <Music className="h-4 w-4 mr-2 text-amber-500" />
                  <span className="text-amber-500">No tracks selected</span>
                </div>
              )}
            </div>
            <Button variant="default" onClick={() => setIsOpen(false)}>
              Done
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 