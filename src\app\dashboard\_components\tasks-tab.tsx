"use client"

import { TaskStatsOverview } from "@/components/stats/TaskStatsOverview"
import { TaskCompletionChart } from "@/components/stats/TaskCompletionChart"
import { TaskPriorityDistribution } from "@/components/stats/TaskPriorityDistribution"
import { TaskPomodoroRelationship } from "@/components/stats/TaskPomodoroRelationship"
import { TaskFocusInsights } from "@/components/stats/TaskFocusInsights"

export function TasksTab() {
  return (
    <div className="space-y-4">
      <TaskStatsOverview />

      <div className="grid gap-4 md:grid-cols-2">
        <TaskCompletionChart />
        <TaskPriorityDistribution />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <TaskPomodoroRelationship />
        <TaskFocusInsights />
      </div>
    </div>
  )
}
