"use client"

import * as React from "react"
import { format, startOfWeek, endOfWeek, isWithinInterval } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { DateRange } from "react-day-picker"

export interface WeekPickerProps {
  week: DateRange | undefined
  onSelect: (week: DateRange | undefined) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function WeekPicker({
  week,
  onSelect,
  placeholder = "Select week",
  className,
  disabled = false,
}: WeekPickerProps) {
  // Custom day click handler to select a full week
  const handleDayClick = React.useCallback((day: Date | undefined) => {
    if (!day) {
      onSelect(undefined)
      return
    }

    // Calculate the start (Sunday) and end (Saturday) of the week
    const start = startOfWeek(day, { weekStartsOn: 0 })
    const end = endOfWeek(day, { weekStartsOn: 0 })
    
    onSelect({ from: start, to: end })
  }, [onSelect])
  
  // Custom modifiers to highlight the selected week
  const modifiers = React.useMemo(() => {
    if (!week?.from || !week?.to) return { selectedWeek: [] }
    
    return {
      selectedWeek: (day: Date) => {
        return isWithinInterval(day, { start: week.from!, end: week.to! })
      }
    }
  }, [week])
  
  // Custom modifier styles
  const modifiersStyles = {
    selectedWeek: {
      backgroundColor: 'var(--selected-week-background, hsl(var(--primary) / 0.1))',
    }
  }
  
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !week && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {week?.from ? (
            <>
              {format(week.from, "MMM d")} - {format(week.to || week.from, "MMM d, yyyy")}
            </>
          ) : (
            <span>{placeholder}</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={week?.from}
          onSelect={handleDayClick}
          initialFocus
          modifiers={modifiers}
          modifiersStyles={modifiersStyles}
        />
      </PopoverContent>
    </Popover>
  )
} 