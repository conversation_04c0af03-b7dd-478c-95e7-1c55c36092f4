import { z } from 'zod';
import { UserWithRelationsSchema, UserPartialWithRelationsSchema } from './UserSchema'
import type { UserWithRelations, UserPartialWithRelations } from './UserSchema'
import { PomodoroSessionWithRelationsSchema, PomodoroSessionPartialWithRelationsSchema } from './PomodoroSessionSchema'
import type { PomodoroSessionWithRelations, PomodoroSessionPartialWithRelations } from './PomodoroSessionSchema'

/////////////////////////////////////////
// TASK SCHEMA
/////////////////////////////////////////

export const TaskSchema = z.object({
  id: z.string().cuid(),
  title: z.string(),
  completed: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  userId: z.string(),
})

export type Task = z.infer<typeof TaskSchema>

/////////////////////////////////////////
// TASK PARTIAL SCHEMA
/////////////////////////////////////////

export const TaskPartialSchema = TaskSchema.partial()

export type TaskPartial = z.infer<typeof TaskPartialSchema>

/////////////////////////////////////////
// TASK RELATION SCHEMA
/////////////////////////////////////////

export type TaskRelations = {
  user: UserWithRelations;
  pomodoroSessions: PomodoroSessionWithRelations[];
};

export type TaskWithRelations = z.infer<typeof TaskSchema> & TaskRelations

export const TaskWithRelationsSchema: z.ZodType<TaskWithRelations> = TaskSchema.merge(z.object({
  user: z.lazy(() => UserWithRelationsSchema),
  pomodoroSessions: z.lazy(() => PomodoroSessionWithRelationsSchema).array(),
}))

/////////////////////////////////////////
// TASK PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type TaskPartialRelations = {
  user?: UserPartialWithRelations;
  pomodoroSessions?: PomodoroSessionPartialWithRelations[];
};

export type TaskPartialWithRelations = z.infer<typeof TaskPartialSchema> & TaskPartialRelations

export const TaskPartialWithRelationsSchema: z.ZodType<TaskPartialWithRelations> = TaskPartialSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  pomodoroSessions: z.lazy(() => PomodoroSessionPartialWithRelationsSchema).array(),
})).partial()

export type TaskWithPartialRelations = z.infer<typeof TaskSchema> & TaskPartialRelations

export const TaskWithPartialRelationsSchema: z.ZodType<TaskWithPartialRelations> = TaskSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  pomodoroSessions: z.lazy(() => PomodoroSessionPartialWithRelationsSchema).array(),
}).partial())

export default TaskSchema;
