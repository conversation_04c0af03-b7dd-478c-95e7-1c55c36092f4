"use client";

import { useEffect, useRef, useState, useCallback } from "react";
import { useAudioStore } from "@/lib/audio-store";
import { useGetMusics } from "@schemas/Music/music-query";
import { useGetNatureSounds } from "@schemas/Natural/nature-sound-query";
import { Button } from "@/components/ui/button";
import { Pause, Play, Volume2, VolumeX, X } from "lucide-react";
import { Slider } from "@/components/ui/slider";
import { cn } from "@/lib/utils";

export function AudioPlayer() {
  const { selectedAudioId, setSelectedAudioId } = useAudioStore();
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(80);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  const { data: musics } = useGetMusics();
  const { data: natureSounds } = useGetNatureSounds();

  // Find the selected audio
  const selectedMusic = musics?.find(music => music.id === selectedAudioId);
  const selectedNatureSound = natureSounds?.find(sound => sound.id === selectedAudioId);
  const selectedAudioSrc = selectedMusic?.src || selectedNatureSound?.src;
  const selectedAudioTitle = selectedMusic?.title || selectedNatureSound?.title || "Unknown Track";

  // Handle time update event
  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  }, []);

  // Handle loaded metadata event
  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  }, []);

  // Handle play/pause toggle
  const handlePlayPause = useCallback(() => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play().catch(error => {
        console.error("Play failed:", error);
      });
    }
  }, [isPlaying]);

  // Handle volume toggle
  const handleVolumeToggle = useCallback(() => {
    setIsMuted(!isMuted);
  }, [isMuted]);

  // Handle seeking in the timeline
  const handleSeek = useCallback((value: number[]) => {
    const newTime = value[0];
    if (audioRef.current) {
      audioRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  }, []);

  // Handle close player
  const handleClose = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
    }
    setSelectedAudioId("");
  }, [setSelectedAudioId]);

  // Create/update audio element and set up event listeners
  useEffect(() => {
    if (!selectedAudioSrc) {
      setIsPlaying(false);
      return;
    }

    // Create audio element if it doesn't exist
    if (!audioRef.current) {
      audioRef.current = new Audio();

      // Set up event listeners for the audio element
      audioRef.current.addEventListener("play", () => setIsPlaying(true));
      audioRef.current.addEventListener("pause", () => setIsPlaying(false));
      audioRef.current.addEventListener("ended", () => {
        setIsPlaying(false);
        setSelectedAudioId("");
      });
      audioRef.current.addEventListener("timeupdate", handleTimeUpdate);
      audioRef.current.addEventListener("loadedmetadata", handleLoadedMetadata);
    }

    // Update audio source if it changed
    if (audioRef.current.src !== selectedAudioSrc) {
      audioRef.current.src = selectedAudioSrc;
      audioRef.current.load();
    }

    // Play the audio
    const playPromise = audioRef.current.play();

    // Handle autoplay restrictions
    if (playPromise !== undefined) {
      playPromise.catch(error => {
        console.error("Autoplay prevented:", error);
        setIsPlaying(false);
      });
    }

    // Update volume & muted state
    audioRef.current.volume = isMuted ? 0 : volume / 100;

    // Cleanup function
    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener("play", () => setIsPlaying(true));
        audioRef.current.removeEventListener("pause", () => setIsPlaying(false));
        audioRef.current.removeEventListener("ended", () => {
          setIsPlaying(false);
          setSelectedAudioId("");
        });
        audioRef.current.removeEventListener("timeupdate", handleTimeUpdate);
        audioRef.current.removeEventListener("loadedmetadata", handleLoadedMetadata);
        audioRef.current.pause();
      }
    };
  }, [selectedAudioId, selectedAudioSrc, setSelectedAudioId, isMuted, volume, handleTimeUpdate, handleLoadedMetadata]);

  // Update volume when it changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume / 100;
    }
  }, [volume, isMuted]);

  // Format time (seconds to MM:SS)
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Don't render anything if no audio is selected
  if (!selectedAudioId || !selectedAudioSrc) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-sm border-t shadow-lg animate-in slide-in-from-bottom-2 duration-300">
      <div className="container mx-auto max-w-4xl px-4 py-3">
        <div className="flex items-center gap-4">
          {/* Track info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3">
              {/* Play/pause button */}
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "h-10 w-10 rounded-full shrink-0 transition-all duration-200 hover:scale-105",
                  isPlaying
                    ? "bg-primary text-primary-foreground hover:bg-primary/90 shadow-md"
                    : "hover:bg-accent"
                )}
                onClick={handlePlayPause}
                aria-label={isPlaying ? "Pause" : "Play"}
              >
                {isPlaying ? (
                  <Pause className="h-5 w-5" />
                ) : (
                  <Play className="h-5 w-5 ml-0.5" />
                )}
              </Button>

              {/* Track title */}
              <div className="min-w-0 flex-1">
                <div className="truncate font-medium text-sm leading-tight">
                  {selectedAudioTitle}
                </div>
                <div className="flex items-center gap-2 text-xs text-muted-foreground mt-0.5">
                  <span>{formatTime(currentTime)}</span>
                  <span>/</span>
                  <span>{formatTime(duration)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Progress bar - hidden on mobile */}
          <div className="hidden md:flex flex-1 max-w-md items-center gap-3">
            <Slider
              value={[currentTime]}
              min={0}
              max={duration || 100}
              step={0.1}
              onValueChange={handleSeek}
              className="cursor-pointer"
              aria-label="Seek audio position"
            />
          </div>

          {/* Volume controls */}
          <div className="flex items-center gap-2 shrink-0">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 hover:bg-accent transition-colors"
              onClick={handleVolumeToggle}
              aria-label={isMuted ? "Unmute" : "Mute"}
            >
              {isMuted ? (
                <VolumeX className="h-4 w-4" />
              ) : (
                <Volume2 className="h-4 w-4" />
              )}
            </Button>
            <Slider
              value={[isMuted ? 0 : volume]}
              min={0}
              max={100}
              step={1}
              onValueChange={(value) => {
                setVolume(value[0]);
                if (value[0] > 0 && isMuted) {
                  setIsMuted(false);
                }
              }}
              className="w-20"
              aria-label="Volume"
            />
          </div>

          {/* Close button */}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 hover:bg-accent transition-colors shrink-0"
            onClick={handleClose}
            aria-label="Close player"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Mobile progress bar */}
        <div className="md:hidden mt-3 space-y-1">
          <Slider
            value={[currentTime]}
            min={0}
            max={duration || 100}
            step={0.1}
            onValueChange={handleSeek}
            className="cursor-pointer"
            aria-label="Seek audio position"
          />
        </div>
      </div>
    </div>
  );
}