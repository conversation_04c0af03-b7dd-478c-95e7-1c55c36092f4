'use client';

import { useUserStore } from '@/store/userStore';

/**
 * Debug component to check user preferences structure
 * This can be temporarily added to verify the preferences are properly initialized
 */
export function DebugPreferences() {
  const { preferences } = useUserStore();
  
  return (
    <div className="fixed top-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs z-50">
      <h3 className="font-bold mb-2">Debug: User Preferences</h3>
      <pre className="whitespace-pre-wrap">
        {JSON.stringify(preferences, null, 2)}
      </pre>
      <div className="mt-2">
        <p>UI Dismissed: {preferences?.ui?.dismissedSignInPrompt ? 'true' : 'false'}</p>
        <p>UI Object Exists: {preferences?.ui ? 'true' : 'false'}</p>
      </div>
    </div>
  );
}

// Helper function to test preferences access
export function testPreferencesAccess() {
  const store = useUserStore.getState();
  
  console.log('=== Preferences Debug ===');
  console.log('Full preferences:', store.preferences);
  console.log('UI preferences:', store.preferences?.ui);
  console.log('Dismissed status:', store.preferences?.ui?.dismissedSignInPrompt);
  console.log('Safe access:', store.preferences?.ui?.dismissedSignInPrompt ?? false);
  console.log('========================');
  
  return {
    hasPreferences: !!store.preferences,
    hasUI: !!store.preferences?.ui,
    dismissedStatus: store.preferences?.ui?.dismissedSignInPrompt ?? false,
  };
}
