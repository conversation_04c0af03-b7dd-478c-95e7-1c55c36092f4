---
description: 
globs: 
alwaysApply: false
---
# Pomodoro 365 Project Overview

## Architecture Overview
Pomodoro 365 is a modern web application built with Next.js 15 (App Router), React 19, TypeScript, and Tailwind CSS. It implements the Pomodoro Technique with customizable timers and ambient video backgrounds for focus.

## Main Directories
- [src/app](mdc:src/app) - Next.js App Router pages
- [src/components](mdc:src/components) - React components
- [src/lib](mdc:src/lib) - Utilities and state management
- [src/hooks](mdc:src/hooks) - Custom React hooks

## Key Tech Stack
- Next.js 15 with App Router
- React 19
- TypeScript
- Tailwind CSS
- Zustand for state management
- Shadcn UI components
- Radix UI primitives
- Lucide React for icons

## Core Functionality
- Customizable Pomodoro timers (work, short break, long break)
- Ambient video backgrounds for focus
- Fullscreen mode
- Adjustable timer positions
- Volume control for background videos
