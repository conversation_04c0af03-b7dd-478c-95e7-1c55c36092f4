import { useEffect, useState, useCallback, useMemo } from 'react';
import { useUserStore } from '@/store/userStore';
import { useLocalTaskStore, populateTaskWithSessions, type LocalTask } from '@/lib/local-task-store';
import { useLocalPomodoroStore } from '@/lib/local-pomodoro-store';
import { 
  useGetTasks, 
  useCreateTask, 
  useUpdateTask, 
  useDeleteTask,
  type GetTasks_ResponseTypeSuccess 
} from '@schemas/Tasks/task-query';
import { toast } from 'sonner';
import { useCoordinatedSync } from './useCoordinatedSync';

// Unified task interface that works for both local and remote tasks
export interface UnifiedTask {
  id: string;
  title: string;
  completed: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
  isLocal: boolean; // Flag to distinguish local vs remote tasks
  // Optional fields from remote tasks
  userId?: string;
  pomodoroSessions?: any[];
  // Flag for optimistic updates
  isOptimistic?: boolean;
}

interface UseTaskManagementOptions {
  filters?: {
    completed?: boolean;
    searchTerm?: string;
  };
}

export function useTaskManagement(options?: UseTaskManagementOptions) {
  const { isAuthenticated } = useUserStore();
  
  // State for optimistic updates
  const [optimisticTasks, setOptimisticTasks] = useState<UnifiedTask[]>([]);

  // Use coordinated sync instead of individual transfers
  const { syncStatus } = useCoordinatedSync();

  // Local task store functions
  const {
    addTask: addLocalTask,
    updateTask: updateLocalTask,
    deleteTask: deleteLocalTask,
    getFilteredTasks: getFilteredLocalTasks,
    getTaskStats,
  } = useLocalTaskStore();

  // Local pomodoro store to get sessions for linking
  const { sessions: localSessions } = useLocalPomodoroStore();

  // Remote task queries and mutations
  const { 
    data: remoteTasks = [], 
    isLoading: isLoadingRemote, 
    error: remoteError 
  } = useGetTasks({
    completed: options?.filters?.completed
  }, {
    enabled: isAuthenticated
  });

  const createTaskMutation = useCreateTask();
  const updateTaskMutation = useUpdateTask();
  const deleteTaskMutation = useDeleteTask();

  // Get local tasks based on filters and populate with sessions
  const localTasks = useMemo(() => {
    if (isAuthenticated) return []; // Don't show local tasks when authenticated
    
    const filteredTasks = getFilteredLocalTasks({
      completed: options?.filters?.completed,
      searchTerm: options?.filters?.searchTerm
    });

    // Populate each task with its associated pomodoro sessions
    return filteredTasks.map(task => populateTaskWithSessions(task, localSessions));
  }, [isAuthenticated, getFilteredLocalTasks, options?.filters, localSessions]);

  // Combine and format all tasks including optimistic updates
  const allTasks: UnifiedTask[] = useMemo(() => {
    const tasks: UnifiedTask[] = [];

    // Add local tasks (only when not authenticated)
    if (!isAuthenticated) {
      localTasks.forEach(task => {
        tasks.push({
          ...task,
          isLocal: true,
          createdAt: new Date(task.createdAt),
          updatedAt: new Date(task.updatedAt),
        });
      });
    } else {
      // Add remote tasks (when authenticated)
      remoteTasks.forEach(task => {
        tasks.push({
          ...task,
          isLocal: false,
          createdAt: task.createdAt,
          updatedAt: task.updatedAt,
        });
      });
    }

    // Add optimistic tasks for authenticated users
    if (isAuthenticated) {
      optimisticTasks.forEach(task => {
        // Only add if not already in remote tasks (avoid duplicates)
        if (!tasks.find(t => t.id === task.id)) {
          tasks.push(task);
        }
      });
    }

    // Apply search filter
    if (options?.filters?.searchTerm) {
      const searchLower = options.filters.searchTerm.toLowerCase();
      return tasks.filter(task =>
        task.title.toLowerCase().includes(searchLower)
      );
    }

    // Sort tasks: incomplete first, then by creation date (oldest first)
    return tasks.sort((a, b) => {
      // First, sort by completion status (incomplete tasks first)
      if (a.completed !== b.completed) {
        return a.completed ? 1 : -1;
      }
      
      // Within same completion status, sort by creation date (oldest first)
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();
      return dateA - dateB;
    });
  }, [isAuthenticated, localTasks, remoteTasks, optimisticTasks, options?.filters?.searchTerm]);

  // Helper to generate optimistic task ID
  const generateOptimisticId = () => `optimistic_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Create task function with improved optimistic updates
  const createTask = useCallback(async (taskData: { title: string; completed?: boolean }) => {
    const trimmedTitle = taskData.title.trim();
    if (!trimmedTitle) {
      throw new Error('Task title cannot be empty');
    }

    if (isAuthenticated) {
      // Create optimistic task for immediate UI update
      const optimisticTask: UnifiedTask = {
        id: generateOptimisticId(),
        title: trimmedTitle,
        completed: taskData.completed || false,
        createdAt: new Date(),
        updatedAt: new Date(),
        isLocal: false,
        isOptimistic: true,
        pomodoroSessions: []
      };

      // Add optimistic task immediately to UI
      setOptimisticTasks(prev => [optimisticTask, ...prev]);

      try {
        // Create remote task
        const result = await createTaskMutation.mutateAsync({
          form: {
            title: trimmedTitle,
            completed: taskData.completed?.toString() || 'false'
          }
        });

        // Remove optimistic task once real task is created
        // We use a shorter delay for better responsiveness
        setTimeout(() => {
          setOptimisticTasks(prev => prev.filter(task => task.id !== optimisticTask.id));
        }, 300); // Shorter delay for better responsiveness

        return result;
      } catch (error) {
        // Remove optimistic task on error
        setOptimisticTasks(prev => prev.filter(task => task.id !== optimisticTask.id));
        throw error;
      }
    } else {
      // Create local task only when not authenticated
      addLocalTask({
        title: trimmedTitle,
        completed: taskData.completed || false
      });
      
      // Show success message for local tasks
      toast.success('Task created successfully');
    }
  }, [isAuthenticated, createTaskMutation, addLocalTask]);

  // Update task function with optimistic updates
  const updateTask = useCallback(async (taskId: string, updates: { title?: string; completed?: boolean }) => {
    const task = allTasks.find(t => t.id === taskId);
    if (!task) {
      throw new Error('Task not found');
    }

    // Skip optimistic update for optimistic tasks to avoid conflicts
    if (task.isOptimistic) {
      return;
    }

    if (task.isLocal) {
      // Update local task
      updateLocalTask(taskId, updates);
      toast.success('Task updated successfully');
    } else {
      try {
        // For remote tasks, apply optimistic update first
        if (updates.completed !== undefined) {
          // This will be handled by React Query's optimistic updates
          // We don't need to manage state manually here since the mutation
          // will invalidate and refetch the data
        }

        // Update remote task
        await updateTaskMutation.mutateAsync({
          form: {
            ...(updates.title && { title: updates.title }),
            ...(updates.completed !== undefined && { completed: updates.completed.toString() })
          },
          param: { id: taskId }
        });
      } catch (error) {
        console.error('Error updating task:', error);
        throw error;
      }
    }
  }, [allTasks, updateLocalTask, updateTaskMutation]);

  // Delete task function
  const deleteTask = useCallback(async (taskId: string) => {
    const task = allTasks.find(t => t.id === taskId);
    if (!task) {
      throw new Error('Task not found');
    }

    // Skip delete for optimistic tasks - just remove from optimistic state
    if (task.isOptimistic) {
      setOptimisticTasks(prev => prev.filter(t => t.id !== taskId));
      return;
    }

    if (task.isLocal) {
      // Delete local task
      deleteLocalTask(taskId);
      toast.success('Task deleted successfully');
    } else {
      // Delete remote task
      await deleteTaskMutation.mutateAsync({ id: taskId });
    }
  }, [allTasks, deleteLocalTask, deleteTaskMutation]);

  // Clear stale optimistic tasks periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setOptimisticTasks(prev => {
        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
        return prev.filter(task => {
          const taskTime = new Date(task.createdAt).getTime();
          return taskTime > fiveMinutesAgo;
        });
      });
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, []);

  // Calculate task statistics including optimistic tasks
  const taskStats = useMemo(() => {
    if (!isAuthenticated) {
      return getTaskStats();
    } else {
      // Include optimistic tasks in stats for immediate feedback
      const allTasksWithOptimistic = [...remoteTasks, ...optimisticTasks.filter(task => !remoteTasks.find(rt => rt.id === task.id))];
      
      const totalTasks = allTasksWithOptimistic.length;
      const completedTasks = allTasksWithOptimistic.filter(task => task.completed).length;
      const pendingTasks = totalTasks - completedTasks;
      const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
      
      // For today's tasks, we need to filter by creation date
      const today = new Date().toDateString();
      const todayCreated = allTasksWithOptimistic.filter(task => {
        const taskDate = new Date(task.createdAt).toDateString();
        return taskDate === today;
      }).length;

      return {
        totalTasks,
        completedTasks,
        pendingTasks,
        todayCreated,
        completionRate,
      };
    }
  }, [isAuthenticated, getTaskStats, remoteTasks, optimisticTasks]);

  return {
    tasks: allTasks,
    isLoading: isLoadingRemote && allTasks.length === 0, // Only show loading if no tasks at all
    error: remoteError,
    createTask,
    updateTask,
    deleteTask,
    taskStats,
    isCreating: createTaskMutation.isPending,
    isUpdating: updateTaskMutation.isPending,
    isDeleting: deleteTaskMutation.isPending,
    syncStatus,
  };
} 