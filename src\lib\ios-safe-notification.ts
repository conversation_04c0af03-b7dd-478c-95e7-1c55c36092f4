'use client';

/**
 * iOS-Safe Notification Wrapper
 * Provides a safe interface for notifications that works on iOS and other platforms
 */

export interface SafeNotificationOptions {
  title: string;
  body: string;
  icon?: string;
  sound?: string;
  fallbackToSound?: boolean;
}

export interface NotificationCapabilities {
  hasNotificationAPI: boolean;
  canAccessPermission: boolean;
  currentPermission: NotificationPermission;
  canRequestPermission: boolean;
  isIOS: boolean;
}

/**
 * Check notification capabilities safely
 */
export function checkNotificationCapabilities(): NotificationCapabilities {
  const isIOS = typeof window !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);
  
  let hasNotificationAPI = false;
  let canAccessPermission = false;
  let currentPermission: NotificationPermission = 'denied';
  let canRequestPermission = false;

  if (typeof window !== 'undefined') {
    // Check if Notification API exists
    hasNotificationAPI = 'Notification' in window;
    
    if (hasNotificationAPI) {
      try {
        // Test if we can access Notification.permission
        currentPermission = Notification.permission;
        canAccessPermission = true;
        
        // Test if we can call requestPermission
        canRequestPermission = typeof Notification.requestPermission === 'function';
      } catch (error) {
        console.warn('Notification API exists but is not accessible:', error);
        canAccessPermission = false;
        canRequestPermission = false;
      }
    }
  }

  return {
    hasNotificationAPI,
    canAccessPermission,
    currentPermission,
    canRequestPermission,
    isIOS
  };
}

/**
 * Safely request notification permission
 */
export async function safeRequestNotificationPermission(): Promise<NotificationPermission> {
  const capabilities = checkNotificationCapabilities();
  
  if (!capabilities.hasNotificationAPI || !capabilities.canRequestPermission) {
    console.log('Notification API not available or cannot request permission');
    return 'denied';
  }

  try {
    const permission = await Notification.requestPermission();
    console.log('Notification permission result:', permission);
    return permission;
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return 'denied';
  }
}

/**
 * Safely show a notification with fallbacks
 */
export async function safeShowNotification(options: SafeNotificationOptions): Promise<boolean> {
  const capabilities = checkNotificationCapabilities();
  
  // If on iOS or notification API is not available, just play sound
  if (capabilities.isIOS || !capabilities.hasNotificationAPI || !capabilities.canAccessPermission) {
    console.log('Using sound-only notification (iOS or API not available)');
    if (options.sound && options.fallbackToSound !== false) {
      try {
        const audio = new Audio(options.sound);
        audio.volume = 0.5;
        await audio.play();
        return true;
      } catch (error) {
        console.error('Error playing notification sound:', error);
        return false;
      }
    }
    return false;
  }

  // Check permission
  if (capabilities.currentPermission !== 'granted') {
    if (capabilities.canRequestPermission) {
      const permission = await safeRequestNotificationPermission();
      if (permission !== 'granted') {
        console.log('Notification permission not granted, falling back to sound');
        if (options.sound && options.fallbackToSound !== false) {
          try {
            const audio = new Audio(options.sound);
            audio.volume = 0.5;
            await audio.play();
            return true;
          } catch (error) {
            console.error('Error playing fallback sound:', error);
            return false;
          }
        }
        return false;
      }
    } else {
      console.log('Cannot request notification permission');
      return false;
    }
  }

  // Try to show notification
  try {
    const notification = new Notification(options.title, {
      body: options.body,
      icon: options.icon || '/favicon.ico',
    });

    // Play sound if provided
    if (options.sound) {
      try {
        const audio = new Audio(options.sound);
        audio.volume = 0.3; // Lower volume when showing visual notification
        audio.play().catch(soundError => {
          console.warn('Error playing notification sound:', soundError);
        });
      } catch (soundError) {
        console.warn('Error creating notification audio:', soundError);
      }
    }

    // Auto-close after 5 seconds
    setTimeout(() => {
      try {
        notification.close();
      } catch (closeError) {
        console.warn('Error closing notification:', closeError);
      }
    }, 5000);

    return true;
  } catch (error) {
    console.error('Error showing notification:', error);
    
    // Fallback to sound only
    if (options.sound && options.fallbackToSound !== false) {
      try {
        const audio = new Audio(options.sound);
        audio.volume = 0.5;
        await audio.play();
        return true;
      } catch (soundError) {
        console.error('Error playing fallback sound:', soundError);
        return false;
      }
    }
    
    return false;
  }
}

/**
 * iOS-safe notification for Pomodoro completion
 */
export async function notifyPomodoroComplete(): Promise<boolean> {
  return safeShowNotification({
    title: 'Pomodoro Complete!',
    body: 'Time for a break. Good job!',
    sound: '/notifications/bell.mp3',
    fallbackToSound: true
  });
}

/**
 * iOS-safe notification for break completion
 */
export async function notifyBreakComplete(isLongBreak = false): Promise<boolean> {
  return safeShowNotification({
    title: `${isLongBreak ? 'Long' : 'Short'} Break Complete!`,
    body: 'Time to focus again.',
    sound: '/notifications/bell.mp3',
    fallbackToSound: true
  });
}

/**
 * Test notification functionality
 */
export async function testNotification(): Promise<boolean> {
  return safeShowNotification({
    title: 'Test Notification',
    body: 'This is a test notification to check if everything works.',
    sound: '/notifications/bell.mp3',
    fallbackToSound: true
  });
}

/**
 * Get user-friendly status message
 */
export function getNotificationStatusMessage(): string {
  const capabilities = checkNotificationCapabilities();
  
  if (capabilities.isIOS) {
    return 'iOS device detected. Sound notifications will work, but browser notifications may be limited.';
  }
  
  if (!capabilities.hasNotificationAPI) {
    return 'Browser notifications are not supported. Only sound alerts will work.';
  }
  
  if (!capabilities.canAccessPermission) {
    return 'Notification API is blocked. Only sound alerts will work.';
  }
  
  switch (capabilities.currentPermission) {
    case 'granted':
      return 'Notifications are enabled and working.';
    case 'denied':
      return 'Notifications are blocked. Please enable them in your browser settings.';
    case 'default':
      return 'Click to enable notifications for timer alerts.';
    default:
      return 'Notification status unknown.';
  }
}
