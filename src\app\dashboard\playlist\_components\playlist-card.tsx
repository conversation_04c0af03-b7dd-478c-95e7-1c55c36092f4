"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MoreHorizontal, Play, Music2, Video, Edit, Trash, Clock, Calendar, Heart, Share2, Download } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { useDeleteMusicPlaylistUser } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { toast } from "sonner"
import Image from "next/image"
import { useState } from "react"
import { PlaylistFormSheet } from "./playlist-form-sheet"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import { GetMusicPlaylistsUser_ResponseTypeSuccess } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { useRouter } from "next/navigation"

interface PlaylistCardProps {
  playlist: GetMusicPlaylistsUser_ResponseTypeSuccess[0]
  viewMode: "grid" | "list"
}

export function PlaylistCard({ playlist, viewMode }: PlaylistCardProps) {
  const router = useRouter()
  const deleteMusicPlaylist = useDeleteMusicPlaylistUser()
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)

  const handleDelete = async () => {
    try {
      await deleteMusicPlaylist.mutateAsync({ id: playlist.id })
      toast.success("Playlist deleted successfully")
      setIsDeleteDialogOpen(false)
    } catch (error) {
      console.error(error)
      toast.error("Failed to delete playlist")
    }
  }

  const handlePlay = () => {
    setIsPlaying(!isPlaying)
    // TODO: Implement playlist playing functionality
    toast.success(isPlaying ? "Playlist paused" : "Playing playlist...")
  }

  const handleEditDetails = () => setIsEditSheetOpen(true)

  const handleCardClick = () => {
    router.push(`/dashboard/playlist/${playlist.id}`)
  }

  const handleActionClick = (e: React.MouseEvent) => {
    e.stopPropagation()
  }

  const totalItems = (playlist.videos?.length || 0) + (playlist.musics?.length || 0)
  const estimatedDuration = Math.floor(totalItems * 3.5) // Estimated duration in minutes

  const renderMediaCounts = () => (
    <div className="flex items-center gap-3">
      <div className="flex items-center gap-1">
        <div className="p-0.5 rounded bg-orange-100 dark:bg-orange-900/30">
          <Music2 className="h-2.5 w-2.5 text-orange-600 dark:text-orange-400" />
        </div>
        <span className="text-xs font-medium text-muted-foreground">{playlist.musics?.length || 0}</span>
      </div>
      <div className="flex items-center gap-1">
        <div className="p-0.5 rounded bg-red-100 dark:bg-red-900/30">
          <Video className="h-2.5 w-2.5 text-red-600 dark:text-red-400" />
        </div>
        <span className="text-xs font-medium text-muted-foreground">{playlist.videos?.length || 0}</span>
      </div>
      {estimatedDuration > 0 && (
        <div className="flex items-center gap-1">
          <div className="p-0.5 rounded bg-rose-100 dark:bg-rose-900/30">
            <Clock className="h-2.5 w-2.5 text-rose-600 dark:text-rose-400" />
          </div>
          <span className="text-xs font-medium text-muted-foreground">{estimatedDuration}m</span>
        </div>
      )}
    </div>
  )

  const creationDate = new Date(playlist.createdAt).toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })

  // Compact List View Component
  if (viewMode === "list") {
    return (
      <>
        <motion.div
          layout
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 10 }}
          whileHover={{ scale: 1.005 }}
          transition={{ duration: 0.15 }}
        >
          <Card 
            className="group relative overflow-hidden border-muted/50 hover:border-orange-200 dark:hover:border-orange-800 transition-all duration-200 hover:shadow-md cursor-pointer"
            onClick={handleCardClick}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <CardContent className="p-3">
              <div className="flex items-center gap-3">
                {/* Compact Thumbnail */}
                <div className="relative w-16 h-16 rounded-lg overflow-hidden shrink-0 group-hover:scale-105 transition-transform duration-200">
                  {playlist.imageUrl ? (
                    <Image
                      src={playlist.imageUrl}
                      alt={playlist.name || 'Playlist image'}
                      fill
                      className="object-cover"
                      sizes="64px"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-orange-100 via-red-50 to-rose-100 dark:from-orange-900/40 dark:via-red-900/30 dark:to-rose-900/40 flex items-center justify-center relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-orange-400/20 to-rose-400/20 opacity-50" />
                      <Music2 className="h-6 w-6 text-orange-600/80 dark:text-orange-400/80 relative z-10" />
                    </div>
                  )}
                  

                </div>

                {/* Compact Content */}
                <div className="flex-1 min-w-0 space-y-1">
                  <div>
                    <h3 className="font-semibold line-clamp-1 text-base group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors">
                      {playlist.name}
                    </h3>
                    <p className="text-xs text-muted-foreground line-clamp-1">
                      {playlist.description || "No description provided"}
                    </p>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    {renderMediaCounts()}
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Calendar className="h-2.5 w-2.5" />
                      <span>{creationDate}</span>
                    </div>
                  </div>
                </div>

                {/* Compact Actions */}
                <div className="flex items-center gap-1">
                  
                  <DropdownMenu modal={false}>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7 opacity-70 hover:opacity-100 transition-opacity"
                        onClick={handleActionClick}
                      >
                        <MoreHorizontal className="h-3.5 w-3.5" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-44">
                      <DropdownMenuItem onClick={(e) => {
                        handleActionClick(e)
                        handlePlay()
                      }} className="text-sm">
                        <Play className="mr-2 h-3.5 w-3.5" />
                        Play Playlist
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={handleActionClick} className="text-sm">
                        <Share2 className="mr-2 h-3.5 w-3.5" />
                        Share
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={handleActionClick} className="text-sm">
                        <Download className="mr-2 h-3.5 w-3.5" />
                        Export
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={(e) => {
                        handleActionClick(e)
                        handleEditDetails()
                      }} className="text-sm">
                        <Edit className="mr-2 h-3.5 w-3.5" />
                        Edit Details
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={(e) => {
                          handleActionClick(e)
                          setIsDeleteDialogOpen(true)
                        }}
                        className="text-destructive focus:text-destructive text-sm"
                      >
                        <Trash className="mr-2 h-3.5 w-3.5" />
                        Delete Playlist
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Edit Sheet */}
        <PlaylistFormSheet 
          isOpen={isEditSheetOpen} 
          onOpenChange={setIsEditSheetOpen}
          playlist={playlist}
        />

        {/* Delete Dialog */}
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent className="max-w-sm">
            <AlertDialogHeader className="space-y-3">
              <div className="mx-auto p-2 rounded-full bg-destructive/10">
                <Trash className="h-5 w-5 text-destructive" />
              </div>
              <div className="text-center space-y-1">
                <AlertDialogTitle className="text-lg">Delete Playlist</AlertDialogTitle>
                <AlertDialogDescription className="text-sm">
                  Are you sure you want to delete <span className="font-medium">"{playlist.name}"</span>? 
                  This will remove all {totalItems} tracks.
                </AlertDialogDescription>
              </div>
            </AlertDialogHeader>
            <AlertDialogFooter className="gap-2">
              <AlertDialogCancel className="flex-1">Cancel</AlertDialogCancel>
              <AlertDialogAction 
                onClick={handleDelete} 
                className="flex-1 bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    )
  }

  // Compact Grid View Component
  return (
    <>
      <motion.div
        layout
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        whileHover={{ y: -2 }}
        transition={{ duration: 0.2, ease: "easeOut" }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <Card 
          className={cn(
            "group relative overflow-hidden transition-all duration-200 cursor-pointer border-muted/50",
            "hover:shadow-lg hover:shadow-orange-500/10 hover:border-orange-200 dark:hover:border-orange-800",
            isHovered && "ring-1 ring-orange-500/20"
          )}
          onClick={handleCardClick}
        >
          {/* Compact Thumbnail Section */}
          <div className="relative aspect-[4/3] overflow-hidden">
            {playlist.imageUrl ? (
              <Image
                src={playlist.imageUrl}
                alt={playlist.name || 'Playlist image'}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-orange-100 via-red-50 to-rose-100 dark:from-orange-900/40 dark:via-red-900/30 dark:to-rose-900/40 flex items-center justify-center relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-orange-400/20 to-rose-400/20 opacity-50" />
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,_rgba(251,_146,_60,_0.1),_transparent_50%)]" />
                <Music2 className="h-12 w-12 text-orange-600/60 dark:text-orange-400/60 relative z-10 group-hover:scale-110 transition-transform duration-200" />
              </div>
            )}
            
            {/* Gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-200" />
            


            {/* Compact Stats Badge */}
            <div className="absolute top-2 left-2">
              <Badge variant="secondary" className="bg-black/20 text-white border-0 backdrop-blur-sm text-xs px-2 py-0.5">
                {totalItems}
              </Badge>
            </div>

            {/* Action Menu */}
            <div className="absolute top-2 right-2">
              <DropdownMenu modal={false}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 bg-black/20 hover:bg-black/40 text-white border-0 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-all duration-200"
                    onClick={handleActionClick}
                  >
                    <MoreHorizontal className="h-3.5 w-3.5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-44">
                  <DropdownMenuItem onClick={(e) => {
                    handleActionClick(e)
                    handlePlay()
                  }} className="text-sm">
                    <Play className="mr-2 h-3.5 w-3.5" />
                    Play Playlist
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleActionClick} className="text-sm">
                    <Heart className="mr-2 h-3.5 w-3.5" />
                    Add to Favorites
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleActionClick} className="text-sm">
                    <Share2 className="mr-2 h-3.5 w-3.5" />
                    Share
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleActionClick} className="text-sm">
                    <Download className="mr-2 h-3.5 w-3.5" />
                    Export
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={(e) => {
                    handleActionClick(e)
                    handleEditDetails()
                  }} className="text-sm">
                    <Edit className="mr-2 h-3.5 w-3.5" />
                    Edit Details
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={(e) => {
                      handleActionClick(e)
                      setIsDeleteDialogOpen(true)
                    }}
                    className="text-destructive focus:text-destructive text-sm"
                  >
                    <Trash className="mr-2 h-3.5 w-3.5" />
                    Delete Playlist
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Compact Content Section */}
          <CardContent className="p-3 space-y-2">
            <div className="space-y-1">
              <h3 className="font-semibold text-sm line-clamp-1 group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors">
                {playlist.name}
              </h3>
              <p className="text-xs text-muted-foreground line-clamp-2 leading-relaxed min-h-[2rem]">
                {playlist.description || "A curated collection of focus music to enhance your productivity."}
              </p>
            </div>

            <div className="space-y-2">
              {renderMediaCounts()}
              
              <div className="flex items-center justify-between pt-1 border-t border-border/50">
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Calendar className="h-2.5 w-2.5" />
                  <span>{creationDate}</span>
                </div>
                

              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Edit Sheet */}
      <PlaylistFormSheet 
        isOpen={isEditSheetOpen} 
        onOpenChange={setIsEditSheetOpen}
        playlist={playlist}
      />

      {/* Compact Delete Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent className="max-w-sm">
          <AlertDialogHeader className="space-y-3">
            <div className="mx-auto p-2 rounded-full bg-destructive/10">
              <Trash className="h-5 w-5 text-destructive" />
            </div>
            <div className="text-center space-y-1">
              <AlertDialogTitle className="text-lg">Delete Playlist</AlertDialogTitle>
              <AlertDialogDescription className="text-sm">
                Are you sure you want to delete <span className="font-medium">"{playlist.name}"</span>? 
                This will permanently remove all {totalItems} tracks.
              </AlertDialogDescription>
            </div>
          </AlertDialogHeader>
          <AlertDialogFooter className="gap-2">
            <AlertDialogCancel className="flex-1">Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete} 
              className="flex-1 bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
} 