"use client";

import { useState, useRef } from "react";
import { notFound } from "next/navigation";
import Image from "next/image";
import { useGetVideo, useUpdateVideo } from "@schemas/Video/video-query";
import { PlaylistSelector } from "./PlaylistSelector";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Calendar,
  Edit,
  Eye,
  EyeOff,
  Film,
  Globe,
  ImageIcon,
  Lock,
  Maximize2,
  Pause,
  Play,
  Save,
  Share2,
  Sparkles,
  Tag,
  Trash2,
  X
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { formatDistanceToNow } from "date-fns";
import { ErrorBoundary as ReactErrorBoundary } from "react-error-boundary";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { VideoGenreSelector } from "./VideoGenreSelector";
import { ThumbnailPreview } from "./ThumbnailPreview";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface VideoClientProps {
  videoId: string;
}

function VideoSkeleton() {
  return (
    <div className="container mx-auto py-6 space-y-6 animate-in fade-in-50">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Skeleton className="h-8 w-2/3" />
          <Skeleton className="aspect-video w-full rounded-lg" />
          <Skeleton className="h-20 w-full" />
        </div>
        <div className="space-y-4">
          <Skeleton className="h-7 w-40" />
          <Skeleton className="h-[400px] w-full" />
        </div>
      </div>
    </div>
  );
}

function ErrorFallback({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) {
  return (
    <Alert variant="destructive" className="m-4">
      <AlertTitle>Something went wrong!</AlertTitle>
      <AlertDescription className="mt-2 space-y-2">
        <p>{error.message}</p>
        <Button onClick={resetErrorBoundary} variant="outline" size="sm">
          Try again
        </Button>
      </AlertDescription>
    </Alert>
  );
}

export function VideoClient({ videoId }: VideoClientProps) {
  const { data: video, isLoading } = useGetVideo(videoId);
  const { mutate: updateVideo, isPending: isUpdating } = useUpdateVideo();
  const [isEditing, setIsEditing] = useState(false);
  const [editedVideo, setEditedVideo] = useState<{
    title: string;
    description: string | undefined;
    src: string;
    thumbnail: string;
    isPublic: boolean;
    isPremium: boolean;
    videoGenre: string[];
  } | null>(null);

  // Video player state
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false); // Used in toggleFullscreen
  const [showThumbnail, setShowThumbnail] = useState(true);
  const [showThumbnailDialog, setShowThumbnailDialog] = useState(false);

  if (isLoading) {
    return <VideoSkeleton />;
  }

  if (!video) {
    notFound();
  }

  // Expose playlist data for audio store to use
  const videoInfo = {
    musicPlaylist: video.musicPlaylist,
    naturePlaylist: video.naturePlaylist
  };

  const handleEditClick = () => {
    setEditedVideo({
      title: video.title,
      description: video.description || undefined,
      src: video.src,
      thumbnail: video.thumbnail,
      isPublic: video.isPublic,
      isPremium: video.isPremium || false,
      videoGenre: video.videoGenre || ["MEDITATION"],
    });
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditedVideo(null);
  };

  const handleSaveEdit = () => {
    if (!editedVideo) return;

    // Convert to the expected form format
    const formData = {
      title: editedVideo.title,
      description: editedVideo.description,
      src: editedVideo.src,
      thumbnail: editedVideo.thumbnail,
      isPublic: String(editedVideo.isPublic),
      isPremium: String(editedVideo.isPremium),
      videoGenre: editedVideo.videoGenre,
    };

    updateVideo(
      {
        form: formData,
        param: { id: videoId }
      },
      {
        onSuccess: () => {
          setIsEditing(false);
          setEditedVideo(null);
          toast.success("Video updated successfully");
        },
        onError: (error) => {
          toast.error(`Failed to update video: ${error.message}`);
        }
      }
    );
  };

  const handleInputChange = (field: string, value: string | boolean | string[]) => {
    if (!editedVideo) return;

    setEditedVideo({
      ...editedVideo,
      [field]: value
    });
  };

  const renderVideoHeader = () => {
    if (isEditing && editedVideo) {
      return (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Input
              value={editedVideo.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              className="text-2xl font-bold mr-2 "
              placeholder="Video title"
            />
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                // size="sm"
                onClick={handleCancelEdit}
                disabled={isUpdating}
              >
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
              <Button
                variant="default"
                // size="sm"
                onClick={handleSaveEdit}
                disabled={isUpdating}
              >
                <Save className="h-4 w-4 mr-1" />
                {isUpdating ? 'Saving...' : 'Save'}
              </Button>
            </div>
          </div>
          <div className="flex flex-wrap items-center gap-2 text-sm">
            <Badge variant={editedVideo.isPublic ? "default" : "secondary"}>
              {editedVideo.isPublic ?
                <><Globe className="h-3 w-3 mr-1" /> Public</> :
                <><Lock className="h-3 w-3 mr-1" /> Private</>
              }
            </Badge>
            <Badge variant={editedVideo.isPremium ? "default" : "secondary"}>
              {editedVideo.isPremium ?
                <><Sparkles className="h-3 w-3 mr-1" /> Premium</> :
                <><Film className="h-3 w-3 mr-1" /> Free</>
              }
            </Badge>
            {video.createdAt && (
              <span className="flex items-center gap-1 text-muted-foreground">
                <Calendar className="h-3 w-3" />
                {formatDistanceToNow(new Date(video.createdAt), { addSuffix: true })}
              </span>
            )}
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">{video.title}</h1>
          <Button
            variant="outline"
            size="sm"
            onClick={handleEditClick}
          >
            <Edit className="h-4 w-4 mr-1" />
            Edit
          </Button>
        </div>
        <div className="flex flex-wrap items-center gap-2 text-sm">
          <Badge variant={video.isPublic ? "default" : "secondary"}>
            {video.isPublic ?
              <><Globe className="h-3 w-3 mr-1" /> Public</> :
              <><Lock className="h-3 w-3 mr-1" /> Private</>
            }
          </Badge>
          <Badge variant={video.isPremium ? "default" : "secondary"}>
            {video.isPremium ?
              <><Sparkles className="h-3 w-3 mr-1" /> Premium</> :
              <><Film className="h-3 w-3 mr-1" /> Free</>
            }
          </Badge>
          {video.videoGenre && video.videoGenre.length > 0 && (
            <Badge variant="outline" className="flex items-center gap-1">
              <Tag className="h-3 w-3" />
              {video.videoGenre.join(', ')}
            </Badge>
          )}
          {video.createdAt && (
            <span className="flex items-center gap-1 text-muted-foreground">
              <Calendar className="h-3 w-3" />
              {formatDistanceToNow(new Date(video.createdAt), { addSuffix: true })}
            </span>
          )}
          <Button variant="ghost" size="icon" className="ml-auto h-7 w-7">
            <Share2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  };

  const togglePlayPause = () => {
    if (!videoRef.current) return;

    if (videoRef.current.paused) {
      videoRef.current.play();
      setIsPlaying(true);
      setShowThumbnail(false);
    } else {
      videoRef.current.pause();
      setIsPlaying(false);
    }
  };

  const toggleFullscreen = () => {
    if (!videoRef.current) return;

    if (!document.fullscreenElement) {
      videoRef.current.requestFullscreen().then(() => {
        setIsFullscreen(true);
      }).catch(err => {
        console.error(`Error attempting to enable fullscreen: ${err.message}`);
      });
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const handleVideoEnded = () => {
    setIsPlaying(false);
    setShowThumbnail(true);
  };

  const openThumbnailDialog = () => {
    setShowThumbnailDialog(true);
  };

  const renderVideoPlayer = () => {
    if (isEditing && editedVideo) {
      return (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Thumbnail preview */}
            <div className="space-y-2">
              <Label htmlFor="video-thumbnail">Thumbnail</Label>
              <ThumbnailPreview
                src={editedVideo.thumbnail}
                alt={editedVideo.title}
                aspectRatio="video"
                className="w-full"
                showPlayButton={false}
                isPremium={editedVideo.isPremium}
                onClick={openThumbnailDialog}
              />
              <Input
                id="video-thumbnail"
                value={editedVideo.thumbnail}
                onChange={(e) => handleInputChange('thumbnail', e.target.value)}
                placeholder="https://example.com/thumbnail.jpg"
                className="mt-2"
              />
            </div>

            {/* Video source */}
            <div className="space-y-2">
              <Label htmlFor="video-src">Video Source</Label>
              <div className="aspect-video rounded-lg overflow-hidden bg-muted/30 relative">
                <video
                  src={editedVideo.src}
                  poster={editedVideo.thumbnail}
                  controls
                  className="w-full h-full object-cover"
                  playsInline
                  preload="metadata"
                />
              </div>
              <Input
                id="video-src"
                value={editedVideo.src}
                onChange={(e) => handleInputChange('src', e.target.value)}
                placeholder="https://example.com/video.mp4"
                className="mt-2"
              />
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* Video player with thumbnail overlay */}
        <div className="relative rounded-lg overflow-hidden bg-muted/30 group">
          {/* Thumbnail overlay */}
          {showThumbnail && (
            <div className="absolute inset-0 z-10">
              <ThumbnailPreview
                src={video.thumbnail}
                alt={video.title}
                aspectRatio="video"
                className="w-full h-full"
                isPremium={video.isPremium}
                onClick={togglePlayPause}
              />
            </div>
          )}

          {/* Video element */}
          <video
            ref={videoRef}
            src={video.src}
            poster={video.thumbnail}
            className="w-full h-full object-cover aspect-video"
            playsInline
            preload="metadata"
            onEnded={handleVideoEnded}
            controls={!showThumbnail}
          />

          {/* Custom controls overlay */}
          <div className="absolute bottom-4 right-4 z-20 flex items-center gap-2">
            {!showThumbnail && (
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full bg-background/20 backdrop-blur-sm hover:bg-background/40 text-white"
                onClick={togglePlayPause}
              >
                {isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </Button>
            )}
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 rounded-full bg-background/20 backdrop-blur-sm hover:bg-background/40 text-white"
              onClick={toggleFullscreen}
            >
              {isFullscreen ? (
                <Maximize2 className="h-4 w-4 rotate-45" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Thumbnail preview section */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Thumbnail</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={openThumbnailDialog}
            className="flex items-center gap-1"
          >
            <ImageIcon className="h-4 w-4" />
            View Full Size
          </Button>
        </div>

        {/* Thumbnail dialog */}
        <Dialog open={showThumbnailDialog} onOpenChange={setShowThumbnailDialog}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Video Thumbnail</DialogTitle>
              <DialogDescription>
                {video.title}
              </DialogDescription>
            </DialogHeader>
            <div className="mt-4 overflow-hidden rounded-lg">
              <div className="relative w-full aspect-video">
                <Image
                  src={video.thumbnail}
                  alt={video.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 1200px) 100vw, 1200px"
                />
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    );
  };

  const renderVideoDescription = () => {
    if (isEditing && editedVideo) {
      return (
        <div className="space-y-2">
          <Label htmlFor="video-description">Description</Label>
          <Textarea
            id="video-description"
            value={editedVideo.description || ''}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Enter video description"
            className="min-h-[120px]"
          />
        </div>
      );
    }

    if (video.description) {
      return (
        <div className="prose prose-sm max-w-none dark:prose-invert">
          <p className="text-muted-foreground whitespace-pre-wrap">
            {video.description}
          </p>
        </div>
      );
    }

    return (
      <div className="text-muted-foreground italic">
        No description provided.
      </div>
    );
  };

  const renderVideoSettings = () => {
    if (isEditing && editedVideo) {
      return (
        <Card>
          <CardHeader>
            <CardTitle>Video Settings</CardTitle>
            <CardDescription>Configure visibility and access settings</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="video-public">Public Video</Label>
                <p className="text-sm text-muted-foreground">
                  Make this video visible to all users
                </p>
              </div>
              <Switch
                id="video-public"
                checked={editedVideo.isPublic}
                onCheckedChange={(checked) => handleInputChange('isPublic', checked)}
              />
            </div>
            <Separator />
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="video-premium">Premium Content</Label>
                <p className="text-sm text-muted-foreground">
                  Restrict this video to premium users only
                </p>
              </div>
              <Switch
                id="video-premium"
                checked={editedVideo.isPremium}
                onCheckedChange={(checked) => handleInputChange('isPremium', checked)}
              />
            </div>
            <Separator />
            <div className="space-y-2">
              <Label>Video Genre</Label>
              <VideoGenreSelector
                selectedGenres={editedVideo.videoGenre}
                onChange={(genres: string[]) => handleInputChange('videoGenre', genres)}
              />
            </div>
          </CardContent>
        </Card>
      );
    }

    return (
      <Card>
        <CardHeader>
          <CardTitle>Video Settings</CardTitle>
          <CardDescription>Visibility and access settings</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Visibility</Label>
              <p className="text-sm text-muted-foreground">
                Who can see this video
              </p>
            </div>
            <Badge variant={video.isPublic ? "default" : "outline"}>
              {video.isPublic ?
                <><Eye className="h-3 w-3 mr-1" /> Public</> :
                <><EyeOff className="h-3 w-3 mr-1" /> Private</>
              }
            </Badge>
          </div>
          <Separator />
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Access Level</Label>
              <p className="text-sm text-muted-foreground">
                Subscription requirement
              </p>
            </div>
            <Badge variant={video.isPremium ? "default" : "outline"}>
              {video.isPremium ?
                <><Sparkles className="h-3 w-3 mr-1" /> Premium</> :
                <><Film className="h-3 w-3 mr-1" /> Free</>
              }
            </Badge>
          </div>
          <Separator />
          <div className="space-y-2">
            <Label>Video Genre</Label>
            <div className="flex flex-wrap gap-2">
              {video.videoGenre && video.videoGenre.length > 0 ? (
                video.videoGenre.map((genre) => (
                  <Badge key={genre} variant="secondary">
                    {genre.charAt(0) + genre.slice(1).toLowerCase()}
                  </Badge>
                ))
              ) : (
                <span className="text-sm text-muted-foreground">No genres specified</span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <ReactErrorBoundary FallbackComponent={ErrorFallback}>
      <div 
        className="container mx-auto py-6 space-y-8 animate-in fade-in-50"
        data-video-info={JSON.stringify(videoInfo)}
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Video Details Section */}
          <div className="lg:col-span-2 space-y-6">
            {renderVideoHeader()}
            {renderVideoPlayer()}

            <Tabs defaultValue="description" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="description">Description</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
              </TabsList>
              <TabsContent value="description" className="mt-4">
                {renderVideoDescription()}
              </TabsContent>
              <TabsContent value="settings" className="mt-4">
                <div className="md:hidden">
                  {renderVideoSettings()}
                </div>
                <div className="hidden md:block">
                  <div className="text-muted-foreground text-center py-4">
                    Video settings are available in the sidebar on larger screens.
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar Section */}
          <div className="space-y-6">
            <div className="sticky top-6">
              <div className="hidden md:block">
                {renderVideoSettings()}
              </div>

              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Playlist Settings</CardTitle>
                  <CardDescription>Assign this video to playlists</CardDescription>
                </CardHeader>
                <CardContent>
                  <PlaylistSelector
                    videoId={videoId}
                    currentMusicPlaylistId={video.musicPlaylistId}
                    currentNaturePlaylistId={video.naturePlaylistId}
                  />
                </CardContent>
              </Card>

              <div className="mt-6">
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" className="w-full">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Video
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. This will permanently delete the
                        video and remove it from any playlists it belongs to.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ReactErrorBoundary>
  );
}