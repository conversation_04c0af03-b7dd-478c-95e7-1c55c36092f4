import { betterAuth, BetterAuthOptions } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import prisma from "@/lib/prisma";
// import { sendEmail } from "@/actions/email";
import { openAPI } from "better-auth/plugins";
import { admin } from "better-auth/plugins";
import { polar } from "@polar-sh/better-auth";
import { Polar } from "@polar-sh/sdk";

// Only initialize Polar client if access token is available
const polarClient = process.env.POLAR_ACCESS_TOKEN 
  ? new Polar({
      accessToken: process.env.POLAR_ACCESS_TOKEN,
      server: process.env.NEXT_PUBLIC_POLAR_ENV as 'sandbox' | 'production'
    })
  : undefined;

export const auth = betterAuth({
  secret: process.env.BETTER_AUTH_SECRET as string,
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  session: {
    expiresIn: 60 * 60 * 24 * 30, // 30 days
    updateAge: 60 * 60 * 24 * 7, // 7 days (every 7 days the session expiration is updated)
    cookieCache: {
      enabled: true,
      maxAge: 5 * 60, // Cache duration in seconds
    },
  },
  user: {
    additionalFields: {
      premium: {
        type: "boolean",
        required: false,
      },
      role: {
        type: "string",
        required: false,
      },
    },
    changeEmail: {
      enabled: true,
      // sendChangeEmailVerification: async ({ newEmail, url }) => {
      //   await sendEmail({
      //     to: newEmail,
      //     subject: "Verify your email change",
      //     text: `Click the link to verify: ${url}`,
      //   });
      // },
    },
  },
  trustedOrigins: ["https://456f-41-56-168-232.ngrok-free.app", "http://localhost:2604"],
  socialProviders: {
    // github: {
    //   enabled: true,
    //   clientId: process.env.GITHUB_CLIENT_ID as string,
    //   clientSecret: process.env.GITHUB_CLIENT_SECRET as string,
    // },
    google: {
      enabled: true,
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    },
  },
  plugins: [
    openAPI(),
    admin({
      impersonationSessionDuration: 60 * 60 * 24 * 7, // 7 days
    }),
    // Only add Polar plugin if client is initialized
    ...(polarClient ? [
      polar({
        client: polarClient,
        // Enable automatic Polar Customer creation on signup
        createCustomerOnSignUp: true,
        // Enable customer portal
        enableCustomerPortal: true, // Deployed under /portal for authenticated users
        // Configure checkout
        checkout: {
            enabled: true,
            products: [
              {
                productId: "788578c8-0fb7-476a-a773-12722785f5e4", // ID of Product from Polar Dashboard
                slug: "sub1" // Custom slug for easy reference in Checkout URL, e.g. /checkout/pro
              },
              {
                productId: "b5489322-18f8-4d30-b326-be0aaf0f0211", // ID of Product from Polar Dashboard
                slug: "sub2" // Custom slug for easy reference in Checkout URL, e.g. /checkout/pro
              }
            ],
            successUrl: "/success?checkout_id={CHECKOUT_ID}"
        },
        // Incoming Webhooks handler will be installed at /polar/webhooks
        webhooks: {
            secret: process.env.POLAR_WEBHOOK_SECRET as string,
            onPayload: async (payload) => {
              console.log("Received Polar webhook payload:", payload);
            },
        }
    })
    ] : [])
  ], // api/auth/reference
  emailAndPassword: {
    enabled: true,
    // requireEmailVerification: true,
    // sendResetPassword: async ({ user, url }) => {
    //   await sendEmail({
    //     to: user.email,
    //     subject: "Reset your password",
    //     text: `Click the link to reset your password: ${url}`,
    //   });
    // },
  },
  // emailVerification: {
  //   sendOnSignUp: true,
  //   autoSignInAfterVerification: true,
  //   sendVerificationEmail: async ({ user, token }) => {
  //     const verificationUrl = `${process.env.BETTER_AUTH_URL}/api/auth/verify-email?token=${token}&callbackURL=${process.env.EMAIL_VERIFICATION_CALLBACK_URL}`;
  //     await sendEmail({
  //       to: user.email,
  //       subject: "Verify your email address",
  //       text: `Click the link to verify your email: ${verificationUrl}`,
  //     });
  //   },
  // }
} satisfies BetterAuthOptions);

export type Session = typeof auth.$Infer.Session;
