'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useGlobalMusicState } from '@/lib/use-global-music-control';
import { useAudioStore } from '@/lib/audio-store';
import { Play, Pause, Volume2, VolumeX } from 'lucide-react';

/**
 * Debug component to visualize the global music control state.
 * This helps developers understand what's happening with audio coordination.
 */
export function GlobalMusicDebug() {
  const globalMusicState = useGlobalMusicState();
  const {
    pauseAllSources,
    setMutualExclusionEnabled,
    setAutoPlay,
  } = useAudioStore();

  const {
    currentActiveSource,
    activeSource,
    allSources,
    mutualExclusionEnabled,
    shouldAutoPlay,
  } = globalMusicState;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Global Music Control Debug</CardTitle>
        <div className="flex gap-2">
          <Badge variant={mutualExclusionEnabled ? "default" : "secondary"}>
            Mutual Exclusion: {mutualExclusionEnabled ? "ON" : "OFF"}
          </Badge>
          <Badge variant={shouldAutoPlay ? "default" : "secondary"}>
            Auto-play: {shouldAutoPlay ? "ON" : "OFF"}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Global Controls */}
        <div className="space-y-2">
          <h3 className="font-medium text-sm">Global Controls</h3>
          <div className="flex gap-2 flex-wrap">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setMutualExclusionEnabled(!mutualExclusionEnabled)}
            >
              Toggle Mutual Exclusion
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setAutoPlay(!shouldAutoPlay)}
            >
              Toggle Auto-play
            </Button>
            <Button
              size="sm"
              variant="destructive"
              onClick={pauseAllSources}
            >
              Pause All Sources
            </Button>
          </div>
        </div>

        {/* Active Source */}
        <div className="space-y-2">
          <h3 className="font-medium text-sm">Currently Active Source</h3>
          {activeSource ? (
            <div className="p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-sm">{activeSource.name}</p>
                  <p className="text-xs text-muted-foreground">
                    Type: {activeSource.type} | ID: {activeSource.id}
                  </p>
                  {activeSource.currentTrack && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Playing: {activeSource.currentTrack.title}
                    </p>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={activeSource.isPlaying ? "default" : "secondary"}>
                    {activeSource.isPlaying ? <Play className="h-3 w-3" /> : <Pause className="h-3 w-3" />}
                  </Badge>
                  <Badge variant="outline">
                    {activeSource.isMuted ? <VolumeX className="h-3 w-3" /> : <Volume2 className="h-3 w-3" />}
                    {activeSource.volume}%
                  </Badge>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">No active source</p>
          )}
        </div>

        {/* All Registered Sources */}
        <div className="space-y-2">
          <h3 className="font-medium text-sm">All Registered Sources ({allSources.length})</h3>
          {allSources.length > 0 ? (
            <div className="space-y-2">
              {allSources.map((source) => (
                <div
                  key={source.id}
                  className={`p-2 rounded-lg border ${
                    source.id === currentActiveSource
                      ? 'border-primary bg-primary/5'
                      : 'border-border bg-card'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="font-medium text-sm truncate">{source.name}</p>
                        {source.id === currentActiveSource && (
                          <Badge variant="default" className="text-xs">ACTIVE</Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {source.type} | {source.id}
                      </p>
                      {source.currentTrack && (
                        <p className="text-xs text-muted-foreground truncate">
                          {source.currentTrack.title}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-1">
                      <Badge
                        variant={source.isPlaying ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {source.isPlaying ? "Playing" : "Paused"}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {source.volume}%
                      </Badge>
                      {source.isMuted && (
                        <Badge variant="destructive" className="text-xs">
                          Muted
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">No sources registered</p>
          )}
        </div>

        {/* Statistics */}
        <div className="space-y-2">
          <h3 className="font-medium text-sm">Statistics</h3>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="p-2 bg-muted/50 rounded">
              <p className="text-muted-foreground">Total Sources</p>
              <p className="font-medium">{allSources.length}</p>
            </div>
            <div className="p-2 bg-muted/50 rounded">
              <p className="text-muted-foreground">Playing Sources</p>
              <p className="font-medium">{allSources.filter(s => s.isPlaying).length}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
