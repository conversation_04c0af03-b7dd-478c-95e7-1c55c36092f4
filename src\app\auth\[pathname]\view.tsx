"use client";

import { AuthCard } from "@daveyplate/better-auth-ui";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export function AuthView({ pathname }: { pathname: string }) {
  const router = useRouter();
  const isSignIn = pathname === "sign-in";

  useEffect(() => {
    // Clear router cache (protected routes)
    router.refresh();
  }, [router]);

  return (
    <div className="w-full">
      <div className="mb-6 space-y-2">
        <h1 className="text-2xl font-bold tracking-tight">
          {isSignIn ? "Welcome back" : "Create an account"}
        </h1>
        <p className="text-gray-500">
          {isSignIn
            ? "Enter your credentials to access your account"
            : "Join Pomodoro 365 to boost your productivity"}
        </p>
      </div>
      <AuthCard
        pathname={pathname}
      />
    </div>
  );
}
