'use client';

import { motion } from 'framer-motion';
import { Check, X, Crown, Zap } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const features = [
  {
    category: "Core Features",
    items: [
      { name: "Pomodoro Timer", free: true, premium: true, lifetime: true, description: "25/5/15 minute intervals" },
      { name: "Custom Timer Settings", free: false, premium: true, lifetime: true, description: "Personalize work and break durations" },
      { name: "Session Tracking", free: "Current day", premium: "30-day history", lifetime: "30-day history", description: "Monitor your productivity" },
      { name: "Notifications", free: "Basic", premium: "Advanced", lifetime: "Advanced", description: "Sound alerts and reminders" },
    ]
  },
  {
    category: "Content & Media",
    items: [
      { name: "Video Backgrounds", free: "3 videos", premium: "Full library", lifetime: "Full library", description: "Immersive focus environments" },
      { name: "Music Library", free: false, premium: true, lifetime: true, description: "Concentration-enhancing tracks" },
      { name: "Natural Sounds", free: false, premium: true, lifetime: true, description: "Ambient audio for focus" },
      { name: "Playlist Creation", free: false, premium: true, lifetime: true, description: "Custom audio collections" },
    ]
  },
  {
    category: "Analytics & Insights",
    items: [
      { name: "Daily Statistics", free: true, premium: true, lifetime: true, description: "Today's focus metrics" },
      { name: "Historical Analytics", free: false, premium: true, lifetime: true, description: "30-day trend analysis" },
      { name: "Productivity Insights", free: false, premium: true, lifetime: true, description: "AI-powered recommendations" },
      { name: "Export Data", free: false, premium: true, lifetime: true, description: "Download your statistics" },
    ]
  },
  {
    category: "Sync & Storage",
    items: [
      { name: "Local Storage", free: true, premium: true, lifetime: true, description: "Save settings locally" },
      { name: "Cloud Sync", free: false, premium: true, lifetime: true, description: "Access across all devices" },
      { name: "Backup & Restore", free: false, premium: true, lifetime: true, description: "Never lose your data" },
      { name: "Multi-device Access", free: "1 device", premium: "Unlimited", lifetime: "Unlimited", description: "Use on all your devices" },
    ]
  },
  {
    category: "Support & Updates",
    items: [
      { name: "Community Support", free: true, premium: true, lifetime: true, description: "Access to user forums" },
      { name: "Priority Support", free: false, premium: true, lifetime: true, description: "Faster response times" },
      { name: "Feature Updates", free: "Basic", premium: "Early access", lifetime: "Early access", description: "Get new features first" },
      { name: "Ad-free Experience", free: false, premium: true, lifetime: true, description: "Distraction-free focus" },
      { name: "Lifetime Access", free: false, premium: false, lifetime: true, description: "Never pay again" },
    ]
  }
];

export const FeatureComparison = () => {
  return (
    <section className="w-full py-16 md:py-20 bg-gradient-to-b from-muted/20 to-background">
      <div className="container mx-auto px-4 max-w-6xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Compare{' '}
            <span className="bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 bg-clip-text text-transparent">
              Features
            </span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            See exactly what's included in each plan to make the best choice for your productivity needs.
          </p>
        </motion.div>

        {/* Column Headers for Desktop */}
        <div className="hidden md:block mb-8">
          <Card>
            <CardContent className="py-4">
              <div className="grid grid-cols-12 gap-4 items-center">
                <div className="col-span-6">
                  <h3 className="font-semibold text-lg">Features</h3>
                </div>
                <div className="col-span-2 text-center">
                  <h3 className="font-semibold text-lg">Free</h3>
                </div>
                <div className="col-span-2 text-center">
                  <h3 className="font-semibold text-lg flex items-center justify-center gap-2">
                    Premium <Crown className="h-4 w-4 text-orange-500" />
                  </h3>
                </div>
                <div className="col-span-2 text-center">
                  <h3 className="font-semibold text-lg flex items-center justify-center gap-2">
                    Lifetime <Zap className="h-4 w-4 text-purple-500" />
                  </h3>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-8">
          {features.map((category, categoryIndex) => (
            <motion.div
              key={category.category}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: categoryIndex * 0.1 }}
              viewport={{ once: true }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl font-semibold text-center md:text-left">
                    {category.category}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {category.items.map((item, itemIndex) => (
                      <div
                        key={itemIndex}
                        className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center py-3 border-b border-border/50 last:border-b-0"
                      >
                        {/* Feature Name & Description */}
                        <div className="md:col-span-6">
                          <div className="font-medium text-foreground">{item.name}</div>
                          <div className="text-sm text-muted-foreground">{item.description}</div>
                        </div>

                        {/* Free Plan */}
                        <div className="md:col-span-2 flex items-center justify-center md:justify-center">
                          <div className="flex items-center gap-2">
                            {typeof item.free === 'boolean' ? (
                              item.free ? (
                                <Check className="h-5 w-5 text-green-500" />
                              ) : (
                                <X className="h-5 w-5 text-muted-foreground" />
                              )
                            ) : (
                              <Badge variant="secondary" className="text-xs">
                                {item.free}
                              </Badge>
                            )}
                            <span className="text-sm font-medium md:hidden">Free</span>
                          </div>
                        </div>

                        {/* Premium Plan */}
                        <div className="md:col-span-2 flex items-center justify-center md:justify-center">
                          <div className="flex items-center gap-2">
                            {typeof item.premium === 'boolean' ? (
                              item.premium ? (
                                <Check className="h-5 w-5 text-orange-500" />
                              ) : (
                                <X className="h-5 w-5 text-muted-foreground" />
                              )
                            ) : (
                              <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs">
                                {item.premium}
                              </Badge>
                            )}
                            <span className="text-sm font-medium md:hidden flex items-center gap-1">
                              Premium <Crown className="h-3 w-3 text-orange-500" />
                            </span>
                          </div>
                        </div>

                        {/* Lifetime Plan */}
                        <div className="md:col-span-2 flex items-center justify-center md:justify-center">
                          <div className="flex items-center gap-2">
                            {typeof item.lifetime === 'boolean' ? (
                              item.lifetime ? (
                                <Check className="h-5 w-5 text-purple-500" />
                              ) : (
                                <X className="h-5 w-5 text-muted-foreground" />
                              )
                            ) : (
                              <Badge className="bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs">
                                {item.lifetime}
                              </Badge>
                            )}
                            <span className="text-sm font-medium md:hidden flex items-center gap-1">
                              Lifetime <Zap className="h-3 w-3 text-purple-500" />
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>


      </div>
    </section>
  );
};
