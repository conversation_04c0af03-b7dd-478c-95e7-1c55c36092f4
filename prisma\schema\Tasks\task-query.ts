import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { InferRequestType, InferResponseType } from "hono";
import client from "@/lib/trpc";
import { toast } from "sonner";

// Get all tasks
export type GetTasks_ResponseType = InferResponseType<
  (typeof client.api.tasks)["$get"],
  200
>;

export type GetTasks_ResponseTypeSuccess = Extract<
  GetTasks_ResponseType,
  { data: object }
>["data"];

export const useGetTasks = (filters?: {
  completed?: boolean;
  status?: "TODO" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED";
  priority?: "LOW" | "MEDIUM" | "HIGH" | "URGENT";
  scheduledDate?: string;
}, options?: {
  enabled?: boolean;
}) => {
  return useQuery({
    queryKey: ["tasks", filters],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      
      if (filters?.completed !== undefined) {
        queryParams.append("completed", String(filters.completed));
      }
      
      if (filters?.status) {
        queryParams.append("status", filters.status);
      }
      
      if (filters?.priority) {
        queryParams.append("priority", filters.priority);
      }
      
      if (filters?.scheduledDate) {
        queryParams.append("scheduledDate", filters.scheduledDate);
      }
      
      const queryString = queryParams.toString();
      
      let response;
      if (queryString) {
        response = await client.api.tasks.$get({
          query: { 
            completed: filters?.completed !== undefined ? String(filters.completed) : undefined,
            status: filters?.status,
            priority: filters?.priority,
            scheduledDate: filters?.scheduledDate
          }
        });
      } else {
        response = await client.api.tasks.$get();
      }
      
      if (!response.ok) {
        throw new Error("Failed to fetch tasks");
      }
      const { data } = await response.json();
      return data;
    },
    enabled: options?.enabled !== false, // Default to true, only disable if explicitly set to false
  });
};

// Get single task
type GetTask_ResponseType = InferResponseType<
  (typeof client.api.tasks)[":id"]["$get"],
  200
>;

export type GetTask_ResponseTypeSuccess = Extract<
  GetTask_ResponseType,
  { data: object }
>["data"];

export const useGetTask = (id?: string) => {
  return useQuery({
    queryKey: ["tasks", { id }],
    queryFn: async () => {
      if (!id) throw new Error("No task ID provided");

      const response = await client.api.tasks[":id"]["$get"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch task");
      }

      const { data } = await response.json();
      return data;
    },
    enabled: !!id,
  });
};

// Create task
interface CreateTaskSuccessResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

type CreateTaskRequest = InferRequestType<
  (typeof client.api.tasks)["$post"]
>;

export const useCreateTask = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation<
    CreateTaskSuccessResponse,
    Error,
    CreateTaskRequest
  >({
    mutationFn: async ({ form }) => {
      const response = await client.api.tasks.$post({ form });

      if (!response.ok) {
        throw new Error("Failed to create task");
      }

      return await response.json();
    },
    onSuccess: () => {
      // Don't show toast for optimistic updates as task is already visible
      // toast.success("Task created successfully");
      queryClient.invalidateQueries({ queryKey: ["tasks"] });
    },
    onError: (error) => {
      toast.error(`Failed to create task: ${error.message}`);
    },
  });

  return mutation;
};

// Update task
type UpdateTask_ResponseType = InferResponseType<
  (typeof client.api.tasks)[":id"]["$patch"],
  200
>;

export type UpdateTask_ResponseTypeSuccess = Extract<
  UpdateTask_ResponseType,
  { data: object }
>["data"];

type UpdateTaskRequest = InferRequestType<
  (typeof client.api.tasks)[":id"]["$patch"]
>;

export const useUpdateTask = () => {
  const queryClient = useQueryClient();
  
  return useMutation<
    UpdateTask_ResponseType,
    Error,
    UpdateTaskRequest
  >({
    mutationFn: async (variables) => {
      const { form, param } = variables;

      if (!param?.id) {
        throw new Error("No task ID provided");
      }

      const response = await client.api.tasks[":id"]["$patch"]({
        form,
        param: { id: param.id },
      });

      if (!response.ok) {
        throw new Error(`Failed to update task. Status: ${response.status}`);
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Task updated successfully");
      
      // Invalidate specific task first
      const taskId = data?.id;
      if (taskId) {
        queryClient.invalidateQueries({ queryKey: ["tasks", { id: taskId }] });
      }
      
      // Then invalidate broader task queries
      queryClient.invalidateQueries({ queryKey: ["tasks"] });
      
      // Also invalidate pomodoro queries if this task has sessions
      queryClient.invalidateQueries({ queryKey: ["pomodoro"] });
    },
    onError: (error) => {
      toast.error(`Failed to update task: ${error.message}`);
    },
  });
};

// Delete task
type DeleteTask_ResponseType = InferResponseType<
  (typeof client.api.tasks)[":id"]["$delete"],
  200
>;

export const useDeleteTask = () => {
  const queryClient = useQueryClient();

  return useMutation<DeleteTask_ResponseType, Error, { id: string }>({
    mutationFn: async ({ id }) => {
      if (!id) throw new Error("No task ID provided");

      const response = await client.api.tasks[":id"]["$delete"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to delete task");
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Task deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["tasks"] });
      queryClient.invalidateQueries({ queryKey: ["pomodoro"] });
    },
    onError: (error) => {
      toast.error(`Failed to delete task: ${error.message}`);
    },
  });
};

// Add interface for bulk transfer
export interface BulkTransferTask {
  localId: string;
  title: string;
  completed: string;
  createdAt: string;
  updatedAt: string;
}

// Bulk transfer local tasks to database
type BulkTransferTasksRequest = InferRequestType<
  (typeof client.api.tasks)["bulk-transfer"]["$post"]
>;

interface TaskMapping {
  originalIndex: number;
  localId: string;
  id: string;
  title: string;
}

export const useBulkTransferTasks = () => {
  const queryClient = useQueryClient();

  return useMutation<
    { data: { 
      transferred: number; 
      skipped: number; 
      taskMapping?: TaskMapping[];
      localToDbMapping?: Record<string, string>;
    } },
    Error,
    { tasks: BulkTransferTask[] }
  >({
    mutationFn: async ({ tasks }) => {
      console.log('Transferring tasks to database:', tasks.length);

      const response = await client.api.tasks["bulk-transfer"].$post({ 
        json: { tasks } 
      });

      if (!response.ok) {
        throw new Error("Failed to transfer local tasks to database");
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success(`Successfully transferred ${data.transferred} tasks to your account`, {
        description: data.skipped > 0 ? `${data.skipped} tasks were skipped (invalid data)` : undefined,
        duration: 5000,
      });
      
      // Invalidate all task queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["tasks"] });
    },
    onError: (error) => {
      toast.error(`Failed to transfer tasks: ${error.message}`);
    },
  });
};