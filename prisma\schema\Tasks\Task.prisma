model Task {
    id          String   @id @default(cuid())
    title       String
    completed   Boolean  @default(false)
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    // Relations
    user             User              @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId           String
    pomodoroSessions PomodoroSession[]

    @@index([userId])
    @@index([completed])
}