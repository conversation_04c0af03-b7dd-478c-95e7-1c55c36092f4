'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw } from 'lucide-react';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log error details for debugging
    console.error('Client-side error:', {
      message: error.message,
      stack: error.stack,
      digest: error.digest,
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'unknown',
      url: typeof window !== 'undefined' ? window.location.href : 'unknown',
      timestamp: new Date().toISOString(),
    });

    // Send error to monitoring service if available
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'exception', {
        description: error.message,
        fatal: false,
        custom_map: {
          digest: error.digest,
          userAgent: navigator.userAgent,
        },
      });
    }
  }, [error]);

  const isIOSDevice = typeof window !== 'undefined' && 
    /iPad|iPhone|iPod/.test(navigator.userAgent);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="max-w-md w-full mx-4">
        <div className="bg-card border border-border rounded-lg p-6 shadow-lg">
          <div className="flex items-center gap-3 mb-4">
            <div className="flex-shrink-0">
              <AlertCircle className="h-8 w-8 text-destructive" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-foreground">
                Something went wrong
              </h1>
              <p className="text-sm text-muted-foreground">
                {isIOSDevice 
                  ? 'We detected an issue on your iOS device. This might be related to video loading or browser compatibility.'
                  : 'An unexpected error occurred while loading the page.'
                }
              </p>
            </div>
          </div>

          <div className="space-y-3">
            <div className="bg-muted/50 rounded-md p-3">
              <p className="text-xs font-mono text-muted-foreground break-all">
                {error.message}
              </p>
              {error.digest && (
                <p className="text-xs text-muted-foreground mt-1">
                  Error ID: {error.digest}
                </p>
              )}
            </div>

            {isIOSDevice && (
              <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-md p-3">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  <strong>iOS Users:</strong> Try refreshing the page or clearing your browser cache. 
                  If the issue persists, try using Safari in non-private mode.
                </p>
              </div>
            )}

            <div className="flex gap-2">
              <Button 
                onClick={reset}
                className="flex-1"
                variant="default"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <Button 
                onClick={() => window.location.href = '/'}
                variant="outline"
                className="flex-1"
              >
                Go Home
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
