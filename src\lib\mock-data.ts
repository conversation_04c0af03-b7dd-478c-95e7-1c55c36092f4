// Generate mock data for the stats page
export function generateMockHistoricalData() {
  // Helper to generate a random number between min and max
  const random = (min: number, max: number) => Math.floor(Math.random() * (max - min + 1)) + min

  // Helper to generate a date string for n days ago
  const daysAgo = (days: number) => {
    const date = new Date()
    date.setDate(date.getDate() - days)
    return date.toISOString().split("T")[0]
  }

  // Generate daily focus time for the last 30 days
  const dailyFocusTime = Array.from({ length: 30 }, (_, i) => {
    // Weekend days have less focus time
    const isWeekend = new Date(daysAgo(i)).getDay() === 0 || new Date(daysAgo(i)).getDay() === 6
    const minutes = isWeekend ? random(0, 120) : random(60, 240)

    return {
      date: daysAgo(i),
      minutes,
    }
  }).reverse()

  // Generate completion rate data
  const completionRate = {
    completed: random(70, 120),
    interrupted: random(10, 40),
  }

  // Generate hourly distribution data
  const hourlyDistribution = [
    { hour: "6 AM", value: random(0, 30) },
    { hour: "8 AM", value: random(20, 60) },
    { hour: "10 AM", value: random(40, 120) },
    { hour: "12 PM", value: random(30, 90) },
    { hour: "2 PM", value: random(40, 100) },
    { hour: "4 PM", value: random(50, 110) },
    { hour: "6 PM", value: random(30, 80) },
    { hour: "8 PM", value: random(10, 50) },
  ]

  // Generate weekly comparison data
  const weeklyComparison = [
    { name: "Mon", thisWeek: random(60, 120), lastWeek: random(40, 100) },
    { name: "Tue", thisWeek: random(70, 130), lastWeek: random(50, 110) },
    { name: "Wed", thisWeek: random(50, 110), lastWeek: random(60, 120) },
    { name: "Thu", thisWeek: random(80, 140), lastWeek: random(70, 130) },
    { name: "Fri", thisWeek: random(60, 120), lastWeek: random(50, 110) },
    { name: "Sat", thisWeek: random(30, 90), lastWeek: random(20, 80) },
    { name: "Sun", thisWeek: random(20, 80), lastWeek: random(10, 70) },
  ]

  // Generate daily heatmap data
  const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
  const dailyHeatmap = []

  for (const day of days) {
    for (let hour = 0; hour < 24; hour++) {
      // More likely to have focus time during work hours
      const isWorkHour = hour >= 9 && hour <= 17
      const isWeekend = day === "Saturday" || day === "Sunday"

      let value = 0
      if (!isWeekend && isWorkHour) {
        value = random(0, 60)
      } else if (!isWeekend) {
        value = random(0, 30)
      } else {
        value = random(0, 15)
      }

      if (value > 0) {
        dailyHeatmap.push({
          day,
          hour,
          value,
        })
      }
    }
  }

  // Generate streak calendar data
  const streakData = []
  const today = new Date()
  const startDate = new Date(today)
  startDate.setMonth(today.getMonth() - 3)

  const currentDate = new Date(startDate)

  while (currentDate <= today) {
    const isWeekend = currentDate.getDay() === 0 || currentDate.getDay() === 6
    const randomSkip = random(1, 10) > 8 // Occasionally skip days

    let count = 0
    if (!randomSkip) {
      count = isWeekend ? random(0, 3) : random(0, 8)
    }

    streakData.push({
      date: currentDate.toISOString().split("T")[0],
      count,
    })

    currentDate.setDate(currentDate.getDate() + 1)
  }

  // Generate today's sessions
  const todaySessions = []
  let startHour = 9

  for (let i = 0; i < random(4, 8); i++) {
    const duration = 25
    const startTime = `${startHour}:${random(0, 1) === 0 ? "00" : "30"}`
    const endHour = startTime.split(":")[0]
    const endMinute = Number.parseInt(startTime.split(":")[1]) + duration
    const endTime = endMinute >= 60 ? `${Number.parseInt(endHour) + 1}:${endMinute - 60}` : `${endHour}:${endMinute}`

    todaySessions.push({
      title: "",
      timeRange: `${startTime} - ${endTime}`,
      duration,
      completed: random(1, 10) > 2, // 80% chance of completion
    })

    startHour += random(1, 2)
    if (startHour > 18) break
  }

  // Generate monthly progress data
  const monthNames = ["January", "February", "March", "April", "May", "June"]
  const monthlyProgress = monthNames
    .map((month) => ({
      month,
      totalHours: random(15, 45),
    }))
    .reverse()

  // Calculate averages and other stats
  const averageDaily = Math.round(dailyFocusTime.reduce((acc, day) => acc + (day.minutes > 0 ? 1 : 0), 0) / 30)
  const averageDailyMinutes = Math.round(dailyFocusTime.reduce((acc, day) => acc + day.minutes, 0) / 30)
  const longestStreak = random(5, 14)
  const daysActiveThisMonth = random(15, 28)
  const daysActiveLastMonth = random(10, 25)
  const consistencyScore = random(50, 95)
  const todayQuality = random(50, 95)

  // Calculate month-over-month growth
  const thisMonth = monthlyProgress[0].totalHours
  const lastMonth = monthlyProgress[1].totalHours
  const monthOverMonthGrowth = Math.round(((thisMonth - lastMonth) / lastMonth) * 100)

  // Generate recommendation based on data
  let recommendation = ""
  if (averageDailyMinutes < 60) {
    recommendation =
      "Try to increase your daily focus time by starting with just one additional Pomodoro session each day."
  } else if (consistencyScore < 70) {
    recommendation =
      "Your focus sessions are somewhat irregular. Consider setting a regular schedule to build a stronger habit."
  } else if (completionRate.interrupted > completionRate.completed * 0.3) {
    recommendation =
      "You have a high rate of interrupted sessions. Consider identifying and eliminating common distractions."
  } else {
    recommendation =
      "You're doing great! Consider increasing your Pomodoro session length from 25 to 30 minutes to further boost productivity."
  }

  // Generate innovative metrics data
  const productivityScore = random(60, 95)
  const focusEfficiency = random(65, 95)
  const deepWorkRatio = random(40, 85)
  const goalAchievement = random(50, 90)

  // Generate flow state data
  const flowStateRating = ["Developing", "Good", "Strong", "Excellent"][random(0, 3)]
  const flowStateTime = ["morning", "mid-day", "afternoon", "evening"][random(0, 3)]
  const flowStateDuration = random(30, 90)

  const flowStateData = [
    { time: "8 AM", flowScore: random(20, 40) },
    { time: "9 AM", flowScore: random(30, 50) },
    { time: "10 AM", flowScore: random(40, 70) },
    { time: "11 AM", flowScore: random(50, 80) },
    { time: "12 PM", flowScore: random(40, 60) },
    { time: "1 PM", flowScore: random(30, 50) },
    { time: "2 PM", flowScore: random(50, 70) },
    { time: "3 PM", flowScore: random(60, 90) },
    { time: "4 PM", flowScore: random(70, 95) },
    { time: "5 PM", flowScore: random(50, 80) },
    { time: "6 PM", flowScore: random(40, 60) },
  ]

  const flowTriggers = ["Background music", "Morning sessions", "Clear goals", "Minimal distractions", "After exercise"]

  const flowBlockers = ["Notifications", "Hunger", "Afternoon slump", "Multitasking", "Unclear objectives"]

  // Generate optimal time data
  const optimalTimeData = [
    { hour: "6 AM", score: random(20, 40) },
    { hour: "7 AM", score: random(30, 50) },
    { hour: "8 AM", score: random(40, 60) },
    { hour: "9 AM", score: random(50, 80) },
    { hour: "10 AM", score: random(70, 95) },
    { hour: "11 AM", score: random(80, 100) },
    { hour: "12 PM", score: random(50, 70) },
    { hour: "1 PM", score: random(30, 50) },
    { hour: "2 PM", score: random(40, 60) },
    { hour: "3 PM", score: random(60, 80) },
    { hour: "4 PM", score: random(70, 90) },
    { hour: "5 PM", score: random(50, 70) },
    { hour: "6 PM", score: random(40, 60) },
    { hour: "7 PM", score: random(30, 50) },
    { hour: "8 PM", score: random(20, 40) },
  ]

  const optimalDay = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"][random(0, 4)]
  const optimalDayCompletion = random(75, 95)
  const optimalTime = ["9-11 AM", "10 AM-12 PM", "2-4 PM", "3-5 PM"][random(0, 3)]
  const optimalTimeEfficiency = random(80, 98)
  const optimalDuration = ["25 min", "30 min", "45 min"][random(0, 2)]
  const optimalDurationSuccess = random(80, 95)

  // Generate focus pattern insights
  const productivityRhythm = [
    "You exhibit a strong 'morning person' pattern, with peak productivity occurring between 9-11 AM. Consider scheduling your most challenging tasks during this window.",
    "Your productivity follows a bimodal pattern with peaks in mid-morning and mid-afternoon. The post-lunch period shows a consistent dip in focus quality.",
    "You demonstrate a 'night owl' productivity pattern, with focus quality increasing steadily throughout the day and peaking in the late afternoon and early evening.",
  ][random(0, 2)]

  const focusDurationInsight = [
    "Your optimal focus session length appears to be 25 minutes. Sessions longer than 30 minutes show a 40% higher interruption rate.",
    "You perform best with slightly longer focus periods of 30-35 minutes, followed by shorter 5-minute breaks.",
    "Your data suggests you might benefit from the 'Pomodoro Fibonacci' approach: gradually increasing session lengths (25, 30, 35, 40 minutes) before taking a longer break.",
  ][random(0, 2)]

  const consistencyPattern = [
    "You show strong weekday consistency but significant drops on weekends. This 'work-week warrior' pattern is common but may impact momentum.",
    "Your focus sessions are evenly distributed throughout the week with no significant day-to-day variance, indicating excellent habit formation.",
    "Your data shows a 'strong start, gradual decline' weekly pattern, with Monday-Wednesday being your most productive days and a tapering effect toward the weekend.",
  ][random(0, 2)]

  const recommendations = [
    "Schedule your most challenging tasks during your peak productivity window of " + optimalTime,
    "Consider implementing a 'focus environment trigger' like specific music or lighting that signals your brain it's time to concentrate",
    "Your data suggests that taking a short 5-minute walk before focus sessions improves completion rates by 30%",
    "Try the '2-minute rule' for distractions: note them down to address later rather than breaking your focus",
    "Experiment with " + optimalDuration + " focus sessions, which align with your natural attention span",
  ]

  // Generate daily calendar view data
  const dailyCalendarSessions = []
  let sessionId = 1

  // Morning focus block
  let currentHour = 9
  let currentMinute = 0

  for (let i = 0; i < random(2, 4); i++) {
    // Focus session
    const focusStartTime = `${currentHour.toString().padStart(2, "0")}:${currentMinute.toString().padStart(2, "0")}`

    currentMinute += 25
    if (currentMinute >= 60) {
      currentHour += 1
      currentMinute -= 60
    }

    const focusEndTime = `${currentHour.toString().padStart(2, "0")}:${currentMinute.toString().padStart(2, "0")}`

    dailyCalendarSessions.push({
      id: `session-${sessionId++}`,
      title: "Focus Session",
      type: "focus",
      startTime: focusStartTime,
      endTime: focusEndTime,
      completed: random(1, 10) > 2, // 80% chance of completion
    })

    // Short break
    const breakStartTime = focusEndTime

    currentMinute += 5
    if (currentMinute >= 60) {
      currentHour += 1
      currentMinute -= 60
    }

    const breakEndTime = `${currentHour.toString().padStart(2, "0")}:${currentMinute.toString().padStart(2, "0")}`

    dailyCalendarSessions.push({
      id: `session-${sessionId++}`,
      title: "Short Break",
      type: "shortBreak",
      startTime: breakStartTime,
      endTime: breakEndTime,
      completed: true,
    })
  }

  // Lunch break
  const lunchStartTime = `${currentHour.toString().padStart(2, "0")}:${currentMinute.toString().padStart(2, "0")}`

  currentHour += 1

  const lunchEndTime = `${currentHour.toString().padStart(2, "0")}:${currentMinute.toString().padStart(2, "0")}`

  dailyCalendarSessions.push({
    id: `session-${sessionId++}`,
    title: "Long Break",
    type: "longBreak",
    startTime: lunchStartTime,
    endTime: lunchEndTime,
    completed: true,
  })

  // Afternoon focus block
  for (let i = 0; i < random(3, 5); i++) {
    // Focus session
    const focusStartTime = `${currentHour.toString().padStart(2, "0")}:${currentMinute.toString().padStart(2, "0")}`

    currentMinute += 25
    if (currentMinute >= 60) {
      currentHour += 1
      currentMinute -= 60
    }

    const focusEndTime = `${currentHour.toString().padStart(2, "0")}:${currentMinute.toString().padStart(2, "0")}`

    dailyCalendarSessions.push({
      id: `session-${sessionId++}`,
      title: "Focus Session",
      type: "focus",
      startTime: focusStartTime,
      endTime: focusEndTime,
      completed: random(1, 10) > 3, // 70% chance of completion
    })

    // Break (short or long)
    const breakStartTime = focusEndTime

    // Every 4th break is a long break
    const isLongBreak = i > 0 && i % 3 === 0

    currentMinute += isLongBreak ? 15 : 5
    if (currentMinute >= 60) {
      currentHour += 1
      currentMinute -= 60
    }

    const breakEndTime = `${currentHour.toString().padStart(2, "0")}:${currentMinute.toString().padStart(2, "0")}`

    dailyCalendarSessions.push({
      id: `session-${sessionId++}`,
      title: isLongBreak ? "Long Break" : "Short Break",
      type: isLongBreak ? "longBreak" : "shortBreak",
      startTime: breakStartTime,
      endTime: breakEndTime,
      completed: true,
    })
  }

  return {
    dailyFocusTime,
    completionRate,
    hourlyDistribution,
    weeklyComparison,
    dailyHeatmap,
    streakData,
    todaySessions,
    monthlyProgress,
    averageDaily,
    averageDailyMinutes,
    longestStreak,
    daysActiveThisMonth,
    daysActiveLastMonth,
    consistencyScore,
    todayQuality,
    monthOverMonthGrowth,
    recommendation,
    // Innovative metrics
    productivityScore,
    focusEfficiency,
    deepWorkRatio,
    goalAchievement,
    flowStateRating,
    flowStateTime,
    flowStateDuration,
    flowStateData,
    flowTriggers,
    flowBlockers,
    optimalTimeData,
    optimalDay,
    optimalDayCompletion,
    optimalTime,
    optimalTimeEfficiency,
    optimalDuration,
    optimalDurationSuccess,
    // Focus pattern insights
    productivityRhythm,
    focusDurationInsight,
    consistencyPattern,
    recommendations,
    // Daily calendar view
    dailyCalendarSessions,
  }
}
