'use client';

import { useState } from "react";
import { <PERSON>ricingHeader } from "./pricing-header";
import { BillingToggle } from "./billing-toggle";
import { PricingCard } from "./pricing-card";
import { FeatureComparison } from "./feature-comparison";
import { PricingFAQ } from "./pricing-faq";

export const PricingPageClient = () => {
  const [isAnnual, setIsAnnual] = useState(false);

  // Define the three pricing plans
  const pricingPlans = [
    {
      id: "free",
      name: "Free",
      price: 0,
      period: "forever",
      description: "Perfect for getting started with the Pomodoro Technique",
      features: [
        "Basic Pomodoro timer (25/5/15 min)",
        "3 video backgrounds",
        "Current day analytics",
        "Local storage",
        "Basic notifications"
      ],
      limitations: [
        "No music or natural sounds",
        "Limited video backgrounds",
        "No cloud sync",
        "No historical analytics"
      ],
      buttonText: "Get Started Free",
      buttonVariant: "outline" as const,
      popular: false,
    },
    {
      id: "premium",
      name: "Premium",
      price: isAnnual ? 19 : 3,
      period: isAnnual ? "year" : "month",
      originalPrice: isAnnual ? 36 : undefined,
      description: isAnnual
        ? "Best value with annual billing"
        : "Full access to all premium features",
      features: [
        "Everything in Free",
        "Full video background library",
        "Complete music & natural sounds",
        "30-day analytics history",
        "Cloud sync across devices",
        "Custom timer settings",
        "Advanced notifications",
        "Priority support"
      ],
      buttonText: isAnnual ? "Choose Annual Plan" : "Start Monthly Plan",
      buttonVariant: "default" as const,
      popular: true,
      monthlyEquivalent: isAnnual ? 1.58 : undefined,
      savings: isAnnual ? { percentage: 47, amount: 17 } : undefined,
    },
    {
      id: "lifetime",
      name: "Lifetime",
      price: 59,
      period: "one-time",
      description: "Pay once, own forever with all future updates",
      features: [
        "Everything in Premium",
        "Lifetime access",
        "All future features included",
        "Priority support",
        "Early access to new features",
        "No recurring payments"
      ],
      buttonText: "Buy Lifetime",
      buttonVariant: "default" as const,
      popular: false,
      badge: "One-time Payment",
    },
  ];

  return (
    <main className="min-h-screen flex flex-col bg-background">
      {/* Header Section */}
      <PricingHeader />

      {/* Pricing Cards Section */}
      <section className="w-full py-12 md:py-16">
        <div className="container mx-auto px-4 max-w-7xl">
          {/* Billing Toggle */}
          <BillingToggle isAnnual={isAnnual} onToggle={setIsAnnual} />
          
          {/* Pricing Cards Grid - Proper spacing to prevent badge cutoff */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 pt-2">
            {pricingPlans.map((plan) => (
              <PricingCard key={plan.id} plan={plan} />
            ))}
          </div>
        </div>
      </section>

      {/* Feature Comparison Section */}
      <FeatureComparison />

      {/* FAQ Section */}
      <PricingFAQ />
    </main>
  );
};
