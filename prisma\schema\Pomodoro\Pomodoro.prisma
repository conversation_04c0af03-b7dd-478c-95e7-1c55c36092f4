// Pomodoro-related models for tracking sessions and tasks

model PomodoroSession {
    id                  String       @id @default(cuid())
    startTime           DateTime     @default(now())
    endTime             DateTime?
    totalDuration       Int // Duration in seconds
    focusDuration       Int?         @default(25)
    breakDuration       Int?         @default(5)
    completed           Boolean      @default(false)
    interrupted         Boolean      @default(false)
    interruptedSessions Json? // Array of {startTime, endTime} objects
    note                String?      @db.Text
    intervalType        PomodoroType @default(FOCUS)
    createdAt           DateTime     @default(now())
    updatedAt           DateTime     @updatedAt

    // Relations
    user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId String

    task   Task?   @relation(fields: [taskId], references: [id], onDelete: SetNull)
    taskId String? // Optional task association

    @@index([userId])
    @@index([taskId])
    @@index([startTime])
}

enum PomodoroType {
    FOCUS
    SHORT_BREAK
    LONG_BREAK
}

