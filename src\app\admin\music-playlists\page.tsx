"use client";

import { useGetAdminMusicPlaylists } from "@schemas/MusicPlaylist/music-playlist-admin-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Plus,
  Search,
  Music2,
  Video,
  ListFilter,
  Grid3X3,
  List,
  SlidersHorizontal,
  X,
  Filter,
  Loader2
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { MusicPlaylistCard } from "./_components/music-playlist-card";
import { useState, useEffect, useMemo } from "react";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MusicPlaylistFormSheet } from "./_components/music-playlist-form-sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { useDebounce } from "@/hooks/use-debounce";
import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { formatGenreName, getGenreColorClass, truncateGenres } from "@/lib/genre-utils";

// Add type for sort options
type SortOption = "name" | "newest" | "items";

// Define interfaces for the app
interface Music {
  id: string;
  title: string;
}

interface Video {
  id: string;
  title: string;
  thumbnail: string;
  musicPlaylistId?: string | null;
  naturePlaylistId?: string | null;
}

// We define this interface to match the actual data structure from the API
export interface MusicPlaylist {
  id: string;
  name: string;
  description: string | null;
  isPublic: boolean;
  isDefault: boolean;
  imageUrl: string | null;
  genres: string[];
  createdAt: string;
  updatedAt: string;
  videos: Video[];
  musics: Music[];
}

export default function MusicPlaylistsPage() {
  // State management
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const [activeTab, setActiveTab] = useState("all");
  const [isCreateSheetOpen, setIsCreateSheetOpen] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState<SortOption>("newest");
  const [isFiltersVisible, setIsFiltersVisible] = useState(false);

  // Get playlists with filters
  const { data: playlists, isLoading, refetch } = useGetAdminMusicPlaylists({
    isPublic: activeTab === "public" ? true : undefined,
    isDefault: activeTab === "default" ? true : undefined,
  });

  // Memoize filtered and sorted playlists for performance
  const sortedPlaylists = useMemo(() => {
    const filtered = playlists?.filter((playlist) =>
      playlist.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
      (playlist.description && playlist.description.toLowerCase().includes(debouncedSearchQuery.toLowerCase()))
    ) || [];

    return [...filtered].sort((a, b) => {
      if (sortBy === "name") {
        return a.name.localeCompare(b.name);
      } else if (sortBy === "newest") {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      } else if (sortBy === "items") {
        const aCount = a.videos.length + a.musics.length;
        const bCount = b.videos.length + b.musics.length;
        return bCount - aCount;
      }
      return 0;
    });
  }, [playlists, debouncedSearchQuery, sortBy]);

  // Calculate stats
  const stats = useMemo(() => {
    if (!playlists) return { total: 0, public: 0, default: 0, items: 0 };
    
    return {
      total: playlists.length,
      public: playlists.filter(p => p.isPublic).length,
      default: playlists.filter(p => p.isDefault).length,
      items: playlists.reduce((acc, playlist) => 
        acc + playlist.videos.length + playlist.musics.length, 0)
    };
  }, [playlists]);

  // Set page title
  useEffect(() => {
    document.title = "Music Playlists | Pomodoro 365 Admin";
  }, []);

  // Handle search clear
  const handleClearSearch = () => {
    setSearchQuery("");
  };

  // Render stats cards
  const renderStatsCard = (
    icon: React.ReactNode, 
    label: string, 
    value: number, 
    bgColor: string,
    textColor: string
  ) => (
    <div className="bg-background/80 backdrop-blur-sm rounded-lg p-4 shadow-sm border transition-all hover:border-blue-300 dark:hover:border-blue-700">
      <div className="flex items-center gap-3">
        <div className={cn("p-2.5 rounded-full", bgColor)}>
          {icon}
        </div>
        <div>
          <p className="text-sm text-muted-foreground">{label}</p>
          <p className={cn("text-2xl font-bold", textColor)}>{value}</p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="container mx-auto py-6 px-4 md:px-6 space-y-6 max-w-7xl">
      {/* Header with stats */}
      <motion.div 
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-900/20 rounded-xl p-6 shadow-sm border"
      >
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent dark:from-blue-400 dark:to-indigo-400">
              Music Playlists
            </h1>
            <p className="text-muted-foreground">
              Create and manage music playlists for videos and music tracks
            </p>
          </div>
          <Button
            onClick={() => setIsCreateSheetOpen(true)}
            size="lg"
            className="w-full md:w-auto bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-md transition-all hover:shadow-lg"
          >
            <Plus className="mr-2 h-5 w-5" />
            Create Music Playlist
          </Button>
        </div>

        {/* Stats cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
          {renderStatsCard(
            <Music2 className="h-5 w-5 text-blue-600 dark:text-blue-400" />,
            "Total Playlists",
            stats.total,
            "bg-blue-100 dark:bg-blue-900/30",
            "text-blue-600 dark:text-blue-400"
          )}
          
          {renderStatsCard(
            <Video className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />,
            "Public Playlists",
            stats.public,
            "bg-indigo-100 dark:bg-indigo-900/30",
            "text-indigo-600 dark:text-indigo-400"
          )}
          
          {renderStatsCard(
            <Music2 className="h-5 w-5 text-violet-600 dark:text-violet-400" />,
            "Default Playlists",
            stats.default,
            "bg-violet-100 dark:bg-violet-900/30",
            "text-violet-600 dark:text-violet-400"
          )}
          
          {renderStatsCard(
            <ListFilter className="h-5 w-5 text-purple-600 dark:text-purple-400" />,
            "Total Items",
            stats.items,
            "bg-purple-100 dark:bg-purple-900/30",
            "text-purple-600 dark:text-purple-400"
          )}
        </div>
      </motion.div>

      {/* Filters and controls */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur-md py-4 border-b rounded-lg shadow-sm px-4">
        <div className="flex flex-col md:flex-row items-center gap-4">
          <div className="relative w-full md:w-[300px]">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search music playlists..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 h-10 rounded-full pr-10"
              aria-label="Search music playlists"
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 text-muted-foreground hover:text-foreground"
                onClick={handleClearSearch}
                aria-label="Clear search"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          <div className="hidden md:block">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-auto"
            >
              <TabsList className="grid w-[300px] grid-cols-3">
                <TabsTrigger value="all" className="text-sm">All</TabsTrigger>
                <TabsTrigger value="public" className="text-sm">Public</TabsTrigger>
                <TabsTrigger value="default" className="text-sm">Default</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="flex-1 hidden md:block" />

          <div className="flex items-center gap-2 w-full md:w-auto justify-between md:justify-end">
            <Button
              variant="outline"
              size="sm"
              className="md:hidden h-10 flex-1"
              onClick={() => setIsFiltersVisible(!isFiltersVisible)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {activeTab !== "all" && <Badge variant="secondary" className="ml-2">{activeTab}</Badge>}
            </Button>

            {/* Sort dropdown */}
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-10">
                  <SlidersHorizontal className="h-4 w-4 mr-2" />
                  Sort
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuRadioGroup 
                  value={sortBy} 
                  onValueChange={(value) => setSortBy(value as SortOption)}
                >
                  <DropdownMenuRadioItem value="newest">Newest</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="name">Name</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="items">Most Items</DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* View mode toggle */}
            <div className="flex items-center border rounded-md overflow-hidden">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                className="h-10 rounded-none"
                onClick={() => setViewMode("grid")}
                aria-label="Grid view"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                className="h-10 rounded-none"
                onClick={() => setViewMode("list")}
                aria-label="List view"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile filters */}
        <AnimatePresence>
          {isFiltersVisible && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="md:hidden overflow-hidden mt-4"
            >
              <Tabs
                value={activeTab}
                onValueChange={(value) => {
                  setActiveTab(value);
                }}
                className="w-full"
              >
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="all" className="text-sm">All</TabsTrigger>
                  <TabsTrigger value="public" className="text-sm">Public</TabsTrigger>
                  <TabsTrigger value="default" className="text-sm">Default</TabsTrigger>
                </TabsList>
              </Tabs>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Results count */}
      {!isLoading && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing <span className="font-medium text-foreground">{sortedPlaylists.length}</span> of <span className="font-medium text-foreground">{stats.total}</span> music playlists
          </p>
          {searchQuery && (
            <Badge variant="outline" className="flex items-center gap-1">
              Search: {searchQuery}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1"
                onClick={handleClearSearch}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}

      {/* Playlists grid/list */}
      <div className="mt-4">
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="space-y-3">
                <Skeleton className="aspect-video rounded-lg" />
                <div className="space-y-2">
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <div className="flex gap-2 mt-2">
                    <Skeleton className="h-4 w-8 rounded-full" />
                    <Skeleton className="h-4 w-8 rounded-full" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : sortedPlaylists.length === 0 ? (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
            className="text-center py-16 bg-muted/30 rounded-lg border border-dashed"
          >
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900/30 mb-4">
              <Music2 className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="text-xl font-semibold">No music playlists found</h3>
            <p className="text-muted-foreground mt-2 max-w-md mx-auto">
              {searchQuery
                ? "Try adjusting your search query or filters to find what you're looking for."
                : "Create your first music playlist to organize your videos and music tracks."}
            </p>
            <Button
              onClick={() => setIsCreateSheetOpen(true)}
              className="mt-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Music Playlist
            </Button>
          </motion.div>
        ) : viewMode === "grid" ? (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {sortedPlaylists.map((playlist, index) => (
              <motion.div
                key={playlist.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <MusicPlaylistCard playlist={playlist} />
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
          >
            <div className="rounded-lg border overflow-hidden">
              <ScrollArea className="h-[calc(100vh-300px)]">
                <div className="divide-y">
                  {sortedPlaylists.map((playlist, index) => (
                    <motion.div
                      key={playlist.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.2, delay: index * 0.03 }}
                      className="flex items-center gap-4 p-4 hover:bg-muted/50 transition-colors"
                    >
                      <div className="relative h-16 w-24 rounded-md overflow-hidden flex-shrink-0">
                        {playlist.imageUrl ? (
                          <Image
                            src={playlist.imageUrl}
                            alt={playlist.name}
                            className="object-cover"
                            fill
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 flex items-center justify-center">
                            <Music2 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                          </div>
                        )}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 flex-wrap">
                          <h3 className="font-medium truncate">{playlist.name}</h3>
                          {playlist.isPublic && (
                            <Badge variant="outline" className="bg-blue-50 dark:bg-blue-900/20 text-xs">
                              Public
                            </Badge>
                          )}
                          {playlist.isDefault && (
                            <Badge variant="outline" className="bg-indigo-50 dark:bg-indigo-900/20 text-xs">
                              Default
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground line-clamp-1 mt-1">
                          {playlist.description || "No description"}
                        </p>

                        {/* Genres */}
                        {playlist.genres && playlist.genres.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {truncateGenres(playlist.genres, 3).visible.map((genre) => (
                              <Badge
                                key={genre}
                                variant="outline"
                                className={cn("text-xs", getGenreColorClass(genre))}
                              >
                                {formatGenreName(genre)}
                              </Badge>
                            ))}
                            {truncateGenres(playlist.genres, 3).remaining > 0 && (
                              <Badge variant="outline" className="text-xs bg-gray-100 text-gray-600 dark:bg-gray-900/30 dark:text-gray-400">
                                +{truncateGenres(playlist.genres, 3).remaining}
                              </Badge>
                            )}
                          </div>
                        )}

                        <div className="flex items-center gap-4 mt-2">
                          <div className="flex items-center gap-1">
                            <Video className="h-3 w-3 text-indigo-600 dark:text-indigo-400" />
                            <span className="text-xs">{playlist.videos.length}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Music2 className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                            <span className="text-xs">{playlist.musics.length}</span>
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {new Date(playlist.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.location.href = `/admin/music-playlists/${playlist.id}`}
                        className="hidden sm:flex"
                      >
                        View Details
                      </Button>
                    </motion.div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </motion.div>
        )}
      </div>

      {/* Create Music Playlist Sheet */}
      <MusicPlaylistFormSheet
        open={isCreateSheetOpen}
        onOpenChange={setIsCreateSheetOpen}
        onSuccess={() => {
          setIsCreateSheetOpen(false);
          refetch();
        }}
      />
    </div>
  );
} 