"use client";

import { useGetAdminNaturePlaylists } from "@schemas/NaturalPlaylist/natural-playlist-admin-query";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Plus,
  Search,
  Waves,
  Video,
  ListFilter,
  Grid3X3,
  List,
  SlidersHorizontal
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { NaturePlaylistCard } from "./_components/nature-playlist-card";
import { useState, useEffect } from "react";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { NaturePlaylistFormSheet } from "./_components/nature-playlist-form-sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";

// Add type for sort options
type SortOption = "name" | "newest" | "items";

// Define interfaces for the app
interface NatureSound {
  id: string;
  title: string;
}

interface Video {
  id: string;
  title: string;
  thumbnail: string;
  musicPlaylistId?: string | null;
  naturePlaylistId?: string | null;
}

// We define this interface to match the actual data structure from the API
export interface NaturePlaylist {
  id: string;
  name: string;
  description: string | null;
  isPublic: boolean;
  isDefault: boolean;
  imageUrl: string | null;
  createdAt: string;
  updatedAt: string;
  videos: Video[];
  natureSounds: NatureSound[];
}

export default function NaturePlaylistsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [isCreateSheetOpen, setIsCreateSheetOpen] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState<SortOption>("newest");

  // Get playlists with filters
  const { data: playlists, isLoading, refetch } = useGetAdminNaturePlaylists({
    isPublic: activeTab === "public" ? true : undefined,
    isDefault: activeTab === "default" ? true : undefined,
  });

  // Filter playlists by search query
  const filteredPlaylists = playlists?.filter((playlist) =>
    playlist.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (playlist.description && playlist.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Sort playlists based on selected sort option
  const sortedPlaylists = filteredPlaylists ? [...filteredPlaylists].sort((a, b) => {
    if (sortBy === "name") {
      return a.name.localeCompare(b.name);
    } else if (sortBy === "newest") {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    } else if (sortBy === "items") {
      const aCount = a.videos.length + a.natureSounds.length;
      const bCount = b.videos.length + b.natureSounds.length;
      return bCount - aCount;
    }
    return 0;
  }) : [];

  // Calculate stats
  const totalPlaylists = playlists?.length || 0;
  const publicPlaylists = playlists?.filter((p) => p.isPublic).length || 0;
  const defaultPlaylists = playlists?.filter((p) => p.isDefault).length || 0;

  // Calculate total items across all playlists
  const totalItems: number = playlists?.reduce((acc: number, playlist) => {
    return acc + playlist.videos.length + playlist.natureSounds.length;
  }, 0) || 0;

  // Set page title
  useEffect(() => {
    document.title = "Nature Playlists | Pomodoro 365 Admin";
  }, []);

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 space-y-8">
      {/* Header with stats */}
      <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20 rounded-xl p-6 shadow-sm">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">Nature Playlists</h1>
            <p className="text-muted-foreground">
              Create and manage your nature playlists for videos and nature sounds
            </p>
          </div>
          <Button
            onClick={() => setIsCreateSheetOpen(true)}
            size="lg"
            className="w-full md:w-auto bg-green-600 hover:bg-green-700 text-white"
          >
            <Plus className="mr-2 h-5 w-5" />
            Create Nature Playlist
          </Button>
        </div>

        {/* Stats cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
          <div className="bg-background/80 backdrop-blur-sm rounded-lg p-4 shadow-sm">
            <div className="flex items-center gap-2">
              <div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30">
                <Waves className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Playlists</p>
                <p className="text-2xl font-bold">{totalPlaylists}</p>
              </div>
            </div>
          </div>

          <div className="bg-background/80 backdrop-blur-sm rounded-lg p-4 shadow-sm">
            <div className="flex items-center gap-2">
              <div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30">
                <Video className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Public Playlists</p>
                <p className="text-2xl font-bold">{publicPlaylists}</p>
              </div>
            </div>
          </div>

          <div className="bg-background/80 backdrop-blur-sm rounded-lg p-4 shadow-sm">
            <div className="flex items-center gap-2">
              <div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30">
                <Waves className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Default Playlists</p>
                <p className="text-2xl font-bold">{defaultPlaylists}</p>
              </div>
            </div>
          </div>

          <div className="bg-background/80 backdrop-blur-sm rounded-lg p-4 shadow-sm">
            <div className="flex items-center gap-2">
              <div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30">
                <ListFilter className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Items</p>
                <p className="text-2xl font-bold">{totalItems}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and controls */}
      <div className="flex flex-col md:flex-row items-center gap-4 sticky top-0 z-10 bg-background/80 backdrop-blur-sm py-4 border-b">
        <div className="relative w-full md:w-[300px]">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search nature playlists..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-10 rounded-full"
          />
        </div>

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full md:w-auto"
        >
          <TabsList className="grid w-full md:w-[300px] grid-cols-3">
            <TabsTrigger value="all" className="text-sm">All</TabsTrigger>
            <TabsTrigger value="public" className="text-sm">Public</TabsTrigger>
            <TabsTrigger value="default" className="text-sm">Default</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex-1" />

        <div className="flex items-center gap-2">
          {/* Sort dropdown */}
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-10">
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                Sort
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Sort by</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuRadioGroup 
                value={sortBy} 
                onValueChange={(value) => setSortBy(value as SortOption)}
              >
                <DropdownMenuRadioItem value="newest">Newest</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="name">Name</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="items">Most Items</DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* View mode toggle */}
          <div className="flex items-center border rounded-md overflow-hidden">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="sm"
              className="h-10 rounded-none"
              onClick={() => setViewMode("grid")}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="sm"
              className="h-10 rounded-none"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Results count */}
      {!isLoading && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing {sortedPlaylists.length} of {totalPlaylists} nature playlists
          </p>
          {searchQuery && (
            <Badge variant="outline" className="flex items-center gap-1">
              Search: {searchQuery}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1"
                onClick={() => setSearchQuery("")}
              >
                <Search className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}

      {/* Playlists grid/list */}
      <div className="mt-4">
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="space-y-3">
                <Skeleton className="aspect-video rounded-lg" />
                <div className="space-y-2">
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <div className="flex gap-2 mt-2">
                    <Skeleton className="h-4 w-8 rounded-full" />
                    <Skeleton className="h-4 w-8 rounded-full" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : sortedPlaylists.length === 0 ? (
          <div className="text-center py-16 bg-muted/30 rounded-lg border border-dashed">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
              <Waves className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-semibold">No nature playlists found</h3>
            <p className="text-muted-foreground mt-2 max-w-md mx-auto">
              {searchQuery
                ? "Try adjusting your search query or filters to find what you're looking for."
                : "Create your first nature playlist to organize your videos and nature sounds."}
            </p>
            <Button
              onClick={() => setIsCreateSheetOpen(true)}
              className="mt-6"
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Nature Playlist
            </Button>
          </div>
        ) : viewMode === "grid" ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {sortedPlaylists.map((playlist) => (
              <NaturePlaylistCard key={playlist.id} playlist={playlist} />
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            <div className="rounded-lg border overflow-hidden">
              <ScrollArea className="h-[calc(100vh-300px)]">
                <div className="divide-y">
                  {sortedPlaylists.map((playlist) => (
                    <div key={playlist.id} className="flex items-center gap-4 p-4 hover:bg-muted/50 transition-colors">
                      <div className="relative h-16 w-24 rounded overflow-hidden flex-shrink-0">
                        {playlist.imageUrl ? (
                          <Image
                            src={playlist.imageUrl}
                            alt={playlist.name}
                            className="object-cover"
                            fill
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          />
                        ) : (
                          <div className="w-full h-full bg-muted flex items-center justify-center">
                            <Waves className="h-6 w-6 text-muted-foreground" />
                          </div>
                        )}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium truncate">{playlist.name}</h3>
                          {playlist.isPublic && <Badge variant="outline">Public</Badge>}
                          {playlist.isDefault && <Badge variant="outline">Default</Badge>}
                        </div>
                        <p className="text-sm text-muted-foreground line-clamp-1 mt-1">
                          {playlist.description || "No description"}
                        </p>
                        <div className="flex items-center gap-4 mt-2">
                          <div className="flex items-center gap-1">
                            <Video className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs">{playlist.videos.length}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Waves className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs">{playlist.natureSounds.length}</span>
                          </div>
                        </div>
                      </div>

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.location.href = `/admin/nature-playlists/${playlist.id}`}
                      >
                        View
                      </Button>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </div>
        )}
      </div>

      {/* Create Nature Playlist Sheet */}
      <NaturePlaylistFormSheet
        open={isCreateSheetOpen}
        onOpenChange={setIsCreateSheetOpen}
        onSuccess={() => {
          setIsCreateSheetOpen(false);
          refetch();
        }}
      />
    </div>
  );
} 