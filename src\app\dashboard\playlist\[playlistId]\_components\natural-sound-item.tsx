"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Play, Pause, MoreHorizontal, Trash2, Clock, Waves } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import { useRemoveNatureSoundFromMusicPlaylistUser } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { toast } from "sonner"
import { Badge } from "@/components/ui/badge"

// Define a type for natural sound from playlist
interface PlaylistNatureSound {
  id: string
  title: string
  src?: string | null
  category?: string[]
  user?: {
    id: string
    name: string
    image: string | null
  }
}

interface NatureSoundItemProps {
  natureSound: PlaylistNatureSound
  index: number
  isPlaying: boolean
  onPlay: () => void
  playlistId: string
}

// Map category enum values to display names
const categoryDisplayNames: Record<string, string> = {
  RAIN: "Rain",
  FOREST: "Forest", 
  OCEAN: "Ocean",
  THUNDER: "Thunder",
  WIND: "Wind",
  FIRE: "Fire",
  BIRDS: "Birds",
  WATERFALL: "Waterfall",
  STREAM: "Stream",
  WAVES: "Waves",
  NIGHT: "Night",
  INSECTS: "Insects",
  RIVER: "River",
  STORM: "Storm",
  LAKE: "Lake",
  WHITE_NOISE: "White Noise",
  AMBIENT: "Ambient",
  CITY: "City",
  PEOPLE: "People"
}

export function NatureSoundItem({ natureSound, index, isPlaying, onPlay, playlistId }: NatureSoundItemProps) {
  const [isHovered, setIsHovered] = useState(false)
  const removeNatureSoundFromPlaylist = useRemoveNatureSoundFromMusicPlaylistUser()

  const handleRemove = async () => {
    try {
      await removeNatureSoundFromPlaylist.mutateAsync({
        musicPlaylistUserId: playlistId,
        natureSoundId: natureSound.id
      })
      toast.success("Natural sound removed from playlist")
    } catch (error) {
      console.error(error)
      toast.error("Failed to remove natural sound")
    }
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return "∞" // Natural sounds typically loop
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      'RAIN': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      'FOREST': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      'OCEAN': 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-300',
      'THUNDER': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
      'WIND': 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300',
      'FIRE': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
      'BIRDS': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
      'WATERFALL': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      'STREAM': 'bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300',
      'WAVES': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300',
      'NIGHT': 'bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300',
      'INSECTS': 'bg-lime-100 text-lime-800 dark:bg-lime-900/30 dark:text-lime-300',
      'RIVER': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      'STORM': 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300',
      'LAKE': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      'WHITE_NOISE': 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300',
      'AMBIENT': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
      'CITY': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
      'PEOPLE': 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300'
    }
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
  }

  return (
    <motion.div
      layout
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 10 }}
      transition={{ duration: 0.2, delay: index * 0.05 }}
      className={cn(
        "group flex items-center gap-4 px-4 py-3 mx-2 hover:bg-muted/30 transition-all duration-200 rounded-lg",
        isPlaying && "bg-emerald-50/50 dark:bg-emerald-950/20"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Track Number / Play Button */}
      <div className="w-8 h-8 flex items-center justify-center shrink-0">
        {isHovered || isPlaying ? (
          <Button
            variant="ghost"
            size="icon"
            onClick={onPlay}
            className={cn(
              "h-8 w-8 rounded-full transition-all duration-200",
              isPlaying
                ? "bg-emerald-500 text-white hover:bg-emerald-600"
                : "hover:bg-emerald-100 dark:hover:bg-emerald-900/50"
            )}
          >
            {isPlaying ? (
              <Pause className="h-3.5 w-3.5" />
            ) : (
              <Play className="h-3.5 w-3.5 ml-0.5" />
            )}
          </Button>
        ) : (
          <span className="text-sm text-muted-foreground font-medium">
            {(index + 1).toString().padStart(2, '0')}
          </span>
        )}
      </div>

      {/* Natural Sound Info */}
      <div className="flex-1 min-w-0 flex flex-col justify-center space-y-1">
        <div className="flex items-center gap-2">
          <h4 className={cn(
            "font-medium text-sm truncate transition-colors duration-200 leading-tight",
            isPlaying && "text-emerald-600 dark:text-emerald-400"
          )}>
            {natureSound.title}
          </h4>
          <Waves className="h-3 w-3 text-emerald-500 shrink-0" />
        </div>

        <div className="flex items-center gap-1.5 text-xs text-muted-foreground leading-tight">
          <span>Nature Sound</span>
          {/* Categories */}
          {natureSound.category && natureSound.category.length > 0 && (
            <>
              <span>•</span>
              <div className="flex items-center gap-1">
                <Badge
                  variant="outline"
                  className={cn("text-xs px-1.5 py-0.5 h-4", getCategoryColor(natureSound.category[0]))}
                >
                  {categoryDisplayNames[natureSound.category[0]] || natureSound.category[0]}
                </Badge>
                {natureSound.category.length > 1 && (
                  <span className="text-xs">+{natureSound.category.length - 1}</span>
                )}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Duration & Actions Combined */}
      <div className="flex items-center gap-3 shrink-0">
        <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
          <Clock className="h-3 w-3" />
          <span className="font-medium">{formatDuration()}</span>
        </div>

        {/* Actions */}
        <div className={cn(
          "flex items-center transition-opacity duration-200",
          isHovered || isPlaying ? "opacity-100" : "opacity-0"
        )}>
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 hover:bg-muted"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-44">
              <DropdownMenuItem onClick={onPlay}>
                <Play className="mr-2 h-4 w-4" />
                {isPlaying ? "Restart Sound" : "Play Sound"}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleRemove}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Remove from Playlist
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </motion.div>
  )
}
