"use client";

import { useState } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { Play, AlertCircle, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";

interface ThumbnailPreviewProps {
  src: string;
  alt: string;
  className?: string;
  aspectRatio?: "video" | "square" | "portrait";
  onClick?: () => void;
  showPlayButton?: boolean;
  isPremium?: boolean;
}

export function ThumbnailPreview({
  src,
  alt,
  className,
  aspectRatio = "video",
  onClick,
  showPlayButton = true,
  isPremium = false,
}: ThumbnailPreviewProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const aspectRatioClass = {
    video: "aspect-video",
    square: "aspect-square",
    portrait: "aspect-[3/4]",
  }[aspectRatio];

  return (
    <div
      className={cn(
        "group relative overflow-hidden rounded-lg bg-muted/30",
        aspectRatioClass,
        className,
        onClick && "cursor-pointer"
      )}
      onClick={onClick}
    >
      {/* Loading state */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Skeleton className="h-full w-full" />
        </div>
      )}

      {/* Error state */}
      {hasError ? (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-muted/20 p-4">
          <AlertCircle className="mb-2 h-10 w-10 text-muted-foreground/50" />
          <p className="text-center text-sm text-muted-foreground">
            Failed to load thumbnail
          </p>
          <Button
            variant="ghost"
            size="sm"
            className="mt-2"
            onClick={(e) => {
              e.stopPropagation();
              window.open(src, "_blank");
            }}
          >
            <ExternalLink className="mr-2 h-4 w-4" />
            Open URL
          </Button>
        </div>
      ) : (
        <>
          {/* Thumbnail image */}
          <div
            className={cn(
              "relative h-full w-full transition-all duration-300",
              isLoading ? "scale-105 blur-sm" : "scale-100 blur-0"
            )}
          >
            <Image
              src={src}
              alt={alt}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              onLoad={handleLoad}
              onError={handleError}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>

          {/* Overlay gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

          {/* Play button */}
          {showPlayButton && (
            <div className="absolute inset-0 flex items-center justify-center">
              <Button
                size="icon"
                variant="ghost"
                className="h-12 w-12 rounded-full bg-background/20 text-white backdrop-blur-sm transition-all duration-300 hover:bg-background/40 group-hover:scale-110 group-hover:opacity-100 sm:opacity-0"
                onClick={(e) => {
                  e.stopPropagation();
                  onClick?.();
                }}
              >
                <Play className="h-6 w-6" />
              </Button>
            </div>
          )}

          {/* Premium badge */}
          {isPremium && (
            <Badge
              variant="default"
              className="absolute right-2 top-2 bg-primary/80 backdrop-blur-sm"
            >
              Premium
            </Badge>
          )}
        </>
      )}
    </div>
  );
}
