'use client';

import { useCallback, useRef, useState, useEffect } from 'react';
import { NatureSoundPlayer } from './types';

// Type for localStorage volume data
type IndependentNatureSoundVolumes = Record<string, number>;

// Load nature sound volumes from localStorage
const loadIndependentNatureSoundVolumes = (): IndependentNatureSoundVolumes => {
  if (typeof window !== 'undefined') {
    try {
      const saved = localStorage.getItem('independentNatureSoundVolumes');
      if (saved) {
        const parsed = JSON.parse(saved);
        // Validate that it's an object with number values
        if (typeof parsed === 'object' && parsed !== null) {
          const volumes: IndependentNatureSoundVolumes = {};
          for (const [key, value] of Object.entries(parsed)) {
            if (typeof value === 'number' && value >= 0 && value <= 100) {
              volumes[key] = value;
            }
          }
          return volumes;
        }
      }
    } catch (e) {
      console.error('Failed to load independent nature sound volumes:', e);
    }
  }
  return {};
};

// Save nature sound volumes to localStorage
const saveIndependentNatureSoundVolumes = (volumes: IndependentNatureSoundVolumes): void => {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem('independentNatureSoundVolumes', JSON.stringify(volumes));
    } catch (e) {
      console.error('Failed to save independent nature sound volumes:', e);
    }
  }
};

interface IndependentNatureSound {
  id: string;
  title: string;
  src?: string | null;
  category?: string[];
}

interface UseIndependentNatureSoundsProps {
  natureSounds: IndependentNatureSound[];
}

/**
 * Independent nature sounds hook that operates completely separate from global audio control
 */
export function useIndependentNatureSounds({ natureSounds }: UseIndependentNatureSoundsProps) {
  const [players, setPlayers] = useState<NatureSoundPlayer[]>([]);
  const audioElementsMapRef = useRef(new Map<string, HTMLAudioElement>());
  const previousNatureSoundsRef = useRef<string>('');

  // Cleanup all players
  const cleanupPlayers = useCallback(() => {
    // Stop all audio elements
    audioElementsMapRef.current.forEach(audio => {
      audio.pause();
      audio.src = '';
      audio.onended = null;
      audio.ontimeupdate = null;
    });
    audioElementsMapRef.current.clear();

    setPlayers([]);
  }, []);

  // Initialize players when natureSounds change
  useEffect(() => {
    const currentSoundIds = natureSounds.map(s => s.id).sort().join(',');

    // Only update if the sound IDs actually changed
    if (previousNatureSoundsRef.current === currentSoundIds) {
      return;
    }

    previousNatureSoundsRef.current = currentSoundIds;

    if (!natureSounds || natureSounds.length === 0) {
      // Cleanup existing players
      cleanupPlayers();
      return;
    }

    // Load saved volumes from localStorage
    const savedVolumes = loadIndependentNatureSoundVolumes();

    // Create players for new sounds
    const newPlayers = natureSounds.map(sound => {
      // Check if we already have an audio element for this sound
      let audioElement: HTMLAudioElement;

      if (audioElementsMapRef.current.has(sound.id)) {
        // Reuse existing audio element
        audioElement = audioElementsMapRef.current.get(sound.id)!;
        audioElement.pause();
        audioElement.currentTime = 0;
      } else {
        // Create new audio element
        audioElement = new Audio();
        audioElement.loop = true;
        audioElement.preload = 'metadata';
        
        // Set source if available
        if (sound.src) {
          audioElement.src = sound.src;
        }

        // Store in our map
        audioElementsMapRef.current.set(sound.id, audioElement);
      }

      // Add data attribute for identification
      audioElement.dataset.independentNatureSound = sound.id;

      // Use saved volume if available, otherwise default to 50
      const savedVolume = savedVolumes[sound.id];
      const initialVolume = savedVolume !== undefined ? savedVolume : 50;

      return {
        id: sound.id,
        title: sound.title,
        audioElement,
        isPlaying: false,
        volume: initialVolume,
        isMuted: false
      };
    });

    setPlayers(newPlayers);

    // Cleanup function
    return () => {
      // Don't cleanup on unmount, only when natureSounds change
    };
  }, [natureSounds, cleanupPlayers]);

  // Toggle sound playback
  const toggleSound = useCallback((id: string) => {
    setPlayers(prev =>
      prev.map(player => {
        if (player.id === id) {
          const newIsPlaying = !player.isPlaying;

          if (player.audioElement) {
            if (newIsPlaying) {
              // Set volume based on mute state
              player.audioElement.volume = player.isMuted ? 0 : player.volume / 100;
              player.audioElement.play().catch(() => {
                // Handle play error silently
              });
            } else {
              player.audioElement.pause();
            }
          }

          return { ...player, isPlaying: newIsPlaying };
        }
        return player;
      })
    );
  }, []);

  // Handle volume change
  const handleVolumeChange = useCallback((id: string, value: number[]) => {
    const newVolume = value[0];

    // Save volume to localStorage immediately
    const currentVolumes = loadIndependentNatureSoundVolumes();
    currentVolumes[id] = newVolume;
    saveIndependentNatureSoundVolumes(currentVolumes);

    setPlayers(prev =>
      prev.map(player => {
        if (player.id === id) {
          // Update audio element volume if not muted and playing
          if (player.audioElement && !player.isMuted) {
            player.audioElement.volume = newVolume / 100;
          }

          return { ...player, volume: newVolume };
        }
        return player;
      })
    );
  }, []);

  // Toggle mute
  const toggleMute = useCallback((id: string) => {
    setPlayers(prev =>
      prev.map(player => {
        if (player.id === id) {
          const newIsMuted = !player.isMuted;

          if (player.audioElement) {
            if (newIsMuted) {
              player.audioElement.volume = 0;
            } else {
              player.audioElement.volume = player.volume / 100;
            }
          }

          return { ...player, isMuted: newIsMuted };
        }
        return player;
      })
    );
  }, []);

  // Pause all nature sounds
  const pauseAll = useCallback(() => {
    setPlayers(prev =>
      prev.map(player => {
        if (player.isPlaying && player.audioElement) {
          player.audioElement.pause();
        }
        return { ...player, isPlaying: false };
      })
    );
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupPlayers();
    };
  }, [cleanupPlayers]);

  // Get playing count
  const playingCount = players.filter(player => player.isPlaying).length;

  return {
    players,
    playingCount,
    toggleSound,
    handleVolumeChange,
    toggleMute,
    pauseAll,
    cleanupPlayers,

    // Function to pause all currently playing nature sounds
    pauseAllPlaying: () => {
      setPlayers(prev =>
        prev.map(player => {
          if (player.isPlaying && player.audioElement) {
            player.audioElement.pause();
          }
          return { ...player, isPlaying: false };
        })
      );
    }
  };
}
