'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Bell, X, BellRing } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { notificationService } from '@/lib/notification-service';

interface NotificationPermissionPromptProps {
  isTimerRunning: boolean;
  onDismiss: () => void;
}

// Constants
const DISMISSAL_PERIOD_DAYS = 3; // Show prompt again after 3 days
const DISMISSAL_STORAGE_KEY = 'notification-prompt-last-dismissed';

export function NotificationPermissionPrompt({
  isTimerRunning,
  onDismiss
}: NotificationPermissionPromptProps) {
  const [permissionStatus, setPermissionStatus] = useState<NotificationPermission>('default');
  const [isVisible, setIsVisible] = useState(false);
  const [isRequesting, setIsRequesting] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Check if enough time has passed since last dismissal
  const canShowPrompt = (): boolean => {
    const lastDismissedStr = localStorage.getItem(DISMISSAL_STORAGE_KEY);
    
    if (!lastDismissedStr) {
      // Never been dismissed, can show
      return true;
    }

    try {
      const lastDismissed = new Date(lastDismissedStr);
      const now = new Date();
      const daysSinceLastDismissal = (now.getTime() - lastDismissed.getTime()) / (1000 * 60 * 60 * 24);
      
      return daysSinceLastDismissal >= DISMISSAL_PERIOD_DAYS;
    } catch (error) {
      // Invalid date format, treat as never dismissed
      localStorage.removeItem(DISMISSAL_STORAGE_KEY);
      return true;
    }
  };

  // Check permission status on mount and when timer starts
  useEffect(() => {
    if (notificationService) {
      const status = notificationService.getPermissionStatus();
      setPermissionStatus(status);

      // Clear any existing timer
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }

      // Show prompt after a delay if conditions are met
      if (
        isTimerRunning &&
        status !== 'granted' &&
        notificationService.isSupported() &&
        canShowPrompt()
      ) {
        // Show prompt after 3 seconds of timer running to avoid being intrusive
        timerRef.current = setTimeout(() => {
          setIsVisible(true);
        }, 3000);
      } else {
        setIsVisible(false);
      }
    }

    // Cleanup function
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isTimerRunning]);

  // Handle permission request
  const handleRequestPermission = async () => {
    if (!notificationService) {
      console.log('Notification service not available');
      return;
    }

    setIsRequesting(true);
    console.log('Requesting notification permission...');

    try {
      // Check if notifications are supported
      if (!('Notification' in window)) {
        console.log('Notifications not supported in this browser');
        setIsRequesting(false);
        return;
      }

      // Request permission
      const permission = await Notification.requestPermission();
      console.log('Permission result:', permission);

      // Update our state with the new permission
      setPermissionStatus(permission);

      if (permission === 'granted') {
        console.log('Permission granted!');
        // Clear dismissal timestamp since user granted permission
        localStorage.removeItem(DISMISSAL_STORAGE_KEY);
        // Hide prompt after successful permission grant
        setIsVisible(false);
        onDismiss();
      } else if (permission === 'denied') {
        console.log('Permission denied');
        // Store dismissal timestamp for denied permission
        localStorage.setItem(DISMISSAL_STORAGE_KEY, new Date().toISOString());
        // Keep the dialog open to show the "blocked" message
        setIsRequesting(false);
      } else {
        console.log('Permission default/dismissed');
        // User dismissed the dialog without choosing
        setIsRequesting(false);
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      setIsRequesting(false);
    }
  };

  // Handle dismiss
  const handleDismiss = () => {
    // Save dismissal timestamp to localStorage
    localStorage.setItem(DISMISSAL_STORAGE_KEY, new Date().toISOString());
    setIsVisible(false);
    onDismiss();
  };

  // Test notification
  const handleTestNotification = () => {
    if (notificationService && permissionStatus === 'granted') {
      notificationService.notify({
        title: 'Test Notification',
        body: 'Notifications are working! 🎉',
        icon: '/favicon.ico'
      });
    }
  };

  // Get days remaining until next prompt (for development)
  const getDaysUntilNextPrompt = (): number => {
    const lastDismissedStr = localStorage.getItem(DISMISSAL_STORAGE_KEY);
    if (!lastDismissedStr) return 0;
    
    try {
      const lastDismissed = new Date(lastDismissedStr);
      const now = new Date();
      const daysSinceLastDismissal = (now.getTime() - lastDismissed.getTime()) / (1000 * 60 * 60 * 24);
      return Math.max(0, DISMISSAL_PERIOD_DAYS - daysSinceLastDismissal);
    } catch {
      return 0;
    }
  };

  // Don't render if not supported
  if (!notificationService?.isSupported()) {
    return null;
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 10, scale: 0.95 }}
          transition={{
            duration: 0.3,
            ease: "easeOut",
            scale: { duration: 0.2 }
          }}
          className="fixed bottom-20 left-1/2 transform -translate-x-1/2 z-40 max-w-sm w-full mx-4"
        >
          <div className="bg-background/95 backdrop-blur-md border border-border rounded-lg shadow-lg p-4">
            {/* Header */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-2">
                <div className="p-1.5 rounded-full bg-primary/10">
                  <BellRing className="h-4 w-4 text-primary" />
                </div>
                <h3 className="font-medium text-sm">Enable Notifications</h3>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 -mt-1 -mr-1"
                onClick={handleDismiss}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>

            {/* Content */}
            <div className="mb-4">
              <p className="text-xs text-muted-foreground leading-relaxed">
                Get notified when your Pomodoro sessions complete to stay on track with your productivity goals.
              </p>
            </div>

            {/* Actions */}
            {permissionStatus === 'granted' ? (
              <div className="flex gap-2">
                <div className="flex-1 text-xs text-green-600 font-medium">
                  ✓ Notifications enabled!
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  className="text-xs"
                  onClick={handleDismiss}
                >
                  Close
                </Button>
              </div>
            ) : (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1 text-xs"
                  onClick={handleDismiss}
                >
                  Maybe later
                </Button>
                <Button
                  size="sm"
                  className="flex-1 text-xs"
                  onClick={handleRequestPermission}
                  disabled={isRequesting}
                >
                  <Bell className="h-3 w-3 mr-1.5" />
                  {isRequesting ? 'Requesting...' : 'Enable'}
                </Button>
              </div>
            )}

            {/* Debug info for development */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-2 p-2 bg-muted/50 rounded text-[10px] text-muted-foreground">
                <div>Status: {permissionStatus} | Supported: {notificationService?.isSupported() ? 'Yes' : 'No'}</div>
                <div>Days until next prompt: {getDaysUntilNextPrompt().toFixed(1)}</div>
                <div className="flex gap-1 mt-1">
                  {permissionStatus === 'granted' && (
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-[10px] h-6"
                      onClick={handleTestNotification}
                    >
                      Test Notification
                    </Button>
                  )}
                  <Button
                    size="sm"
                    variant="outline"
                    className="text-[10px] h-6"
                    onClick={() => {
                      localStorage.removeItem(DISMISSAL_STORAGE_KEY);
                      window.location.reload();
                    }}
                  >
                    Reset Timer
                  </Button>
                </div>
              </div>
            )}

            {/* Permission status indicator */}
            {permissionStatus === 'denied' && (
              <div className="mt-3 pt-3 border-t border-border/50">
                <p className="text-[10px] text-muted-foreground">
                  Notifications were blocked. You can enable them in your browser settings.
                </p>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
