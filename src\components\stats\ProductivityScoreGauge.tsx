"use client"

import { useEffect, useRef, useState } from "react"

interface ProductivityScoreGaugeProps {
  score: number
}

export function ProductivityScoreGauge({ score }: ProductivityScoreGaugeProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isAnimating, setIsAnimating] = useState(true)
  const [currentScore, setCurrentScore] = useState(0)

  useEffect(() => {
    const animationDuration = 1500 // ms
    const startTime = Date.now()

    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / animationDuration, 1)

      setCurrentScore(Math.floor(progress * score))

      if (progress < 1) {
        requestAnimationFrame(animate)
      } else {
        setIsAnimating(false)
      }
    }

    animate()
  }, [score])

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    const size = 180
    canvas.width = size
    canvas.height = size

    // Calculate center and radius
    const centerX = size / 2
    const centerY = size / 2
    const radius = size * 0.4

    // Clear canvas
    ctx.clearRect(0, 0, size, size)

    // Draw background arc
    ctx.beginPath()
    ctx.arc(centerX, centerY, radius, Math.PI * 0.75, Math.PI * 2.25, false)
    ctx.lineWidth = 10
    ctx.strokeStyle = "#1e293b" // slate-800
    ctx.stroke()

    // Calculate score angle
    const scoreAngle = (currentScore / 100) * Math.PI * 1.5 + Math.PI * 0.75

    // Draw score arc
    ctx.beginPath()
    ctx.arc(centerX, centerY, radius, Math.PI * 0.75, scoreAngle, false)
    ctx.lineWidth = 10

    // Create gradient based on score
    let gradient
    if (currentScore < 40) {
      gradient = ctx.createLinearGradient(0, 0, size, size)
      gradient.addColorStop(0, "#f43f5e") // rose-500
      gradient.addColorStop(1, "#f97316") // orange-500
    } else if (currentScore < 70) {
      gradient = ctx.createLinearGradient(0, 0, size, size)
      gradient.addColorStop(0, "#f97316") // orange-500
      gradient.addColorStop(1, "#facc15") // yellow-400
    } else {
      gradient = ctx.createLinearGradient(0, 0, size, size)
      gradient.addColorStop(0, "#facc15") // yellow-400
      gradient.addColorStop(1, "#10b981") // emerald-500
    }

    ctx.strokeStyle = gradient
    ctx.stroke()

    // Draw center circle
    ctx.beginPath()
    ctx.arc(centerX, centerY, radius - 20, 0, Math.PI * 2, false)
    ctx.fillStyle = "#0f172a" // slate-900
    ctx.fill()

    // Draw score text
    ctx.font = "bold 36px Inter, sans-serif"
    ctx.fillStyle = "#ffffff"
    ctx.textAlign = "center"
    ctx.textBaseline = "middle"
    ctx.fillText(`${currentScore}`, centerX, centerY - 5)

    // Draw "score" text
    ctx.font = "14px Inter, sans-serif"
    ctx.fillStyle = "#94a3b8" // slate-400
    ctx.fillText("SCORE", centerX, centerY + 20)
  }, [currentScore])

  // Determine score rating text
  const getScoreRating = () => {
    if (score < 40) return "Needs Improvement"
    if (score < 70) return "Good"
    if (score < 85) return "Great"
    return "Excellent"
  }

  // Determine score rating color
  const getScoreRatingColor = () => {
    if (score < 40) return "text-rose-500"
    if (score < 70) return "text-amber-500"
    if (score < 85) return "text-yellow-400"
    return "text-emerald-500"
  }

  return (
    <div className="flex flex-col items-center">
      <canvas ref={canvasRef} width="180" height="180" />
      <p className={`mt-2 text-sm font-medium ${getScoreRatingColor()}`}>{getScoreRating()}</p>
    </div>
  )
}
