"use client"

import { useState, use<PERSON><PERSON>back, use<PERSON>em<PERSON>, useRef, useEffect } from "react"
import { motion, AnimatePresence, useReducedMotion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChevronLeft, ChevronRight, Calendar, Clock, Filter, Check, AlertTriangle, Sparkles, TrendingUp } from "lucide-react"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface Session {
  id: string
  title: string
  type: "focus" | "shortBreak" | "longBreak" | "short_break" | "long_break"
  startTime: string
  endTime: string
  completed: boolean
  duration?: number // in minutes
  productivity?: number // 1-5 rating
  notes?: string
}

interface HorizontalSessionTimelineProps {
  sessions: Session[]
  date?: string
  onSessionSelect?: (session: Session | null) => void
  onDateChange?: (date: string) => void
  className?: string
  showStats?: boolean
  isLoading?: boolean
}

// Animation variants for enhanced UX
const containerVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: [0.4, 0.0, 0.2, 1],
      staggerChildren: 0.1
    }
  }
}

const cardVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: [0.4, 0.0, 0.2, 1]
    }
  }
}

const sessionBlockVariants = {
  hidden: { opacity: 0, scale: 0.8, y: 10 },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.2,
      ease: [0.4, 0.0, 0.2, 1]
    }
  },
  hover: {
    scale: 1.05,
    y: -2,
    transition: {
      duration: 0.2,
      ease: [0.4, 0.0, 0.2, 1]
    }
  },
  tap: {
    scale: 0.98,
    transition: {
      duration: 0.1,
      ease: [0.4, 0.0, 0.2, 1]
    }
  }
}

const filterButtonVariants = {
  inactive: { scale: 1, opacity: 0.7 },
  active: {
    scale: 1.05,
    opacity: 1,
    transition: {
      duration: 0.2,
      ease: [0.4, 0.0, 0.2, 1]
    }
  },
  hover: {
    scale: 1.02,
    transition: {
      duration: 0.15,
      ease: [0.4, 0.0, 0.2, 1]
    }
  }
}

// Normalize session type to handle both API formats
const normalizeSessionType = (type: string): "focus" | "shortBreak" | "longBreak" => {
  switch (type) {
    case "short_break":
      return "shortBreak"
    case "long_break":
      return "longBreak"
    case "focus":
    case "shortBreak":
    case "longBreak":
      return type as "focus" | "shortBreak" | "longBreak"
    default:
      return "focus"
  }
}

export function HorizontalSessionTimeline({
  sessions,
  date,
  onSessionSelect,
  onDateChange,
  className,
  showStats = true,
  isLoading = false
}: HorizontalSessionTimelineProps) {
  const [selectedDate, setSelectedDate] = useState(date || new Date().toISOString().split("T")[0])
  const [selectedSession, setSelectedSession] = useState<Session | null>(null)
  const [showFilters, setShowFilters] = useState(false)
  const [filterType, setFilterType] = useState<"all" | "focus" | "break">("all")

  // Use Framer Motion's useReducedMotion hook
  const shouldReduceMotion = useReducedMotion()

  const timelineRef = useRef<HTMLDivElement>(null)
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  // Update current time indicator every minute
  const [currentTime, setCurrentTime] = useState(new Date())
  
  useEffect(() => {
    // Only update if viewing today
    if (selectedDate !== new Date().toISOString().split("T")[0]) {
      return
    }

    const updateCurrentTime = () => {
      setCurrentTime(new Date())
    }

    // Update immediately
    updateCurrentTime()

    // Set up interval to update every minute
    const interval = setInterval(updateCurrentTime, 60000)

    return () => clearInterval(interval)
  }, [selectedDate])

  // Enhanced date change handler with callback
  const handleDateChange = useCallback((newDate: string) => {
    setSelectedDate(newDate)
    onDateChange?.(newDate)
  }, [onDateChange])

  // Scroll to current time
  const scrollToCurrentTime = useCallback(() => {
    if (!scrollContainerRef.current) return

    const currentMinutes = currentTime.getHours() * 60 + currentTime.getMinutes()
    const scrollPosition = (currentMinutes / 1440) * 2400 - (scrollContainerRef.current.clientWidth / 2)

    scrollContainerRef.current.scrollTo({
      left: Math.max(0, scrollPosition),
      behavior: shouldReduceMotion ? 'auto' : 'smooth'
    })
  }, [currentTime, shouldReduceMotion])

  // Scroll to a specific session
  const scrollToSession = useCallback((session: Session) => {
    if (!scrollContainerRef.current) return

    const startTime = new Date(session.startTime)
    const startMinutes = startTime.getHours() * 60 + startTime.getMinutes()
    const scrollPosition = (startMinutes / 1440) * 2400 - (scrollContainerRef.current.clientWidth / 2)

    scrollContainerRef.current.scrollTo({
      left: Math.max(0, scrollPosition),
      behavior: shouldReduceMotion ? 'auto' : 'smooth'
    })
  }, [shouldReduceMotion])

  // Enhanced session selection with auto-scroll
  const handleSessionSelect = useCallback((session: Session | null) => {
    setSelectedSession(session)
    onSessionSelect?.(session)
    
    if (session) {
      scrollToSession(session)
    }
  }, [onSessionSelect, scrollToSession])

  // Format date for display
  const formatDisplayDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric",
    })
  }

  // Navigate to previous day
  const goToPreviousDay = useCallback(() => {
    const currentDate = new Date(selectedDate)
    currentDate.setDate(currentDate.getDate() - 1)
    handleDateChange(currentDate.toISOString().split("T")[0])
  }, [selectedDate, handleDateChange])

  // Navigate to next day
  const goToNextDay = useCallback(() => {
    const currentDate = new Date(selectedDate)
    currentDate.setDate(currentDate.getDate() + 1)
    handleDateChange(currentDate.toISOString().split("T")[0])
  }, [selectedDate, handleDateChange])

  // Go to today
  const goToToday = useCallback(() => {
    handleDateChange(new Date().toISOString().split("T")[0])
  }, [handleDateChange])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target !== document.body) return // Only handle when no input is focused

      switch (e.key) {
        case 'ArrowLeft':
          if (e.shiftKey && scrollContainerRef.current) {
            // Shift + Left: Scroll left in timeline
            e.preventDefault()
            scrollContainerRef.current.scrollBy({ left: -200, behavior: shouldReduceMotion ? 'auto' : 'smooth' })
          } else {
            // Left: Previous day
            e.preventDefault()
            goToPreviousDay()
          }
          break
        case 'ArrowRight':
          if (e.shiftKey && scrollContainerRef.current) {
            // Shift + Right: Scroll right in timeline
            e.preventDefault()
            scrollContainerRef.current.scrollBy({ left: 200, behavior: shouldReduceMotion ? 'auto' : 'smooth' })
          } else {
            // Right: Next day
            e.preventDefault()
            goToNextDay()
          }
          break
        case 'Home':
          if (e.shiftKey) {
            // Shift + Home: Scroll to start of timeline
            e.preventDefault()
            scrollContainerRef.current?.scrollTo({ left: 0, behavior: shouldReduceMotion ? 'auto' : 'smooth' })
          } else {
            // Home: Go to today
            e.preventDefault()
            goToToday()
          }
          break
        case 'End':
          if (e.shiftKey && scrollContainerRef.current) {
            // Shift + End: Scroll to end of timeline
            e.preventDefault()
            scrollContainerRef.current.scrollTo({
              left: scrollContainerRef.current.scrollWidth,
              behavior: shouldReduceMotion ? 'auto' : 'smooth'
            })
          }
          break
        case 'Escape':
          e.preventDefault()
          handleSessionSelect(null)
          break
        case '.':
          // Period: Scroll to current time (if viewing today)
          if (selectedDate === new Date().toISOString().split("T")[0]) {
            e.preventDefault()
            scrollToCurrentTime()
          }
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [goToPreviousDay, goToNextDay, goToToday, handleSessionSelect, selectedDate, scrollToCurrentTime, shouldReduceMotion])

  // Memoized filtered sessions with type filtering
  const filteredSessions = useMemo(() => {
    let filtered = sessions.filter(session => {
      const sessionDate = new Date(session.startTime).toISOString().split('T')[0]
      return sessionDate === selectedDate
    })

    // Apply type filter
    if (filterType !== "all") {
      filtered = filtered.filter(session => {
        const normalizedType = normalizeSessionType(session.type)
        if (filterType === "focus") return normalizedType === "focus"
        if (filterType === "break") return normalizedType === "shortBreak" || normalizedType === "longBreak"
        return true
      })
    }

    return filtered.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime())
  }, [sessions, selectedDate, filterType])

  // Memoized session statistics
  const sessionStats = useMemo(() => {
    const totalSessions = filteredSessions.length
    const completedSessions = filteredSessions.filter(s => s.completed).length
    const focusSessions = filteredSessions.filter(s => normalizeSessionType(s.type) === "focus").length
    const totalFocusTime = filteredSessions
      .filter(s => normalizeSessionType(s.type) === "focus" && s.completed)
      .reduce((acc, session) => {
        const start = new Date(session.startTime)
        const end = new Date(session.endTime)
        return acc + (end.getTime() - start.getTime()) / (1000 * 60) // minutes
      }, 0)

    return {
      totalSessions,
      completedSessions,
      focusSessions,
      totalFocusTime: Math.round(totalFocusTime),
      completionRate: totalSessions > 0 ? Math.round((completedSessions / totalSessions) * 100) : 0
    }
  }, [filteredSessions])

  // Generate time markers for the timeline (every hour)
  const generateTimeMarkers = () => {
    const markers = []
    for (let hour = 0; hour < 24; hour += 1) {
      const time = `${hour.toString().padStart(2, "0")}:00`
      const label = hour === 0 ? "12 AM" : hour === 12 ? "12 PM" : hour > 12 ? `${hour - 12} PM` : `${hour} AM`
      markers.push({ time, label, hour })
    }
    return markers
  }

  // Calculate session position and width
  const getSessionStyle = (session: Session) => {
    const startTime = new Date(session.startTime)
    const endTime = new Date(session.endTime)

    const startMinutes = startTime.getHours() * 60 + startTime.getMinutes()
    const endMinutes = endTime.getHours() * 60 + endTime.getMinutes()
    const duration = endMinutes - startMinutes

    // Timeline spans 24 hours (1440 minutes), container width is 2400px at 1x zoom
    const leftPercent = (startMinutes / 1440) * 100
    const widthPercent = (duration / 1440) * 100

    return {
      left: `${leftPercent}%`,
      width: `${widthPercent}%`,
    }
  }



  // Get session background color
  const getSessionColor = (session: Session) => {
    const normalizedType = normalizeSessionType(session.type)
    const baseColors = {
      focus: session.completed ? "bg-rose-500" : "bg-rose-400",
      shortBreak: session.completed ? "bg-emerald-500" : "bg-emerald-400",
      longBreak: session.completed ? "bg-emerald-500" : "bg-emerald-400"
    }
    return baseColors[normalizedType]
  }

  // Get session text color
  const getSessionTextColor = (session: Session) => {
    const normalizedType = normalizeSessionType(session.type)
    const textColors = {
      focus: "text-white",
      shortBreak: "text-white",
      longBreak: "text-white"
    }
    return textColors[normalizedType]
  }

  // Format time for display
  const formatTime = (timeStr: string) => {
    const date = new Date(timeStr)
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  // Get session type label
  const getSessionTypeLabel = (type: string) => {
    const normalizedType = normalizeSessionType(type)
    const labels = {
      focus: "Focus Session",
      shortBreak: "Short Break",
      longBreak: "Long Break"
    }
    return labels[normalizedType]
  }

  const timeMarkers = generateTimeMarkers()

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn("w-full overflow-hidden", className)}
    >
      <Card className={cn(
        "relative overflow-hidden",
        "bg-gradient-to-br from-white via-white to-slate-50/30",
        "dark:from-slate-900 dark:via-slate-900 dark:to-slate-800/30",
        "border border-slate-200/60 dark:border-slate-800/60",
        "shadow-xl shadow-slate-900/5 dark:shadow-slate-900/20",
        "backdrop-blur-sm"
      )}>
        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-red-500/[0.02] via-transparent to-orange-500/[0.02] pointer-events-none" />

        <CardHeader className="relative pb-3">
          <motion.div
            variants={cardVariants}
            className="flex items-center justify-between"
          >
            <div className="flex-1">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2, duration: 0.4 }}
              >
                <CardTitle className="text-xl font-semibold flex items-center bg-gradient-to-r from-slate-900 to-slate-700 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent">
                  <motion.div
                    whileHover={{ rotate: 5, scale: 1.1 }}
                    transition={{ duration: 0.2 }}
                    className="mr-3 p-2 rounded-lg bg-gradient-to-br from-red-500/10 to-orange-500/10 border border-red-200/20 dark:border-red-800/20"
                  >
                    <Calendar className="h-5 w-5 text-red-600 dark:text-red-400" />
                  </motion.div>
                  Daily Session Timeline
                  <AnimatePresence>
                    {isLoading && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        className="ml-3"
                      >
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-red-500/30 border-t-red-500" />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </CardTitle>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.4 }}
              >
                <CardDescription className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                  Detailed view of your focus sessions throughout the day
                </CardDescription>
              </motion.div>

              <AnimatePresence>
                {showStats && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                    className="flex items-center gap-4 mt-2 text-xs text-slate-500 dark:text-slate-400"
                  >
                    <motion.span
                      whileHover={{ scale: 1.05 }}
                      className="flex items-center gap-1 px-2 py-1 rounded-full bg-slate-100 dark:bg-slate-800/50"
                    >
                      <Sparkles className="h-3 w-3 text-amber-500" />
                      {sessionStats.totalSessions} sessions
                    </motion.span>
                    <motion.span
                      whileHover={{ scale: 1.05 }}
                      className="flex items-center gap-1 px-2 py-1 rounded-full bg-slate-100 dark:bg-slate-800/50"
                    >
                      <Clock className="h-3 w-3 text-blue-500" />
                      {sessionStats.totalFocusTime}m focus
                    </motion.span>
                    <motion.span
                      whileHover={{ scale: 1.05 }}
                      className="flex items-center gap-1 px-2 py-1 rounded-full bg-slate-100 dark:bg-slate-800/50"
                    >
                      <TrendingUp className="h-3 w-3 text-green-500" />
                      {sessionStats.completionRate}% completion
                    </motion.span>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.4 }}
              className="flex items-center gap-3"
            >
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowFilters(!showFilters)}
                        className={cn(
                          "h-9 w-9 p-0 border-slate-200 dark:border-slate-700",
                          "bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm",
                          "hover:bg-red-50 dark:hover:bg-red-950/20",
                          "hover:border-red-200 dark:hover:border-red-800/40",
                          "transition-all duration-200",
                          showFilters && "bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800/40"
                        )}
                        aria-label="Toggle filters"
                      >
                        <motion.div
                          animate={{ rotate: showFilters ? 180 : 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          <Filter className="h-4 w-4 text-slate-600 dark:text-slate-400" />
                        </motion.div>
                      </Button>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent>Toggle filters</TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <div className="flex items-center bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden shadow-sm">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={goToPreviousDay}
                          className="h-9 w-9 p-0 rounded-none border-r border-slate-200 dark:border-slate-700 hover:bg-slate-100 dark:hover:bg-slate-700/50"
                          aria-label="Previous day"
                        >
                          <ChevronLeft className="h-4 w-4 text-slate-600 dark:text-slate-400" />
                        </Button>
                      </motion.div>
                    </TooltipTrigger>
                    <TooltipContent>Previous day (←)</TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={goToToday}
                          className="h-9 text-xs px-4 rounded-none border-r border-slate-200 dark:border-slate-700 hover:bg-slate-100 dark:hover:bg-slate-700/50 font-medium"
                          aria-label="Go to today"
                        >
                          Today
                        </Button>
                      </motion.div>
                    </TooltipTrigger>
                    <TooltipContent>Go to today (Home)</TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <AnimatePresence>
                  {selectedDate === new Date().toISOString().split("T")[0] && (
                    <motion.div
                      initial={{ width: 0, opacity: 0 }}
                      animate={{ width: "auto", opacity: 1 }}
                      exit={{ width: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={scrollToCurrentTime}
                                className="h-9 text-xs px-4 rounded-none border-r border-slate-200 dark:border-slate-700 hover:bg-red-50 dark:hover:bg-red-950/20 font-medium text-red-600 dark:text-red-400"
                                aria-label="Scroll to current time"
                              >
                                Now
                              </Button>
                            </motion.div>
                          </TooltipTrigger>
                          <TooltipContent>Scroll to current time</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </motion.div>
                  )}
                </AnimatePresence>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={goToNextDay}
                          className="h-9 w-9 p-0 rounded-none hover:bg-slate-100 dark:hover:bg-slate-700/50"
                          aria-label="Next day"
                        >
                          <ChevronRight className="h-4 w-4 text-slate-600 dark:text-slate-400" />
                        </Button>
                      </motion.div>
                    </TooltipTrigger>
                    <TooltipContent>Next day (→)</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </motion.div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.4 }}
            className="flex items-center justify-between mt-4"
          >
            <div className="text-sm font-medium text-slate-700 dark:text-slate-300">
              {formatDisplayDate(selectedDate)}
            </div>

            <AnimatePresence>
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, x: 20 }}
                  animate={{ opacity: 1, scale: 1, x: 0 }}
                  exit={{ opacity: 0, scale: 0.95, x: 20 }}
                  transition={{ duration: 0.2 }}
                  className="flex items-center gap-2"
                >
                  <div className="flex items-center bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden shadow-sm">
                    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                      <Button
                        variant={filterType === "all" ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setFilterType("all")}
                        className={cn(
                          "h-8 text-xs px-3 rounded-none border-r border-slate-200 dark:border-slate-700",
                          filterType === "all"
                            ? "bg-red-500 hover:bg-red-600 text-white"
                            : "hover:bg-slate-100 dark:hover:bg-slate-700/50"
                        )}
                      >
                        All
                      </Button>
                    </motion.div>
                    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                      <Button
                        variant={filterType === "focus" ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setFilterType("focus")}
                        className={cn(
                          "h-8 text-xs px-3 rounded-none border-r border-slate-200 dark:border-slate-700",
                          filterType === "focus"
                            ? "bg-red-500 hover:bg-red-600 text-white"
                            : "hover:bg-slate-100 dark:hover:bg-slate-700/50"
                        )}
                      >
                        Focus
                      </Button>
                    </motion.div>
                    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                      <Button
                        variant={filterType === "break" ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setFilterType("break")}
                        className={cn(
                          "h-8 text-xs px-3 rounded-none",
                          filterType === "break"
                            ? "bg-red-500 hover:bg-red-600 text-white"
                            : "hover:bg-slate-100 dark:hover:bg-slate-700/50"
                        )}
                      >
                        Breaks
                      </Button>
                    </motion.div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </CardHeader>
      <CardContent className="relative pt-0 pb-6">
        {/* Enhanced Loading State */}
        <AnimatePresence>
          {isLoading && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="flex flex-col items-center justify-center h-32 text-slate-600 dark:text-slate-400"
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                className="mb-3"
              >
                <div className="h-8 w-8 rounded-full border-3 border-red-200 dark:border-red-800/40 border-t-red-500 dark:border-t-red-400" />
              </motion.div>
              <motion.span
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="text-sm font-medium"
              >
                Loading sessions...
              </motion.span>
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: "100%" }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="mt-2 h-1 bg-gradient-to-r from-red-500/20 via-red-500 to-red-500/20 rounded-full max-w-24"
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Enhanced Timeline Container */}
        <AnimatePresence>
          {!isLoading && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.4, delay: 0.1 }}
              className="relative"
              ref={timelineRef}
            >
              {/* Enhanced Timeline Track */}
              <motion.div
                initial={{ scale: 0.98 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.3, delay: 0.2 }}
                className={cn(
                  "relative h-28 rounded-lg overflow-hidden",
                  "bg-gradient-to-br from-slate-50 via-white to-slate-50/50",
                  "dark:from-slate-900/50 dark:via-slate-800/30 dark:to-slate-900/50",
                  "border border-slate-200/60 dark:border-slate-700/60",
                  "shadow-inner shadow-slate-900/5 dark:shadow-slate-900/20",
                  // "focus-within:ring-1 focus-within:ring-red-500/20 focus-within:ring-offset-2",
                  "backdrop-blur-sm"
                )}
                role="application"
                aria-label="Session timeline"
              >
              {/* Horizontal scrollable area */}
              <div
                ref={scrollContainerRef}
                className={cn(
                  "relative w-full h-full overflow-x-auto overflow-y-hidden",
                  "scrollbar-thin scrollbar-thumb-border/50 scrollbar-track-transparent hover:scrollbar-thumb-border pb-2",
                  shouldReduceMotion ? "" : "scroll-smooth"
                )}
                style={{
                  scrollbarWidth: 'thin'
                }}
                tabIndex={0}
                role="region"
                aria-label="Timeline scroll area"
              >
                <div className="relative min-w-[2400px] h-full pb-6">
                {/* Horizontal line above time markers */}
                <div className="absolute bottom-6 left-0 right-0 h-px bg-border"></div>

                {/* Time markers */}
                <div className="absolute bottom-0 left-0 right-0 h-6">
                  {timeMarkers.map((marker) => (
                    <div
                      key={marker.time}
                      className="absolute flex flex-col items-center"
                      style={{ left: `${(marker.hour / 24) * 100}%` }}
                    >
                      <div className="w-px h-2 bg-border"></div>
                      <span className="text-xs text-muted-foreground mt-0.5 whitespace-nowrap select-none">{marker.label}</span>
                    </div>
                  ))}
                </div>

                {/* Enhanced Session blocks */}
                <div className="absolute top-2 left-0 right-0 h-[calc(100%-45px)]" role="group" aria-label="Session blocks">
                  <TooltipProvider>
                    <AnimatePresence>
                      {filteredSessions.map((session, index) => (
                        <motion.div
                          key={session.id}
                          variants={sessionBlockVariants}
                          initial="hidden"
                          animate="visible"
                          exit="hidden"
                          transition={{ delay: index * 0.05 }}
                          style={getSessionStyle(session)}
                          className="absolute top-0 h-full"
                        >
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <motion.button
                                whileHover={shouldReduceMotion ? {} : "hover"}
                                whileTap={shouldReduceMotion ? {} : "tap"}
                                className={cn(
                                  "w-full h-full rounded-lg cursor-pointer overflow-hidden",
                                  "focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:ring-offset-1",
                                  "transition-all duration-200 group relative",
                                  "flex items-center justify-center",
                                  "shadow-sm hover:shadow-md",
                                  selectedSession?.id === session.id
                                    ? "ring-2 ring-red-500 ring-offset-1 z-20 shadow-lg scale-105"
                                    : "hover:ring-1 hover:ring-red-400/50",
                                  getSessionColor(session)
                                )}
                                onClick={() => handleSessionSelect(session)}
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter' || e.key === ' ') {
                                    e.preventDefault()
                                    handleSessionSelect(session)
                                  }
                                }}
                                aria-label={`${getSessionTypeLabel(session.type)} session from ${formatTime(session.startTime)} to ${formatTime(session.endTime)}, ${session.completed ? 'completed' : 'interrupted'}`}
                                aria-pressed={selectedSession?.id === session.id}
                                tabIndex={0}
                              >
                                {/* Subtle gradient overlay */}
                                <div className="absolute inset-0 bg-gradient-to-r from-white/10 via-transparent to-white/5 pointer-events-none" />

                                {/* Completion indicator */}
                                {!session.completed && (
                                  <div className="absolute top-0 right-0 w-2 h-2 bg-amber-400 rounded-bl-lg" />
                                )}

                                <div className="flex items-center justify-center h-full px-2 min-w-0 w-full relative z-10">
                                  <span className={cn("text-xs font-semibold drop-shadow-sm truncate", getSessionTextColor(session))}>
                                    {(() => {
                                      const duration = Math.round((new Date(session.endTime).getTime() - new Date(session.startTime).getTime()) / (1000 * 60))
                                      const style = getSessionStyle(session)
                                      const widthPx = parseFloat(style.width.replace('%', '')) * 2400 / 100

                                      // Show different content based on available space
                                      if (widthPx < 20) {
                                        // Very short sessions - show nothing or just dot
                                        return duration < 5 ? '•' : `${duration}m`
                                      } else if (widthPx < 60) {
                                        // Short sessions - show duration
                                        return `${duration}m`
                                      } else {
                                        // Longer sessions - show full label
                                        return getSessionTypeLabel(session.type)
                                      }
                                    })()}
                                  </span>
                                  {session.productivity && getSessionStyle(session).width && parseFloat(getSessionStyle(session).width.replace('%', '')) * 2400 / 100 > 80 && (
                                    <div className="ml-2 flex">
                                      {Array.from({ length: session.productivity }, (_, i) => (
                                        <motion.div
                                          key={i}
                                          initial={{ scale: 0 }}
                                          animate={{ scale: 1 }}
                                          transition={{ delay: 0.1 + i * 0.05 }}
                                          className="w-1 h-1 bg-white/90 rounded-full ml-0.5"
                                        />
                                      ))}
                                    </div>
                                  )}
                                </div>
                              </motion.button>
                        </TooltipTrigger>
                        <TooltipContent side="top" className="max-w-xs bg-popover border border-border shadow-lg p-2 pb-0">
                          <div className="text-sm space-y-2 text-popover-foreground">
                            <div className="flex items-center justify-between">
                              <p className="font-semibold">{getSessionTypeLabel(session.type)}</p>
                              {session.completed ? (
                                <Check className="w-4 h-4 text-green-500" aria-label="Completed" />
                              ) : (
                                <AlertTriangle className="w-4 h-4 text-amber-500" aria-label="Interrupted" />
                              )}
                            </div>
                            <div className="space-y-1">
                              <p className="text-xs text-muted-foreground">
                                <span className="font-medium">Time:</span> {formatTime(session.startTime)} - {formatTime(session.endTime)}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                <span className="font-medium">Duration:</span> {Math.round((new Date(session.endTime).getTime() - new Date(session.startTime).getTime()) / (1000 * 60))} minutes
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              {session.productivity && (
                                <Badge variant="outline" className="text-xs border-muted-foreground">
                                  {session.productivity}/5 ⭐
                                </Badge>
                              )}
                            </div>
                            {session.notes && (
                              <div className="border-t border-border pt-2 mt-2">
                                <p className="text-xs text-muted-foreground">
                                  <span className="font-medium">Notes:</span> {session.notes}
                                </p>
                              </div>
                            )}
                          </div>
                        </TooltipContent>
                      </Tooltip>
                        </motion.div>
                      ))}
                    </AnimatePresence>
                  </TooltipProvider>
                </div>

                {/* Current time indicator (if viewing today) - moved inside timeline */}
                {selectedDate === new Date().toISOString().split("T")[0] && (
                  <div
                    // fix the timer height here, check later
                    className="absolute top-[1px] h-[calc(100%-30px)] pointer-events-none z-20"
                    style={{
                      left: `${((currentTime.getHours() * 60 + currentTime.getMinutes()) / 1440) * 100}%`
                    }}
                  >
                    {/* Red dot at top */}
                    <div className="absolute -top-0.7 -left-[3px] w-2 h-2 bg-red-500 rounded-full shadow-sm"></div>
                    {/* Vertical line */}
                    <div className="w-0.5 h-full bg-red-500"></div>
                  </div>
                )}
                </div>
              </div>
            </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Enhanced Session Summary */}
        <AnimatePresence>
          {!isLoading && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.4, delay: 0.2 }}
            >
              {filteredSessions.length > 0 ? (
                <div className="mt-6">
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="flex items-center justify-between mb-4"
                  >
                    <h3 className="text-lg font-semibold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent">
                      Session Details
                    </h3>
                    <AnimatePresence>
                      {showStats && (
                        <motion.div
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: 20 }}
                          className="flex items-center gap-3 text-xs text-slate-500 dark:text-slate-400"
                        >
                          <span className="flex items-center gap-1 px-2 py-1 rounded-full bg-slate-100 dark:bg-slate-800/50">
                            <TrendingUp className="h-3 w-3 text-blue-500" />
                            {sessionStats.focusSessions} focus
                          </span>
                          <span className="flex items-center gap-1 px-2 py-1 rounded-full bg-slate-100 dark:bg-slate-800/50">
                            <Clock className="h-3 w-3 text-green-500" />
                            {sessionStats.totalFocusTime}m total
                          </span>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>

                  <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
                    {filteredSessions.map((session, index) => (
                      <motion.button
                        key={session.id}
                        initial={{ opacity: 0, y: 20, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{ delay: 0.4 + index * 0.05, duration: 0.3 }}
                        whileHover={shouldReduceMotion ? {} : { scale: 1.02, y: -2 }}
                        whileTap={shouldReduceMotion ? {} : { scale: 0.98 }}
                        className={cn(
                          "rounded-xl border p-3 text-left transition-all duration-200 overflow-hidden",
                          "bg-gradient-to-br from-white via-white to-slate-50/30",
                          "dark:from-slate-800/50 dark:via-slate-800/30 dark:to-slate-900/50",
                          "backdrop-blur-sm shadow-sm hover:shadow-md",
                          selectedSession?.id === session.id
                            ? 'border-red-500 bg-red-50/50 dark:bg-red-950/20 shadow-md focus:outline-none focus:ring-2 focus:ring-red-600/60 focus:ring-offset-1'
                            : 'border-slate-200 dark:border-slate-700 hover:border-red-200 dark:hover:border-red-800/40 focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:ring-offset-2'
                        )}
                        onClick={() => handleSessionSelect(session)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault()
                            handleSessionSelect(session)
                          }
                        }}
                        aria-label={`Select ${getSessionTypeLabel(session.type)} session`}
                        aria-pressed={selectedSession?.id === session.id}
                      >
                      <div className="flex items-center justify-between mb-1.5">
                        <div className="flex items-center">
                          <div className={cn(
                            "w-3 h-3 rounded-full mr-2", 
                            selectedSession?.id === session.id 
                              ? "bg-slate-400 dark:bg-slate-500" // Neutral color when selected
                              : getSessionColor(session)
                          )}></div>
                          <span className="font-medium text-sm">{getSessionTypeLabel(session.type)}</span>
                        </div>
                        <div className="flex items-center gap-1.5">
                          {session.completed ? (
                            <Check className="w-4 h-4 text-green-500" aria-label="Completed" />
                          ) : (
                            <AlertTriangle className="w-4 h-4 text-amber-500" aria-label="Interrupted" />
                          )}
                          {session.productivity && (
                            <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                              {session.productivity}/5
                            </Badge>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <div className="flex items-center">
                          <Clock className="w-3 h-3 mr-1" />
                          {formatTime(session.startTime)} - {formatTime(session.endTime)}
                        </div>
                        <span className="font-medium">
                          {(() => {
                            // Calculate duration from startTime and endTime if duration is not provided
                            const duration = session.duration || Math.round(
                              (new Date(session.endTime).getTime() - new Date(session.startTime).getTime()) / (1000 * 60)
                            );
                            return `${duration}m`;
                          })()}
                        </span>
                      </div>

                      {session.notes && (
                        <div className="mt-2 text-xs text-slate-500 dark:text-slate-400 italic truncate">
                          "{session.notes}"
                        </div>
                      )}
                    </motion.button>
                  ))}
                </div>
              </div>
            ) : (
              <div className="mt-4 text-center py-8" role="status" aria-live="polite">
                <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
                <h3 className="text-lg font-medium text-foreground mb-1">No sessions recorded</h3>
                <p className="text-muted-foreground mb-0.5">
                  {filterType === "all"
                    ? "No sessions found for this day"
                    : `No ${filterType} sessions found for this day`
                  }
                </p>
                <p className="text-xs text-muted-foreground">
                  Start a focus session to see it appear here
                </p>
              </div>
            )}

            {/* Legend */}
            <div
              className="mt-6 pt-4 border-t border-border"
              role="group"
              aria-label="Session type legend"
            >
              <h4 className="text-sm font-medium text-foreground mb-2 text-center">Session Types</h4>
              <div className="flex flex-wrap items-center justify-center gap-4">
                <div className="flex items-center" role="listitem">
                  <div className="mr-2 h-3 w-3 rounded-full bg-rose-500" aria-hidden="true"></div>
                  <span className="text-xs text-muted-foreground">Focus Session</span>
                </div>
                <div className="flex items-center" role="listitem">
                  <div className="mr-2 h-3 w-3 rounded-full bg-emerald-500" aria-hidden="true"></div>
                  <span className="text-xs text-muted-foreground">Short Break</span>
                </div>
                <div className="flex items-center" role="listitem">
                  <div className="mr-2 h-3 w-3 rounded-full bg-emerald-500" aria-hidden="true"></div>
                  <span className="text-xs text-muted-foreground">Long Break</span>
                </div>
                <div className="flex items-center" role="listitem">
                  <div className="mr-2 h-3 w-3 rounded-full bg-muted border border-border" aria-hidden="true"></div>
                  <span className="text-xs text-muted-foreground">Interrupted</span>
                </div>
              </div>

              {/* Keyboard shortcuts hint */}
              <div className="mt-3 text-center">
                <p className="text-xs text-muted-foreground">
                  Use ← → to navigate days • Shift + ← → to scroll timeline • Home for today • Period (.) to scroll to now • Esc to deselect
                </p>
              </div>
            </div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
    </motion.div>
  )
}
