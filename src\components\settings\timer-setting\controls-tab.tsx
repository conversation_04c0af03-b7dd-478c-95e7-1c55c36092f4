'use client';

import { Switch } from '@/components/ui/switch';
import { ToggleR<PERSON>, <PERSON>, Maximize } from 'lucide-react';
import { ControlsTabProps } from './types';

export function ControlsTab({
  autoStartBreaks,
  setAutoStartBreaks,
  autoStartPomodoros,
  setAutoStartPomodoros,
  autoFullscreen,
  setAutoFullscreen
}: ControlsTabProps) {
  // Common section class for consistent spacing and styling
  const sectionClass = "space-y-1.5 mb-2.5";
  const headerClass = "flex items-center justify-between mb-1.5";
  const titleClass = "text-xs font-medium flex items-center gap-1.5 text-foreground/80";
  const descriptionClass = "text-[10px] text-muted-foreground leading-tight";

  return (
    <div className="space-y-2.5">
      {/* Auto Start Breaks Switch */}
      <div className={sectionClass}>
        <div className={headerClass}>
          <h3 className={titleClass}>
            <ToggleRight className="h-3 w-3 text-primary/70" />
            <span>Auto Start Breaks</span>
          </h3>
          <Switch
            size="sm"
            checked={autoStartBreaks}
            onCheckedChange={setAutoStartBreaks}
            className="data-[state=checked]:bg-primary"
          />
        </div>
        <p className={descriptionClass}>
          When enabled, break timers will start automatically after a focus session.
        </p>
      </div>

      {/* Auto Start Pomodoros Switch */}
      <div className={sectionClass}>
        <div className={headerClass}>
          <h3 className={titleClass}>
            <Bell className="h-3 w-3 text-primary/70" />
            <span>Auto Start Pomodoros</span>
          </h3>
          <Switch
            size="sm"
            checked={autoStartPomodoros}
            onCheckedChange={setAutoStartPomodoros}
            className="data-[state=checked]:bg-primary"
          />
        </div>
        <p className={descriptionClass}>
          When enabled, focus timers will start automatically when the page loads and after breaks.
        </p>
      </div>

      {/* Auto Fullscreen Switch */}
      <div className={sectionClass}>
        <div className={headerClass}>
          <h3 className={titleClass}>
            <Maximize className="h-3 w-3 text-primary/70" />
            <span>Auto-fullscreen</span>
          </h3>
          <Switch
            size="sm"
            checked={autoFullscreen}
            onCheckedChange={setAutoFullscreen}
            className="data-[state=checked]:bg-primary"
          />
        </div>
        <p className={descriptionClass}>
          Automatically enter fullscreen mode when starting a timer.
        </p>
      </div>
    </div>
  );
}
