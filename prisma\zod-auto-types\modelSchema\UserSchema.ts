import { z } from 'zod';
import { UserRoleSchema } from '../inputTypeSchemas/UserRoleSchema'
import { SubscriptionTypeSchema } from '../inputTypeSchemas/SubscriptionTypeSchema'
import { SessionWithRelationsSchema, SessionPartialWithRelationsSchema } from './SessionSchema'
import type { SessionWithRelations, SessionPartialWithRelations } from './SessionSchema'
import { AccountWithRelationsSchema, AccountPartialWithRelationsSchema } from './AccountSchema'
import type { AccountWithRelations, AccountPartialWithRelations } from './AccountSchema'
import { SubscriptionWithRelationsSchema, SubscriptionPartialWithRelationsSchema } from './SubscriptionSchema'
import type { SubscriptionWithRelations, SubscriptionPartialWithRelations } from './SubscriptionSchema'
import { PaymentWithRelationsSchema, PaymentPartialWithRelationsSchema } from './PaymentSchema'
import type { PaymentWithRelations, PaymentPartialWithRelations } from './PaymentSchema'
import { VideoWithRelationsSchema, VideoPartialWithRelationsSchema } from './VideoSchema'
import type { VideoWithRelations, VideoPartialWithRelations } from './VideoSchema'
import { MusicPlaylistWithRelationsSchema, MusicPlaylistPartialWithRelationsSchema } from './MusicPlaylistSchema'
import type { MusicPlaylistWithRelations, MusicPlaylistPartialWithRelations } from './MusicPlaylistSchema'
import { MusicPlaylistUserWithRelationsSchema, MusicPlaylistUserPartialWithRelationsSchema } from './MusicPlaylistUserSchema'
import type { MusicPlaylistUserWithRelations, MusicPlaylistUserPartialWithRelations } from './MusicPlaylistUserSchema'
import { NaturePlaylistWithRelationsSchema, NaturePlaylistPartialWithRelationsSchema } from './NaturePlaylistSchema'
import type { NaturePlaylistWithRelations, NaturePlaylistPartialWithRelations } from './NaturePlaylistSchema'
import { MusicWithRelationsSchema, MusicPartialWithRelationsSchema } from './MusicSchema'
import type { MusicWithRelations, MusicPartialWithRelations } from './MusicSchema'
import { NatureSoundWithRelationsSchema, NatureSoundPartialWithRelationsSchema } from './NatureSoundSchema'
import type { NatureSoundWithRelations, NatureSoundPartialWithRelations } from './NatureSoundSchema'
import { FavoriteVideoWithRelationsSchema, FavoriteVideoPartialWithRelationsSchema } from './FavoriteVideoSchema'
import type { FavoriteVideoWithRelations, FavoriteVideoPartialWithRelations } from './FavoriteVideoSchema'
import { PomodoroSessionWithRelationsSchema, PomodoroSessionPartialWithRelationsSchema } from './PomodoroSessionSchema'
import type { PomodoroSessionWithRelations, PomodoroSessionPartialWithRelations } from './PomodoroSessionSchema'
import { TaskWithRelationsSchema, TaskPartialWithRelationsSchema } from './TaskSchema'
import type { TaskWithRelations, TaskPartialWithRelations } from './TaskSchema'

/////////////////////////////////////////
// USER SCHEMA
/////////////////////////////////////////

export const UserSchema = z.object({
  role: UserRoleSchema,
  subscriptionType: SubscriptionTypeSchema,
  id: z.string().cuid(),
  name: z.string(),
  email: z.string(),
  emailVerified: z.boolean(),
  image: z.string().nullish(),
  premium: z.boolean(),
  banned: z.boolean(),
  banReason: z.string().nullish(),
  /**
   * Changed from Int to BigInt for Unix timestamp
   */
  banExpires: z.bigint().nullish(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type User = z.infer<typeof UserSchema>

/////////////////////////////////////////
// USER PARTIAL SCHEMA
/////////////////////////////////////////

export const UserPartialSchema = UserSchema.partial()

export type UserPartial = z.infer<typeof UserPartialSchema>

/////////////////////////////////////////
// USER RELATION SCHEMA
/////////////////////////////////////////

export type UserRelations = {
  sessions: SessionWithRelations[];
  accounts: AccountWithRelations[];
  subscriptions: SubscriptionWithRelations[];
  payments: PaymentWithRelations[];
  videos: VideoWithRelations[];
  musicPlaylists: MusicPlaylistWithRelations[];
  musicPlaylistsUser: MusicPlaylistUserWithRelations[];
  naturePlaylists: NaturePlaylistWithRelations[];
  musics: MusicWithRelations[];
  natureSounds: NatureSoundWithRelations[];
  favoriteVideos: FavoriteVideoWithRelations[];
  pomodoroSessions: PomodoroSessionWithRelations[];
  tasks: TaskWithRelations[];
};

export type UserWithRelations = z.infer<typeof UserSchema> & UserRelations

export const UserWithRelationsSchema: z.ZodType<UserWithRelations> = UserSchema.merge(z.object({
  sessions: z.lazy(() => SessionWithRelationsSchema).array(),
  accounts: z.lazy(() => AccountWithRelationsSchema).array(),
  subscriptions: z.lazy(() => SubscriptionWithRelationsSchema).array(),
  payments: z.lazy(() => PaymentWithRelationsSchema).array(),
  videos: z.lazy(() => VideoWithRelationsSchema).array(),
  musicPlaylists: z.lazy(() => MusicPlaylistWithRelationsSchema).array(),
  musicPlaylistsUser: z.lazy(() => MusicPlaylistUserWithRelationsSchema).array(),
  naturePlaylists: z.lazy(() => NaturePlaylistWithRelationsSchema).array(),
  musics: z.lazy(() => MusicWithRelationsSchema).array(),
  natureSounds: z.lazy(() => NatureSoundWithRelationsSchema).array(),
  favoriteVideos: z.lazy(() => FavoriteVideoWithRelationsSchema).array(),
  pomodoroSessions: z.lazy(() => PomodoroSessionWithRelationsSchema).array(),
  tasks: z.lazy(() => TaskWithRelationsSchema).array(),
}))

/////////////////////////////////////////
// USER PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type UserPartialRelations = {
  sessions?: SessionPartialWithRelations[];
  accounts?: AccountPartialWithRelations[];
  subscriptions?: SubscriptionPartialWithRelations[];
  payments?: PaymentPartialWithRelations[];
  videos?: VideoPartialWithRelations[];
  musicPlaylists?: MusicPlaylistPartialWithRelations[];
  musicPlaylistsUser?: MusicPlaylistUserPartialWithRelations[];
  naturePlaylists?: NaturePlaylistPartialWithRelations[];
  musics?: MusicPartialWithRelations[];
  natureSounds?: NatureSoundPartialWithRelations[];
  favoriteVideos?: FavoriteVideoPartialWithRelations[];
  pomodoroSessions?: PomodoroSessionPartialWithRelations[];
  tasks?: TaskPartialWithRelations[];
};

export type UserPartialWithRelations = z.infer<typeof UserPartialSchema> & UserPartialRelations

export const UserPartialWithRelationsSchema: z.ZodType<UserPartialWithRelations> = UserPartialSchema.merge(z.object({
  sessions: z.lazy(() => SessionPartialWithRelationsSchema).array(),
  accounts: z.lazy(() => AccountPartialWithRelationsSchema).array(),
  subscriptions: z.lazy(() => SubscriptionPartialWithRelationsSchema).array(),
  payments: z.lazy(() => PaymentPartialWithRelationsSchema).array(),
  videos: z.lazy(() => VideoPartialWithRelationsSchema).array(),
  musicPlaylists: z.lazy(() => MusicPlaylistPartialWithRelationsSchema).array(),
  musicPlaylistsUser: z.lazy(() => MusicPlaylistUserPartialWithRelationsSchema).array(),
  naturePlaylists: z.lazy(() => NaturePlaylistPartialWithRelationsSchema).array(),
  musics: z.lazy(() => MusicPartialWithRelationsSchema).array(),
  natureSounds: z.lazy(() => NatureSoundPartialWithRelationsSchema).array(),
  favoriteVideos: z.lazy(() => FavoriteVideoPartialWithRelationsSchema).array(),
  pomodoroSessions: z.lazy(() => PomodoroSessionPartialWithRelationsSchema).array(),
  tasks: z.lazy(() => TaskPartialWithRelationsSchema).array(),
})).partial()

export type UserWithPartialRelations = z.infer<typeof UserSchema> & UserPartialRelations

export const UserWithPartialRelationsSchema: z.ZodType<UserWithPartialRelations> = UserSchema.merge(z.object({
  sessions: z.lazy(() => SessionPartialWithRelationsSchema).array(),
  accounts: z.lazy(() => AccountPartialWithRelationsSchema).array(),
  subscriptions: z.lazy(() => SubscriptionPartialWithRelationsSchema).array(),
  payments: z.lazy(() => PaymentPartialWithRelationsSchema).array(),
  videos: z.lazy(() => VideoPartialWithRelationsSchema).array(),
  musicPlaylists: z.lazy(() => MusicPlaylistPartialWithRelationsSchema).array(),
  musicPlaylistsUser: z.lazy(() => MusicPlaylistUserPartialWithRelationsSchema).array(),
  naturePlaylists: z.lazy(() => NaturePlaylistPartialWithRelationsSchema).array(),
  musics: z.lazy(() => MusicPartialWithRelationsSchema).array(),
  natureSounds: z.lazy(() => NatureSoundPartialWithRelationsSchema).array(),
  favoriteVideos: z.lazy(() => FavoriteVideoPartialWithRelationsSchema).array(),
  pomodoroSessions: z.lazy(() => PomodoroSessionPartialWithRelationsSchema).array(),
  tasks: z.lazy(() => TaskPartialWithRelationsSchema).array(),
}).partial())

export default UserSchema;
