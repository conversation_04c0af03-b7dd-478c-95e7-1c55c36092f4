# Video Management System

This is a comprehensive video management system built with Next.js 15, React, TypeScript, and Shadcn UI components.

## Features

- **Video Listing**: Display videos in a sortable and filterable table
- **Create Video**: Add new videos via a right-side sheet interface
- **Edit Video**: Update existing videos with validation
- **Delete Video**: Remove videos with confirmation dialog
- **Filter Videos**: Filter by status (All, Public, Private, Premium)
- **Responsive Design**: Works on all device sizes

## Components Structure

- `page.tsx`: Main page component that renders the video management interface
- `/components/`: Directory containing all component files:
  - `video-management.tsx`: Container component handling state and queries
  - `video-table.tsx`: Data table component with sorting and actions
  - `video-form-sheet.tsx`: Right-side sheet form for creating/editing videos
  - `types.ts`: TypeScript type definitions for videos and forms

## Implementation Details

### Data Handling

The video management system interfaces with the backend API through TanStack Query hooks defined in `prisma/schema/Video/video-query.ts`:

- `useGetVideos()`: Fetches all videos with optional filters
- `useGetVideo(id)`: Fetches a single video by ID
- `useCreateVideo()`: Creates a new video
- `useUpdateVideo()`: Updates an existing video
- `useDeleteVideo()`: Deletes a video by ID

### Form Handling

The form component uses React state for form data and validation:

- Input validation includes required fields and valid URLs
- Form state is synchronized with the API
- Error handling includes user-friendly error messages
- Success notifications are shown via toast messages

### UI Components

The UI is built using Shadcn UI components:

- `Table` for listing videos
- `Sheet` for the slide-in form
- `Form` components for structured input handling
- `AlertDialog` for delete confirmation
- `Switch` for boolean toggles (Public/Premium)
- `Tabs` for filtering

## Usage

The video management system is accessible at `/videos` in the application. Users can:

1. View all videos in the table
2. Filter videos using the tab filter
3. Create new videos using the "New Video" button
4. Edit videos by clicking the edit button in the action menu
5. Delete videos with confirmation via the action menu