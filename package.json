{"name": "pomodoro-365", "version": "0.1.0", "private": true, "prisma": {"schema": "./prisma/schema"}, "scripts": {"dev": "next dev --turbo --port 2604", "build": "next build", "start": "next start --port 2604", "lint": "next lint", "prisma": "bunx prisma generate && bunx prisma db push", "begin": "git reset --hard HEAD && git pull && export NODE_OPTIONS=--max_old_space_size=8192 && bun install  && bunx prisma generate &&  bun run build && cross-env NODE_ENV=production pm2 start npm --name pomodoro365 -- start && pm2 logs pomodoro365", "deploy": "git reset --hard HEAD && git pull && export NODE_OPTIONS=--max_old_space_size=8192 && bun install  && bunx prisma generate && bun run build && cross-env NODE_ENV=production pm2 restart pomodoro365 && pm2 logs pomodoro365", "server": "pm2 describe pomodoro365 > /dev/null && npm run deploy || npm run begin"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.7.0", "@atlaskit/pragmatic-drag-and-drop-flourish": "^2.0.2", "@atlaskit/pragmatic-drag-and-drop-hitbox": "^1.1.0", "@atlaskit/pragmatic-drag-and-drop-live-region": "^1.3.0", "@atlaskit/pragmatic-drag-and-drop-react-drop-indicator": "^3.2.0", "@daveyplate/better-auth-ui": "^1.7.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.2", "@hono/client": "^0.0.3", "@hono/zod-validator": "^0.5.0", "@hookform/resolvers": "^5.0.1", "@polar-sh/better-auth": "^0.1.1", "@polar-sh/nextjs": "^0.4.0", "@polar-sh/sdk": "^0.32.13", "@prisma/client": "^6.9.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-context-menu": "^2.2.12", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.12", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.75.2", "better-auth": "^1.2.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.16.0", "hono": "^4.7.8", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "next": "15.3.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.56.1", "react-resizable-panels": "^2.1.8", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "tiny-invariant": "^1.3.3", "use-debounce": "^10.0.4", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.3", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "prisma": "^6.9.0", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5", "zod-prisma-types": "^3.2.4"}}